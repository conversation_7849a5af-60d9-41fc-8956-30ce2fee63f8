@echo off
REM Water Management System Startup Script for Windows
REM This script starts the integrated water management system

echo.
echo ========================================================
echo 🌊 Water Management System - Windows Startup
echo ========================================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Check if the startup script exists
if not exist "start-system.js" (
    echo ❌ start-system.js not found
    echo Please ensure you're running this from the project root directory
    echo.
    pause
    exit /b 1
)

echo ✅ Startup script found
echo.

REM Start the system
echo 🚀 Starting Water Management System...
echo.
node start-system.js

REM If we get here, the system has stopped
echo.
echo 🛑 Water Management System has stopped
echo.
pause
