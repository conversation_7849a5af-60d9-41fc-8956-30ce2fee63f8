"""Test Gemini Integration for Water Management System."""

import asyncio
import logging
import os
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_gemini_integration():
    """Test Gemini integration with water management data."""
    try:
        # Import Gemini integration
        from src.llm.gemini_integration import GeminiIntegration
        
        # Initialize with API key
        api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk')
        gemini = GeminiIntegration(api_key)
        
        print("🚀 Testing Gemini Integration for Water Management")
        print("=" * 60)
        
        # Test 1: Water System Analysis
        print("\n📊 Test 1: Water System Analysis")
        system_data = {
            'water_quality': {
                'ph': 7.2,
                'turbidity': 1.5,
                'dissolved_oxygen': 8.5,
                'chlorine_residual': 0.8
            },
            'treatment_performance': {
                'efficiency': 92.5,
                'energy_consumption': 0.45,
                'flow_rate': 1200
            },
            'climate_data': {
                'temperature': 22.5,
                'precipitation': 15.2,
                'humidity': 65
            }
        }
        
        analysis_result = await gemini.analyze_water_system(system_data)
        print(f"✅ Analysis Status: {analysis_result['status']}")
        if analysis_result['status'] == 'success':
            analysis = analysis_result['analysis']
            print(f"📈 Performance Score: {analysis.get('performance_score', 'N/A')}")
            print(f"⚠️  Risk Level: {analysis.get('risk_level', 'N/A')}")
            print(f"💡 Recommendations: {len(analysis.get('recommendations', []))} items")
        
        # Test 2: Operations Optimization
        print("\n⚙️  Test 2: Operations Optimization")
        operational_data = {
            'current_parameters': {
                'flow_rate': 1000,
                'chemical_dose': 2.5,
                'energy_consumption': 450,
                'efficiency': 88.5
            },
            'constraints': {
                'max_energy': 500,
                'min_efficiency': 90,
                'budget_limit': 10000
            }
        }
        
        optimization_result = await gemini.optimize_operations(
            operational_data, 
            objectives=['efficiency', 'cost', 'sustainability']
        )
        print(f"✅ Optimization Status: {optimization_result['status']}")
        if optimization_result['status'] == 'success':
            optimization = optimization_result['optimization']
            print(f"📋 Strategy: {optimization.get('strategy', 'N/A')[:100]}...")
            print(f"🎯 Confidence Level: {optimization.get('confidence_level', 'N/A')}")
        
        # Test 3: Maintenance Prediction
        print("\n🔧 Test 3: Maintenance Prediction")
        equipment_data = {
            'pump_1': {
                'operating_hours': 8500,
                'efficiency': 85.2,
                'vibration_level': 3.2,
                'temperature': 45.5
            },
            'filter_system': {
                'operating_hours': 6200,
                'pressure_drop': 15.8,
                'backwash_frequency': 12
            }
        }
        
        maintenance_result = await gemini.predict_maintenance(equipment_data, forecast_horizon=30)
        print(f"✅ Prediction Status: {maintenance_result['status']}")
        if maintenance_result['status'] == 'success':
            prediction = maintenance_result['prediction']
            print(f"📅 Forecast Horizon: {maintenance_result['forecast_horizon']} days")
            print(f"🔍 Prediction Summary: {prediction.get('summary', 'N/A')[:100]}...")
        
        # Test 4: Strategic Insights
        print("\n🎯 Test 4: Strategic Insights")
        comprehensive_data = {
            'system_performance': system_data,
            'operational_metrics': operational_data,
            'equipment_status': equipment_data,
            'market_conditions': {
                'energy_costs': 0.12,
                'chemical_prices': 1.25,
                'regulatory_changes': ['new_efficiency_standards', 'carbon_pricing']
            }
        }
        
        insights_result = await gemini.generate_strategic_insights(
            comprehensive_data, 
            time_horizon='medium_term'
        )
        print(f"✅ Insights Status: {insights_result['status']}")
        if insights_result['status'] == 'success':
            insights = insights_result['insights']
            print(f"🔮 Time Horizon: {insights_result['time_horizon']}")
            print(f"💡 Strategic Summary: {insights.get('summary', 'N/A')[:100]}...")
        
        # Test 5: Solution Comparison
        print("\n⚖️  Test 5: Solution Comparison")
        solution_a = {
            'name': 'Advanced Filtration System',
            'cost': 150000,
            'efficiency_improvement': 8.5,
            'energy_reduction': 12.0,
            'implementation_time': 6
        }
        
        solution_b = {
            'name': 'Smart Control System',
            'cost': 85000,
            'efficiency_improvement': 6.2,
            'energy_reduction': 15.5,
            'implementation_time': 3
        }
        
        comparison_result = await gemini.compare_solutions(
            solution_a, 
            solution_b,
            criteria=['cost_effectiveness', 'efficiency', 'implementation_speed', 'energy_savings']
        )
        print(f"✅ Comparison Status: {comparison_result['status']}")
        if comparison_result['status'] == 'success':
            comparison = comparison_result['comparison']
            print(f"🏆 Recommendation: {comparison.get('recommendation', 'N/A')}")
            print(f"📊 Criteria Used: {len(comparison_result['criteria'])} factors")
        
        # Test 6: Embeddings Creation
        print("\n🧠 Test 6: Embeddings Creation")
        texts = [
            "Water treatment efficiency optimization",
            "Climate change adaptation strategies",
            "Energy consumption reduction methods",
            "Predictive maintenance protocols"
        ]
        
        embeddings_result = await gemini.create_embeddings(texts)
        print(f"✅ Embeddings Status: {embeddings_result['status']}")
        if embeddings_result['status'] == 'success':
            embeddings = embeddings_result['embeddings']
            print(f"📊 Embeddings Created: {len(embeddings)} vectors")
            print(f"📏 Vector Dimension: {len(embeddings[0]) if embeddings else 0}")
        
        print("\n" + "=" * 60)
        print("🎉 Gemini Integration Test Completed Successfully!")
        print(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        logger.error(f"Gemini integration test failed: {e}")
        print(f"\n❌ Test Failed: {e}")
        return False


async def test_langchain_gemini():
    """Test LangChain integration with Gemini backend."""
    try:
        print("\n🔗 Testing LangChain with Gemini Backend")
        print("=" * 50)
        
        from src.llm.langchain_integration import LangChainIntegration
        
        # Initialize with Gemini API key
        api_key = os.getenv('GEMINI_API_KEY', 'AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk')
        langchain = LangChainIntegration(api_key)
        
        # Initialize chains
        init_result = await langchain.initialize_chains()
        print(f"✅ Chain Initialization: {init_result['status']}")
        
        # Test water quality analysis
        water_data = {
            'ph': 6.8,
            'turbidity': 2.1,
            'chlorine': 0.6,
            'bacteria_count': 0
        }
        
        quality_result = await langchain.analyze_water_quality(water_data)
        print(f"✅ Quality Analysis: {quality_result['status']}")
        
        # Test treatment optimization
        system_data = {
            'flow_rate': 950,
            'chemical_dose': 2.3,
            'energy_consumption': 420
        }
        
        optimization_result = await langchain.optimize_treatment(
            system_data, 
            ['efficiency', 'cost']
        )
        print(f"✅ Treatment Optimization: {optimization_result['status']}")
        
        print("🎉 LangChain-Gemini Integration Test Completed!")
        return True
        
    except Exception as e:
        logger.error(f"LangChain-Gemini test failed: {e}")
        print(f"❌ LangChain Test Failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🧪 Starting Comprehensive Gemini Integration Tests")
    print("=" * 70)
    
    # Test direct Gemini integration
    gemini_success = await test_gemini_integration()
    
    # Test LangChain with Gemini backend
    langchain_success = await test_langchain_gemini()
    
    print("\n" + "=" * 70)
    print("📋 FINAL TEST RESULTS")
    print("=" * 70)
    print(f"🔹 Direct Gemini Integration: {'✅ PASSED' if gemini_success else '❌ FAILED'}")
    print(f"🔹 LangChain-Gemini Integration: {'✅ PASSED' if langchain_success else '❌ FAILED'}")
    
    overall_success = gemini_success and langchain_success
    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🚀 Gemini integration is ready for water management operations!")
        print("💡 The system can now use Google's Gemini AI for:")
        print("   • Water system analysis and optimization")
        print("   • Predictive maintenance recommendations")
        print("   • Strategic planning and insights")
        print("   • Solution comparison and evaluation")
        print("   • Advanced reasoning and decision support")
    
    return overall_success


if __name__ == "__main__":
    asyncio.run(main())
