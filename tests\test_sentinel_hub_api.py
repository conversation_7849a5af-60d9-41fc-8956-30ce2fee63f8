#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test suite for Sentinel Hub API integration
"""

import pytest
import asyncio
import os
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.marine_conservation.apis.sentinel_hub_api import (
    SentinelHubAPI,
    BoundingBox,
    SatelliteImageRequest,
    MarineDebrisDetection,
    detect_marine_debris_area
)


class TestSentinelHubAPI:
    """Test cases for Sentinel Hub API integration"""
    
    @pytest.fixture
    def api_client(self):
        """Create test API client"""
        return SentinelHubAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
    
    @pytest.fixture
    def test_bbox(self):
        """Create test bounding box"""
        return BoundingBox(
            min_lon=2.0,
            min_lat=41.0,
            max_lon=3.0,
            max_lat=42.0
        )
    
    @pytest.fixture
    def test_image_request(self, test_bbox):
        """Create test image request"""
        return SatelliteImageRequest(
            bbox=test_bbox,
            time_range=("2024-01-01T00:00:00Z", "2024-01-07T23:59:59Z"),
            width=512,
            height=512
        )
    
    def test_bounding_box_creation(self, test_bbox):
        """Test bounding box creation and conversion"""
        assert test_bbox.min_lon == 2.0
        assert test_bbox.min_lat == 41.0
        assert test_bbox.max_lon == 3.0
        assert test_bbox.max_lat == 42.0
        
        bbox_string = test_bbox.to_bbox_string()
        assert bbox_string == "2.0,41.0,3.0,42.0"
    
    def test_satellite_image_request_creation(self, test_image_request):
        """Test satellite image request creation"""
        assert test_image_request.width == 512
        assert test_image_request.height == 512
        assert test_image_request.max_cloud_coverage == 0.3
        assert test_image_request.data_collection == "SENTINEL2_L2A"
    
    def test_marine_debris_detection_creation(self):
        """Test marine debris detection object creation"""
        detection = MarineDebrisDetection(
            location=(41.5, 2.5),
            confidence=0.85,
            debris_type="plastic",
            size_estimate=25.5,
            timestamp=datetime.now()
        )
        
        assert detection.location == (41.5, 2.5)
        assert detection.confidence == 0.85
        assert detection.debris_type == "plastic"
        assert detection.size_estimate == 25.5
        assert isinstance(detection.timestamp, datetime)
    
    @pytest.mark.asyncio
    async def test_authentication_success(self, api_client):
        """Test successful OAuth2 authentication"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "access_token": "test_token",
            "expires_in": 3600
        }
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.authenticate()
            
            assert result is True
            assert api_client.access_token == "test_token"
            assert api_client.token_expires_at is not None
    
    @pytest.mark.asyncio
    async def test_authentication_failure(self, api_client):
        """Test failed OAuth2 authentication"""
        mock_response = AsyncMock()
        mock_response.status = 401
        mock_response.text.return_value = "Unauthorized"
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.authenticate()
            
            assert result is False
            assert api_client.access_token is None
    
    @pytest.mark.asyncio
    async def test_get_satellite_image_success(self, api_client, test_image_request):
        """Test successful satellite image retrieval"""
        # Mock authentication
        api_client.access_token = "test_token"
        api_client.token_expires_at = datetime.now().replace(year=2025)
        
        # Mock image response
        mock_image_data = b"fake_image_data"
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = mock_image_data
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.get_satellite_image(test_image_request)
            
            assert result == mock_image_data
    
    @pytest.mark.asyncio
    async def test_get_satellite_image_failure(self, api_client, test_image_request):
        """Test failed satellite image retrieval"""
        # Mock authentication
        api_client.access_token = "test_token"
        api_client.token_expires_at = datetime.now().replace(year=2025)
        
        # Mock failed response
        mock_response = AsyncMock()
        mock_response.status = 400
        mock_response.text.return_value = "Bad Request"
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.get_satellite_image(test_image_request)
            
            assert result is None
    
    def test_evalscript_generation(self, api_client):
        """Test marine debris detection evalscript generation"""
        evalscript = api_client._get_marine_debris_evalscript()
        
        assert "VERSION=3" in evalscript
        assert "evaluatePixel" in evalscript
        assert "ndvi" in evalscript
        assert "ndwi" in evalscript
        assert "plastic_index" in evalscript
        assert "debris_score" in evalscript
    
    def test_pixel_size_calculation(self, api_client, test_bbox):
        """Test pixel size calculation"""
        pixel_size = api_client._calculate_pixel_size(test_bbox, 1000, 1000)
        
        assert isinstance(pixel_size, float)
        assert pixel_size > 0
        # Should be roughly 100-150 meters per pixel for this bbox
        assert 50 < pixel_size < 200
    
    @pytest.mark.asyncio
    async def test_detect_marine_debris(self, api_client, test_bbox):
        """Test marine debris detection workflow"""
        # Mock the entire detection process
        mock_detections = [
            MarineDebrisDetection(
                location=(41.5, 2.5),
                confidence=0.85,
                debris_type="unknown",
                size_estimate=25.0,
                timestamp=datetime.now()
            )
        ]
        
        with patch.object(api_client, 'get_satellite_image') as mock_get_image:
            with patch.object(api_client, '_process_debris_image') as mock_process:
                mock_get_image.return_value = b"fake_image_data"
                mock_process.return_value = mock_detections
                
                result = await api_client.detect_marine_debris(test_bbox, days_back=7)
                
                assert len(result) == 1
                assert result[0].confidence == 0.85
                assert result[0].location == (41.5, 2.5)
    
    @pytest.mark.asyncio
    async def test_detect_marine_debris_no_image(self, api_client, test_bbox):
        """Test marine debris detection when no image is available"""
        with patch.object(api_client, 'get_satellite_image') as mock_get_image:
            mock_get_image.return_value = None
            
            result = await api_client.detect_marine_debris(test_bbox, days_back=7)
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_get_available_dates_success(self, api_client, test_bbox):
        """Test successful retrieval of available dates"""
        # Mock authentication
        api_client.access_token = "test_token"
        api_client.token_expires_at = datetime.now().replace(year=2025)
        
        mock_catalog_response = {
            "features": [
                {"properties": {"datetime": "2024-01-01T10:00:00Z"}},
                {"properties": {"datetime": "2024-01-03T10:00:00Z"}},
                {"properties": {"datetime": "2024-01-05T10:00:00Z"}}
            ]
        }
        
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_catalog_response
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.get_available_dates(test_bbox, days_back=30)
            
            assert len(result) == 3
            assert "2024-01-01T10:00:00Z" in result
            assert "2024-01-03T10:00:00Z" in result
            assert "2024-01-05T10:00:00Z" in result
    
    @pytest.mark.asyncio
    async def test_get_available_dates_failure(self, api_client, test_bbox):
        """Test failed retrieval of available dates"""
        # Mock authentication
        api_client.access_token = "test_token"
        api_client.token_expires_at = datetime.now().replace(year=2025)
        
        mock_response = AsyncMock()
        mock_response.status = 400
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.get_available_dates(test_bbox, days_back=30)
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_convenience_function(self, test_bbox):
        """Test convenience function for debris detection"""
        mock_detections = [
            MarineDebrisDetection(
                location=(41.5, 2.5),
                confidence=0.85,
                debris_type="unknown",
                size_estimate=25.0,
                timestamp=datetime.now()
            )
        ]
        
        with patch('src.marine_conservation.apis.sentinel_hub_api.SentinelHubAPI') as mock_api_class:
            mock_api_instance = AsyncMock()
            mock_api_instance.detect_marine_debris.return_value = mock_detections
            mock_api_class.return_value.__aenter__.return_value = mock_api_instance
            
            result = await detect_marine_debris_area(
                test_bbox,
                client_id="test_id",
                client_secret="test_secret",
                days_back=7
            )
            
            assert len(result) == 1
            assert result[0].confidence == 0.85


class TestImageProcessing:
    """Test cases for image processing functionality"""
    
    def test_simple_debris_detection(self):
        """Test simple debris detection algorithm"""
        import numpy as np
        
        # Create mock API client
        api_client = SentinelHubAPI("test_id", "test_secret")
        
        # Create test image (100x100x3 RGB)
        test_image = np.random.rand(100, 100, 3) * 255
        test_image = test_image.astype(np.uint8)
        
        # Add some bright spots (potential debris)
        test_image[20:25, 20:25, :] = 255  # Bright white spot
        test_image[70:75, 70:75, :] = 200  # Another bright spot
        
        result = api_client._simple_debris_detection(test_image)
        
        assert result.shape == (100, 100)
        assert 0 <= result.min() <= result.max() <= 1
        
        # Bright spots should have higher confidence
        assert result[22, 22] > result[50, 50]  # Center of bright spot vs background


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
