"""
Test script to verify Gemini API integration and basic functionality.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

import google.generativeai as genai
from src.utils.config import get_settings
from src.agents.climate_agent import ClimateAnalysisAgent


async def test_gemini_api():
    """Test basic Gemini API functionality."""
    print("🧪 Testing Gemini API Integration...")
    
    try:
        # Get settings
        settings = get_settings()
        
        if not settings.GOOGLE_API_KEY:
            print("❌ Google API key not found in environment")
            return False
        
        print(f"✅ Google API key found: {settings.GOOGLE_API_KEY[:10]}...")
        
        # Configure Gemini
        genai.configure(api_key=settings.GOOGLE_API_KEY)
        
        # Test basic generation
        model = genai.GenerativeModel('gemini-pro')
        
        prompt = """
        You are an AI assistant for a water management decarbonisation system.
        
        Analyze this sample climate data and provide insights:
        - Temperature: 25°C (average), ranging from 18°C to 32°C
        - Precipitation: 45mm total over 7 days
        - Humidity: 65% average
        - Location: Urban water treatment facility
        
        Provide 3 key insights for water treatment optimization.
        """
        
        print("🔄 Generating response with Gemini...")
        response = model.generate_content(prompt)
        
        print("✅ Gemini API test successful!")
        print("\n📊 Sample Analysis Response:")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini API test failed: {str(e)}")
        return False


async def test_climate_agent():
    """Test the Climate Analysis Agent."""
    print("\n🤖 Testing Climate Analysis Agent...")
    
    try:
        # Initialize agent
        agent = ClimateAnalysisAgent()
        await agent.initialize()
        
        print("✅ Climate Analysis Agent initialized successfully")
        
        # Test data
        test_input = {
            'analysis_type': 'temperature_trend_analysis',
            'location': 'Test City',
            'time_range': '7_days',
            'climate_data': {
                'temperatures': [22.5, 24.1, 26.3, 25.8, 23.7, 21.9, 23.4, 25.6, 27.2, 24.8],
                'precipitation': [0, 2.3, 0, 5.7, 12.1, 0, 0, 3.2, 0, 1.8],
                'humidity': [65, 68, 72, 75, 80, 63, 61, 69, 71, 67],
                'timestamps': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', 
                             '2024-01-05', '2024-01-06', '2024-01-07', '2024-01-08',
                             '2024-01-09', '2024-01-10']
            }
        }
        
        print("🔄 Running climate analysis...")
        result = await agent.execute(test_input)
        
        if result.get('success'):
            print("✅ Climate analysis completed successfully!")
            print("\n📈 Analysis Results:")
            print("-" * 50)
            
            # Display key results
            analysis_results = result.get('results', {})
            
            if 'statistics' in analysis_results:
                stats = analysis_results['statistics']
                print(f"Mean Temperature: {stats.get('mean_temperature', 'N/A'):.1f}°C")
                print(f"Temperature Range: {stats.get('temperature_range', 'N/A'):.1f}°C")
                print(f"Trend Slope: {stats.get('trend_slope', 'N/A'):.3f}")
            
            if 'insights' in analysis_results:
                insights = analysis_results['insights']
                print(f"\n💡 Insights ({len(insights)} found):")
                for i, insight in enumerate(insights, 1):
                    print(f"  {i}. {insight}")
            
            if 'gemini_insights' in analysis_results:
                print(f"\n🧠 Gemini AI Insights:")
                print(analysis_results['gemini_insights'][:200] + "..." if len(analysis_results['gemini_insights']) > 200 else analysis_results['gemini_insights'])
            
            print("-" * 50)
        else:
            print(f"❌ Climate analysis failed: {result.get('error', 'Unknown error')}")
            return False
        
        # Test extreme weather detection
        print("\n🌪️ Testing extreme weather detection...")
        extreme_weather_input = {
            'analysis_type': 'extreme_weather_detection',
            'location': 'Test City',
            'climate_data': {
                'temperatures': [22, 24, 26, 38, 25, 21, 23, 25, 27, 24],  # 38°C is extreme
                'precipitation': [0, 2, 0, 45, 12, 0, 0, 3, 0, 2],  # 45mm is heavy
                'wind_speeds': [5, 7, 6, 25, 8, 4, 6, 7, 9, 6]  # 25 m/s is high
            }
        }
        
        extreme_result = await agent.execute(extreme_weather_input)
        
        if extreme_result.get('success'):
            extreme_events = extreme_result.get('results', {}).get('extreme_events', [])
            print(f"✅ Detected {len(extreme_events)} extreme weather events")
            
            for event in extreme_events[:3]:  # Show first 3 events
                print(f"  - {event['type']}: {event['value']} (severity: {event['severity']})")
        
        await agent.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Climate Agent test failed: {str(e)}")
        return False


async def test_system_configuration():
    """Test system configuration and environment."""
    print("\n⚙️ Testing System Configuration...")
    
    try:
        settings = get_settings()
        
        print("✅ Configuration loaded successfully")
        print(f"  - App Name: {settings.APP_NAME}")
        print(f"  - Environment: {settings.APP_ENVIRONMENT}")
        print(f"  - Debug Mode: {settings.DEBUG}")
        print(f"  - Available LLM Providers: {settings.get_available_llm_providers()}")
        print(f"  - Available Climate APIs: {settings.get_available_climate_apis()}")
        
        # Test feature flags
        print(f"  - Real-time Optimization: {settings.ENABLE_REAL_TIME_OPTIMIZATION}")
        print(f"  - Climate Integration: {settings.ENABLE_CLIMATE_INTEGRATION}")
        print(f"  - Multi-agent System: {settings.ENABLE_MULTI_AGENT_SYSTEM}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Water Management Decarbonisation System Tests")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: System Configuration
    config_result = await test_system_configuration()
    test_results.append(("System Configuration", config_result))
    
    # Test 2: Gemini API
    gemini_result = await test_gemini_api()
    test_results.append(("Gemini API", gemini_result))
    
    # Test 3: Climate Agent (only if Gemini works)
    if gemini_result:
        climate_result = await test_climate_agent()
        test_results.append(("Climate Analysis Agent", climate_result))
    else:
        test_results.append(("Climate Analysis Agent", False))
        print("⏭️ Skipping Climate Agent test due to Gemini API failure")
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for development.")
        print("\n🚀 Next steps:")
        print("  1. Run 'make dev' to start the development environment")
        print("  2. Access the Streamlit dashboard at http://localhost:8501")
        print("  3. Access the API documentation at http://localhost:8000/docs")
    else:
        print("⚠️ Some tests failed. Please check the configuration and API keys.")
        print("\n🔧 Troubleshooting:")
        print("  1. Verify your .env file has the correct API keys")
        print("  2. Check your internet connection")
        print("  3. Ensure all dependencies are installed: pip install -r requirements.txt")


if __name__ == "__main__":
    asyncio.run(main())
