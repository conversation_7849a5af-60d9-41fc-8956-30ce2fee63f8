# Water Management System Startup Script for PowerShell
# This script starts the integrated water management system

Write-Host ""
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host "🌊 Water Management System - PowerShell Startup" -ForegroundColor Cyan
Write-Host "========================================================" -ForegroundColor Cyan
Write-Host ""

# Function to print colored output
function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️ $Message" -ForegroundColor Blue
}

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Node.js found: $nodeVersion"
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Error "Node.js is not installed or not in PATH"
    Write-Host "Please install Node.js from https://nodejs.org/"
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check if the startup script exists
if (-not (Test-Path "start-system.js")) {
    Write-Error "start-system.js not found"
    Write-Host "Please ensure you're running this from the project root directory"
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Success "Startup script found"
Write-Host ""

# Start the system
Write-Host "🚀 Starting Water Management System..." -ForegroundColor Magenta
Write-Host ""

try {
    # Handle Ctrl+C gracefully
    [Console]::TreatControlCAsInput = $false
    
    # Start the Node.js process
    $process = Start-Process -FilePath "node" -ArgumentList "start-system.js" -NoNewWindow -PassThru
    
    # Wait for the process to complete
    $process.WaitForExit()
    
    Write-Host ""
    Write-Host "🛑 Water Management System has stopped" -ForegroundColor Yellow
    Write-Host ""
} catch {
    Write-Error "Failed to start the system: $($_.Exception.Message)"
    Write-Host ""
} finally {
    Read-Host "Press Enter to exit"
}
