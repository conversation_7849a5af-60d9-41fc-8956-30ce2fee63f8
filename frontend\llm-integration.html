<!-- LLM Integration Page -->
<div class="page" id="llm-integration-page">
    <div class="page-header">
        <h1><i class="fas fa-brain"></i> LLM Integration</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> New Chat
            </button>
            <button class="btn-secondary">
                <i class="fas fa-cogs"></i> Configure Models
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Conversations
            </button>
        </div>
    </div>

    <!-- LLM Integration Dashboard -->
    <div class="llm-integration-dashboard">
        <!-- Model Status Overview -->
        <div class="model-status-overview">
            <div class="model-card openai">
                <div class="model-header">
                    <div class="model-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="model-info">
                        <h4>OpenAI GPT-4</h4>
                        <div class="model-status active">Active</div>
                    </div>
                </div>
                <div class="model-metrics">
                    <div class="metric">
                        <span class="metric-label">Requests Today</span>
                        <span class="metric-value">1,247</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Avg Response Time</span>
                        <span class="metric-value">2.3s</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Success Rate</span>
                        <span class="metric-value">99.2%</span>
                    </div>
                </div>
            </div>

            <div class="model-card gemini">
                <div class="model-header">
                    <div class="model-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <div class="model-info">
                        <h4>Google Gemini Pro</h4>
                        <div class="model-status active">Active</div>
                    </div>
                </div>
                <div class="model-metrics">
                    <div class="metric">
                        <span class="metric-label">Requests Today</span>
                        <span class="metric-value">892</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Avg Response Time</span>
                        <span class="metric-value">1.8s</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Success Rate</span>
                        <span class="metric-value">98.7%</span>
                    </div>
                </div>
            </div>

            <div class="model-card claude">
                <div class="model-header">
                    <div class="model-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="model-info">
                        <h4>Anthropic Claude</h4>
                        <div class="model-status standby">Standby</div>
                    </div>
                </div>
                <div class="model-metrics">
                    <div class="metric">
                        <span class="metric-label">Requests Today</span>
                        <span class="metric-value">234</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Avg Response Time</span>
                        <span class="metric-value">2.1s</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Success Rate</span>
                        <span class="metric-value">97.8%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Assistant Chat Interface -->
        <div class="panel chat-interface-panel">
            <div class="panel-header">
                <h3>AI Assistant Chat</h3>
                <div class="panel-controls">
                    <select class="model-select">
                        <option value="gpt-4">GPT-4 (OpenAI)</option>
                        <option value="gemini-pro">Gemini Pro (Google)</option>
                        <option value="claude-3">Claude 3 (Anthropic)</option>
                    </select>
                    <button class="btn-icon" data-action="clear-chat">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn-icon" data-action="export-chat">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="chat-interface-content">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">AI Assistant</span>
                                <span class="message-time">14:23</span>
                                <span class="message-model">GPT-4</span>
                            </div>
                            <div class="message-text">
                                Hello! I'm your water management AI assistant. I can help you analyze data, optimize processes, troubleshoot issues, and provide insights about your water treatment systems. What would you like to know?
                            </div>
                        </div>
                    </div>

                    <div class="message user">
                        <div class="message-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">You</span>
                                <span class="message-time">14:24</span>
                            </div>
                            <div class="message-text">
                                What's the current efficiency of Treatment Plant A and how can we improve it?
                            </div>
                        </div>
                    </div>

                    <div class="message assistant">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">AI Assistant</span>
                                <span class="message-time">14:24</span>
                                <span class="message-model">GPT-4</span>
                            </div>
                            <div class="message-text">
                                Based on the current data, Treatment Plant A is operating at 94.7% efficiency. Here's my analysis:

                                **Current Performance:**
                                - Flow Rate: 2,847 L/min
                                - Energy Consumption: 847 kWh
                                - Chemical Dosing: 12.3 mg/L

                                **Optimization Opportunities:**
                                1. **Chemical Dosing**: Reduce coagulant by 15% during low turbidity periods (potential savings: $847/month)
                                2. **Energy Scheduling**: Shift high-energy processes to off-peak hours (23% energy savings)
                                3. **Filtration Optimization**: Adjust backwash frequency based on real-time turbidity data

                                Would you like me to create an optimization plan for any of these areas?
                            </div>
                            <div class="message-actions">
                                <button class="action-btn" data-action="create-plan">Create Optimization Plan</button>
                                <button class="action-btn" data-action="show-data">Show Supporting Data</button>
                                <button class="action-btn" data-action="simulate">Run Simulation</button>
                            </div>
                        </div>
                    </div>

                    <div class="message user">
                        <div class="message-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">You</span>
                                <span class="message-time">14:26</span>
                            </div>
                            <div class="message-text">
                                Create an optimization plan for chemical dosing
                            </div>
                        </div>
                    </div>

                    <div class="message assistant typing">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">AI Assistant</span>
                                <span class="message-time">14:26</span>
                                <span class="message-model">GPT-4</span>
                            </div>
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chat-input-area">
                    <div class="quick-actions">
                        <button class="quick-action" data-action="system-status">System Status</button>
                        <button class="quick-action" data-action="optimization">Optimization</button>
                        <button class="quick-action" data-action="troubleshoot">Troubleshoot</button>
                        <button class="quick-action" data-action="reports">Generate Report</button>
                    </div>
                    <div class="input-container">
                        <textarea class="chat-input" placeholder="Ask me anything about your water management system..." rows="3"></textarea>
                        <div class="input-actions">
                            <button class="btn-icon" data-action="attach-file">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="btn-icon" data-action="voice-input">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="btn-send" data-action="send-message">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversation History -->
        <div class="panel conversation-history-panel">
            <div class="panel-header">
                <h3>Conversation History</h3>
                <div class="panel-controls">
                    <input type="search" placeholder="Search conversations..." class="conversation-search">
                    <button class="btn-icon" data-action="filter-conversations">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
            <div class="conversation-history-content">
                <div class="conversation-list">
                    <div class="conversation-item active">
                        <div class="conversation-header">
                            <div class="conversation-title">Treatment Plant Optimization</div>
                            <div class="conversation-time">Today, 14:23</div>
                        </div>
                        <div class="conversation-preview">
                            What's the current efficiency of Treatment Plant A and how can we improve it?
                        </div>
                        <div class="conversation-meta">
                            <span class="message-count">4 messages</span>
                            <span class="model-used">GPT-4</span>
                        </div>
                    </div>

                    <div class="conversation-item">
                        <div class="conversation-header">
                            <div class="conversation-title">Energy Consumption Analysis</div>
                            <div class="conversation-time">Yesterday, 16:45</div>
                        </div>
                        <div class="conversation-preview">
                            Can you analyze the energy consumption patterns for the past week?
                        </div>
                        <div class="conversation-meta">
                            <span class="message-count">12 messages</span>
                            <span class="model-used">Gemini Pro</span>
                        </div>
                    </div>

                    <div class="conversation-item">
                        <div class="conversation-header">
                            <div class="conversation-title">Water Quality Troubleshooting</div>
                            <div class="conversation-time">2 days ago, 09:30</div>
                        </div>
                        <div class="conversation-preview">
                            We're seeing unusual pH readings in Sector 7. What could be causing this?
                        </div>
                        <div class="conversation-meta">
                            <span class="message-count">8 messages</span>
                            <span class="model-used">GPT-4</span>
                        </div>
                    </div>

                    <div class="conversation-item">
                        <div class="conversation-header">
                            <div class="conversation-title">Predictive Maintenance Planning</div>
                            <div class="conversation-time">1 week ago, 11:15</div>
                        </div>
                        <div class="conversation-preview">
                            Help me create a predictive maintenance schedule for our equipment.
                        </div>
                        <div class="conversation-meta">
                            <span class="message-count">15 messages</span>
                            <span class="model-used">Claude 3</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Model Configuration -->
        <div class="panel model-configuration-panel">
            <div class="panel-header">
                <h3>Model Configuration</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="test-models">
                        <i class="fas fa-vial"></i>
                    </button>
                    <button class="btn-icon" data-action="save-config">
                        <i class="fas fa-save"></i>
                    </button>
                </div>
            </div>
            <div class="model-configuration-content">
                <div class="model-configs">
                    <div class="config-section">
                        <h4>OpenAI GPT-4</h4>
                        <div class="config-form">
                            <div class="form-group">
                                <label>API Key</label>
                                <input type="password" value="sk-***...***abc" class="config-input">
                            </div>
                            <div class="form-group">
                                <label>Temperature</label>
                                <input type="range" min="0" max="2" step="0.1" value="0.7" class="config-slider">
                                <span class="slider-value">0.7</span>
                            </div>
                            <div class="form-group">
                                <label>Max Tokens</label>
                                <input type="number" value="4096" min="1" max="8192" class="config-input">
                            </div>
                            <div class="form-group">
                                <label>System Prompt</label>
                                <textarea class="config-textarea" rows="3">You are an expert water management AI assistant. Provide accurate, actionable insights based on the data and context provided.</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <h4>Google Gemini Pro</h4>
                        <div class="config-form">
                            <div class="form-group">
                                <label>API Key</label>
                                <input type="password" value="AIza***...***xyz" class="config-input">
                            </div>
                            <div class="form-group">
                                <label>Temperature</label>
                                <input type="range" min="0" max="1" step="0.1" value="0.5" class="config-slider">
                                <span class="slider-value">0.5</span>
                            </div>
                            <div class="form-group">
                                <label>Top P</label>
                                <input type="range" min="0" max="1" step="0.1" value="0.9" class="config-slider">
                                <span class="slider-value">0.9</span>
                            </div>
                            <div class="form-group">
                                <label>Safety Settings</label>
                                <select class="config-select">
                                    <option value="block_none">Block None</option>
                                    <option value="block_few" selected>Block Few</option>
                                    <option value="block_some">Block Some</option>
                                    <option value="block_most">Block Most</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="usage-analytics">
                    <h4>Usage Analytics</h4>
                    <div class="analytics-chart">
                        <canvas id="llmUsageChart"></canvas>
                    </div>
                    <div class="usage-stats">
                        <div class="usage-stat">
                            <div class="stat-label">Total Requests</div>
                            <div class="stat-value">2,373</div>
                            <div class="stat-change positive">+12.3%</div>
                        </div>
                        <div class="usage-stat">
                            <div class="stat-label">Avg Response Time</div>
                            <div class="stat-value">2.1s</div>
                            <div class="stat-change negative">-0.3s</div>
                        </div>
                        <div class="usage-stat">
                            <div class="stat-label">Cost This Month</div>
                            <div class="stat-value">$247.83</div>
                            <div class="stat-change positive">+8.7%</div>
                        </div>
                        <div class="usage-stat">
                            <div class="stat-label">Success Rate</div>
                            <div class="stat-value">98.9%</div>
                            <div class="stat-change positive">+0.2%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prompt Templates -->
        <div class="panel prompt-templates-panel">
            <div class="panel-header">
                <h3>Prompt Templates</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="create-template">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon" data-action="import-templates">
                        <i class="fas fa-upload"></i>
                    </button>
                </div>
            </div>
            <div class="prompt-templates-content">
                <div class="template-categories">
                    <div class="category-tab active" data-category="analysis">Analysis</div>
                    <div class="category-tab" data-category="optimization">Optimization</div>
                    <div class="category-tab" data-category="troubleshooting">Troubleshooting</div>
                    <div class="category-tab" data-category="reporting">Reporting</div>
                </div>

                <div class="template-list">
                    <div class="template-item">
                        <div class="template-header">
                            <div class="template-title">System Performance Analysis</div>
                            <div class="template-actions">
                                <button class="btn-template-action" data-action="use">Use</button>
                                <button class="btn-template-action" data-action="edit">Edit</button>
                                <button class="btn-template-action" data-action="delete">Delete</button>
                            </div>
                        </div>
                        <div class="template-preview">
                            Analyze the performance of {system_name} over the past {time_period}. Focus on efficiency metrics, energy consumption, and identify optimization opportunities...
                        </div>
                        <div class="template-meta">
                            <span class="template-category">Analysis</span>
                            <span class="template-usage">Used 47 times</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-header">
                            <div class="template-title">Water Quality Troubleshooting</div>
                            <div class="template-actions">
                                <button class="btn-template-action" data-action="use">Use</button>
                                <button class="btn-template-action" data-action="edit">Edit</button>
                                <button class="btn-template-action" data-action="delete">Delete</button>
                            </div>
                        </div>
                        <div class="template-preview">
                            We're experiencing {issue_description} in {location}. Current readings show {parameter_values}. Please analyze potential causes and recommend solutions...
                        </div>
                        <div class="template-meta">
                            <span class="template-category">Troubleshooting</span>
                            <span class="template-usage">Used 23 times</span>
                        </div>
                    </div>

                    <div class="template-item">
                        <div class="template-header">
                            <div class="template-title">Energy Optimization Plan</div>
                            <div class="template-actions">
                                <button class="btn-template-action" data-action="use">Use</button>
                                <button class="btn-template-action" data-action="edit">Edit</button>
                                <button class="btn-template-action" data-action="delete">Delete</button>
                            </div>
                        </div>
                        <div class="template-preview">
                            Create an energy optimization plan for {facility_name}. Current consumption is {current_usage}. Target reduction: {target_percentage}%...
                        </div>
                        <div class="template-meta">
                            <span class="template-category">Optimization</span>
                            <span class="template-usage">Used 31 times</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
