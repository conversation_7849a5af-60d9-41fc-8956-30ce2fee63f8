# Water Management System - Startup Guide

This guide explains how to start the Water Management Decarbonisation System using the provided startup scripts.

## 🚀 Quick Start

The system provides multiple startup options for different platforms:

### Windows
```bash
# Option 1: Double-click the batch file
start-system.bat

# Option 2: Run from Command Prompt
start-system.bat

# Option 3: Run from PowerShell
.\start-system.ps1
```

### Linux/Mac
```bash
# Make the script executable (first time only)
chmod +x start-system.sh

# Run the script
./start-system.sh
```

### Cross-Platform (Node.js)
```bash
# Direct Node.js execution
node start-system.js
```

## 🎯 What the Startup Script Does

1. **System Checks**: Verifies all required files and dependencies
2. **Environment Setup**: Configures API keys and environment variables
3. **Integrated Server**: Starts the combined frontend + backend on port 8000
4. **Health Monitoring**: Tests server connectivity and health
5. **Fallback Mode**: If integrated server fails, starts separate servers
6. **Graceful Shutdown**: Handles Ctrl+C and cleanup properly

## 🌐 Access Points

Once started, you can access:

- **Main Application**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health

## 📋 Prerequisites

### Required Software
- **Node.js** (v16.0.0 or higher)
- **Python** (3.9 or higher)
- **npm** (comes with Node.js)

### Required Files
- `integrated_server.py` - Main server script
- `frontend/` directory with HTML, CSS, JS files
- `requirements.txt` - Python dependencies
- `start-system.js` - Main startup script

## 🔧 Configuration

The system is pre-configured with:
- **Gemini API Key**: AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk
- **OpenWeather API Key**: ********************************
- **Port**: 8000 (integrated mode)

## 🛠️ Troubleshooting

### Common Issues

1. **"Node.js not found"**
   - Install Node.js from https://nodejs.org/
   - Restart your terminal after installation

2. **"Python not found"**
   - Install Python 3.9+ from https://python.org/
   - Ensure Python is added to PATH

3. **"Port already in use"**
   - Check if another application is using port 8000
   - Kill the process or change the port in the script

4. **"Required files missing"**
   - Ensure you're running from the project root directory
   - Check that all files are present

### Debug Mode
To see detailed logs, you can run:
```bash
node start-system.js
```

## 🔄 Server Modes

### Integrated Mode (Preferred)
- Single server on port 8000
- Frontend and backend combined
- Faster startup and better performance

### Fallback Mode
- Separate backend (port 8001) and frontend (port 3000)
- Used if integrated server fails
- Automatic fallback with no user intervention

## 🛑 Stopping the System

- Press **Ctrl+C** in the terminal
- The script will gracefully shutdown all services
- Wait for the "System stopped" message

## 📊 System Features

Once running, the system provides:
- Real-time water quality monitoring
- AI-powered climate analysis
- Predictive maintenance alerts
- Energy optimization recommendations
- Blockchain-based data integrity
- Interactive dashboards and reports

## 🔍 Health Monitoring

The system includes built-in health checks:
- Server connectivity tests
- API endpoint validation
- Service status monitoring
- Automatic error recovery

## 📝 Logs

System logs are displayed in the terminal with color coding:
- 🟢 **Green**: Success messages
- 🔴 **Red**: Error messages
- 🟡 **Yellow**: Warning messages
- 🔵 **Blue**: Information messages

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the terminal logs for error messages
3. Ensure all prerequisites are installed
4. Verify you're in the correct directory
5. Check that required ports are available
