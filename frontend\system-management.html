<!-- System Management Page -->
<div class="page" id="system-management-page">
    <div class="page-header">
        <h1><i class="fas fa-server"></i> System Management</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Add Service
            </button>
            <button class="btn-secondary">
                <i class="fas fa-sync"></i> Restart All
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> System Report
            </button>
        </div>
    </div>

    <!-- System Management Dashboard -->
    <div class="system-management-dashboard">
        <!-- System Health Overview -->
        <div class="system-health-overview">
            <div class="health-card cpu">
                <div class="health-icon">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="health-content">
                    <div class="health-value">67%</div>
                    <div class="health-label">CPU Usage</div>
                    <div class="health-trend normal">Normal</div>
                </div>
                <div class="health-chart">
                    <canvas id="cpuChart"></canvas>
                </div>
            </div>

            <div class="health-card memory">
                <div class="health-icon">
                    <i class="fas fa-memory"></i>
                </div>
                <div class="health-content">
                    <div class="health-value">4.2 GB</div>
                    <div class="health-label">Memory Usage</div>
                    <div class="health-trend normal">Normal</div>
                </div>
                <div class="health-chart">
                    <canvas id="memoryChart"></canvas>
                </div>
            </div>

            <div class="health-card storage">
                <div class="health-icon">
                    <i class="fas fa-hdd"></i>
                </div>
                <div class="health-content">
                    <div class="health-value">234 GB</div>
                    <div class="health-label">Storage Used</div>
                    <div class="health-trend warning">High</div>
                </div>
                <div class="health-chart">
                    <canvas id="storageChart"></canvas>
                </div>
            </div>

            <div class="health-card network">
                <div class="health-icon">
                    <i class="fas fa-network-wired"></i>
                </div>
                <div class="health-content">
                    <div class="health-value">847 MB/s</div>
                    <div class="health-label">Network I/O</div>
                    <div class="health-trend normal">Normal</div>
                </div>
                <div class="health-chart">
                    <canvas id="networkChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Services Status -->
        <div class="panel services-panel">
            <div class="panel-header">
                <h3>Services Status</h3>
                <div class="services-summary">
                    <span class="service-count running">12 Running</span>
                    <span class="service-count stopped">2 Stopped</span>
                    <span class="service-count error">1 Error</span>
                </div>
            </div>
            <div class="services-content">
                <div class="service-item running">
                    <div class="service-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">PostgreSQL Database</div>
                        <div class="service-description">Primary data storage</div>
                        <div class="service-status running">Running</div>
                    </div>
                    <div class="service-metrics">
                        <div class="metric">
                            <span class="metric-label">Uptime</span>
                            <span class="metric-value">15d 7h</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Connections</span>
                            <span class="metric-value">47/100</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">CPU</span>
                            <span class="metric-value">12%</span>
                        </div>
                    </div>
                    <div class="service-actions">
                        <button class="btn-service" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="btn-service" title="Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn-service" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>

                <div class="service-item running">
                    <div class="service-icon">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">Redis Cache</div>
                        <div class="service-description">In-memory data structure store</div>
                        <div class="service-status running">Running</div>
                    </div>
                    <div class="service-metrics">
                        <div class="metric">
                            <span class="metric-label">Uptime</span>
                            <span class="metric-value">15d 7h</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Memory</span>
                            <span class="metric-value">847 MB</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Keys</span>
                            <span class="metric-value">12,847</span>
                        </div>
                    </div>
                    <div class="service-actions">
                        <button class="btn-service" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="btn-service" title="Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn-service" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>

                <div class="service-item running">
                    <div class="service-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">AI Agent Coordinator</div>
                        <div class="service-description">Multi-agent coordination service</div>
                        <div class="service-status running">Running</div>
                    </div>
                    <div class="service-metrics">
                        <div class="metric">
                            <span class="metric-label">Agents</span>
                            <span class="metric-value">5 Active</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Tasks</span>
                            <span class="metric-value">23 Queued</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">CPU</span>
                            <span class="metric-value">34%</span>
                        </div>
                    </div>
                    <div class="service-actions">
                        <button class="btn-service" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="btn-service" title="Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn-service" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>

                <div class="service-item error">
                    <div class="service-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">Message Bus</div>
                        <div class="service-description">Inter-service communication</div>
                        <div class="service-status error">Error</div>
                    </div>
                    <div class="service-metrics">
                        <div class="metric">
                            <span class="metric-label">Last Error</span>
                            <span class="metric-value">5 min ago</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Restarts</span>
                            <span class="metric-value">3 today</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Status</span>
                            <span class="metric-value">Connection Failed</span>
                        </div>
                    </div>
                    <div class="service-actions">
                        <button class="btn-service" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="btn-service" title="Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn-service" title="Diagnose">
                            <i class="fas fa-stethoscope"></i>
                        </button>
                    </div>
                </div>

                <div class="service-item stopped">
                    <div class="service-icon">
                        <i class="fas fa-pause"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">Backup Service</div>
                        <div class="service-description">Automated backup and recovery</div>
                        <div class="service-status stopped">Stopped</div>
                    </div>
                    <div class="service-metrics">
                        <div class="metric">
                            <span class="metric-label">Last Backup</span>
                            <span class="metric-value">2 hours ago</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Size</span>
                            <span class="metric-value">2.4 GB</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Status</span>
                            <span class="metric-value">Manually Stopped</span>
                        </div>
                    </div>
                    <div class="service-actions">
                        <button class="btn-service" title="Start">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="btn-service" title="Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                        <button class="btn-service" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Management -->
        <div class="panel configuration-panel">
            <div class="panel-header">
                <h3>Configuration Management</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="backup-config">
                        <i class="fas fa-save"></i>
                    </button>
                    <button class="btn-icon" data-action="restore-config">
                        <i class="fas fa-upload"></i>
                    </button>
                    <button class="btn-icon" data-action="validate-config">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </div>
            <div class="configuration-content">
                <div class="config-sections">
                    <div class="config-section">
                        <div class="config-header">
                            <div class="config-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="config-info">
                                <h4>Database Configuration</h4>
                                <div class="config-status valid">Valid</div>
                            </div>
                            <div class="config-actions">
                                <button class="btn-config">Edit</button>
                                <button class="btn-config">Test</button>
                            </div>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <span class="config-key">Host</span>
                                <span class="config-value">localhost:5432</span>
                            </div>
                            <div class="config-item">
                                <span class="config-key">Database</span>
                                <span class="config-value">watermanagement</span>
                            </div>
                            <div class="config-item">
                                <span class="config-key">Pool Size</span>
                                <span class="config-value">20</span>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <div class="config-header">
                            <div class="config-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="config-info">
                                <h4>API Keys</h4>
                                <div class="config-status valid">Valid</div>
                            </div>
                            <div class="config-actions">
                                <button class="btn-config">Edit</button>
                                <button class="btn-config">Rotate</button>
                            </div>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <span class="config-key">OpenAI API</span>
                                <span class="config-value">sk-***...***abc</span>
                            </div>
                            <div class="config-item">
                                <span class="config-key">Google Gemini</span>
                                <span class="config-value">AIza***...***xyz</span>
                            </div>
                            <div class="config-item">
                                <span class="config-key">OpenWeather</span>
                                <span class="config-value">199a***...***6eb</span>
                            </div>
                        </div>
                    </div>

                    <div class="config-section">
                        <div class="config-header">
                            <div class="config-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="config-info">
                                <h4>AI Agent Settings</h4>
                                <div class="config-status warning">Needs Review</div>
                            </div>
                            <div class="config-actions">
                                <button class="btn-config">Edit</button>
                                <button class="btn-config">Reset</button>
                            </div>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <span class="config-key">Max Concurrent Tasks</span>
                                <span class="config-value">10</span>
                            </div>
                            <div class="config-item">
                                <span class="config-key">Task Timeout</span>
                                <span class="config-value">300s</span>
                            </div>
                            <div class="config-item">
                                <span class="config-key">Retry Attempts</span>
                                <span class="config-value">3</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup & Recovery -->
        <div class="panel backup-recovery-panel">
            <div class="panel-header">
                <h3>Backup & Recovery</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="create-backup">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon" data-action="schedule-backup">
                        <i class="fas fa-clock"></i>
                    </button>
                </div>
            </div>
            <div class="backup-recovery-content">
                <div class="backup-status">
                    <div class="backup-card">
                        <div class="backup-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="backup-info">
                            <div class="backup-value">2 hours ago</div>
                            <div class="backup-label">Last Backup</div>
                        </div>
                    </div>
                    <div class="backup-card">
                        <div class="backup-icon">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="backup-info">
                            <div class="backup-value">2.4 GB</div>
                            <div class="backup-label">Backup Size</div>
                        </div>
                    </div>
                    <div class="backup-card">
                        <div class="backup-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="backup-info">
                            <div class="backup-value">99.9%</div>
                            <div class="backup-label">Success Rate</div>
                        </div>
                    </div>
                </div>

                <div class="backup-history">
                    <h4>Recent Backups</h4>
                    <div class="backup-list">
                        <div class="backup-item successful">
                            <div class="backup-time">2 hours ago</div>
                            <div class="backup-details">
                                <div class="backup-name">Full System Backup</div>
                                <div class="backup-size">2.4 GB</div>
                            </div>
                            <div class="backup-status success">Success</div>
                            <div class="backup-actions">
                                <button class="btn-backup">Restore</button>
                                <button class="btn-backup">Download</button>
                            </div>
                        </div>

                        <div class="backup-item successful">
                            <div class="backup-time">1 day ago</div>
                            <div class="backup-details">
                                <div class="backup-name">Database Backup</div>
                                <div class="backup-size">1.8 GB</div>
                            </div>
                            <div class="backup-status success">Success</div>
                            <div class="backup-actions">
                                <button class="btn-backup">Restore</button>
                                <button class="btn-backup">Download</button>
                            </div>
                        </div>

                        <div class="backup-item failed">
                            <div class="backup-time">2 days ago</div>
                            <div class="backup-details">
                                <div class="backup-name">Configuration Backup</div>
                                <div class="backup-size">--</div>
                            </div>
                            <div class="backup-status failed">Failed</div>
                            <div class="backup-actions">
                                <button class="btn-backup">Retry</button>
                                <button class="btn-backup">Logs</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="backup-schedule">
                    <h4>Backup Schedule</h4>
                    <div class="schedule-list">
                        <div class="schedule-item">
                            <div class="schedule-type">Full System</div>
                            <div class="schedule-frequency">Daily at 2:00 AM</div>
                            <div class="schedule-retention">Keep 7 days</div>
                            <div class="schedule-status active">Active</div>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-type">Database</div>
                            <div class="schedule-frequency">Every 6 hours</div>
                            <div class="schedule-retention">Keep 24 hours</div>
                            <div class="schedule-status active">Active</div>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-type">Configuration</div>
                            <div class="schedule-frequency">Weekly on Sunday</div>
                            <div class="schedule-retention">Keep 4 weeks</div>
                            <div class="schedule-status paused">Paused</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Logs -->
        <div class="panel system-logs-panel">
            <div class="panel-header">
                <h3>System Logs</h3>
                <div class="panel-controls">
                    <select class="log-level-select">
                        <option value="all">All Levels</option>
                        <option value="error">Error</option>
                        <option value="warning">Warning</option>
                        <option value="info">Info</option>
                        <option value="debug">Debug</option>
                    </select>
                    <button class="btn-icon" data-action="refresh-logs">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn-icon" data-action="download-logs">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="system-logs-content">
                <div class="log-entries">
                    <div class="log-entry error">
                        <div class="log-time">2024-01-15 14:23:45</div>
                        <div class="log-level error">ERROR</div>
                        <div class="log-service">MessageBus</div>
                        <div class="log-message">Connection to Redis failed: Connection refused</div>
                    </div>
                    <div class="log-entry warning">
                        <div class="log-time">2024-01-15 14:20:12</div>
                        <div class="log-level warning">WARN</div>
                        <div class="log-service">Database</div>
                        <div class="log-message">High connection count: 89/100 connections in use</div>
                    </div>
                    <div class="log-entry info">
                        <div class="log-time">2024-01-15 14:18:33</div>
                        <div class="log-level info">INFO</div>
                        <div class="log-service">AIAgent</div>
                        <div class="log-message">Climate analysis task completed successfully</div>
                    </div>
                    <div class="log-entry info">
                        <div class="log-time">2024-01-15 14:15:07</div>
                        <div class="log-level info">INFO</div>
                        <div class="log-service">Backup</div>
                        <div class="log-message">Scheduled backup completed: 2.4 GB</div>
                    </div>
                    <div class="log-entry debug">
                        <div class="log-time">2024-01-15 14:12:54</div>
                        <div class="log-level debug">DEBUG</div>
                        <div class="log-service">Sensor</div>
                        <div class="log-message">Received data from sensor WQ-001: pH=7.2, turbidity=1.5</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
