"""
Simple test to check OpenWeatherMap API key status.
"""

import asyncio
import aiohttp
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.utils.config import get_settings


async def test_api_key_simple():
    """Simple test of the OpenWeatherMap API key."""
    print("🔑 Testing OpenWeatherMap API Key...")
    
    try:
        settings = get_settings()
        api_key = settings.OPENWEATHER_API_KEY
        
        if not api_key or api_key == "your_openweathermap_api_key_here":
            print("❌ No valid API key found")
            return False
        
        print(f"✅ API Key: {api_key[:10]}...{api_key[-4:]}")
        
        # Test with a simple request
        url = "https://api.openweathermap.org/data/2.5/weather"
        params = {
            "q": "London,UK",
            "appid": api_key,
            "units": "metric"
        }
        
        print("🔄 Making test request to OpenWeatherMap...")
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                print(f"📡 Response Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print("✅ API key is working!")
                    print(f"📍 Location: {data.get('name', 'Unknown')}, {data.get('sys', {}).get('country', 'Unknown')}")
                    print(f"🌡️ Temperature: {data.get('main', {}).get('temp', 'N/A')}°C")
                    print(f"☁️ Weather: {data.get('weather', [{}])[0].get('description', 'N/A')}")
                    return True
                    
                elif response.status == 401:
                    error_data = await response.json()
                    print("❌ API key authentication failed")
                    print(f"Error: {error_data.get('message', 'Invalid API key')}")
                    print("\n💡 Possible issues:")
                    print("  1. API key is not activated yet (can take up to 2 hours)")
                    print("  2. API key is invalid or expired")
                    print("  3. API key doesn't have permission for this endpoint")
                    return False
                    
                elif response.status == 429:
                    print("❌ Rate limit exceeded")
                    print("Too many requests. Please wait and try again.")
                    return False
                    
                else:
                    error_data = await response.text()
                    print(f"❌ API request failed with status {response.status}")
                    print(f"Error: {error_data}")
                    return False
                    
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False


async def test_api_key_activation():
    """Test if the API key is activated by checking account info."""
    print("\n🔍 Checking API Key Activation Status...")
    
    try:
        settings = get_settings()
        api_key = settings.OPENWEATHER_API_KEY
        
        # Try a simple call to check if key is active
        url = "https://api.openweathermap.org/data/2.5/weather"
        params = {
            "lat": 51.5074,
            "lon": -0.1278,
            "appid": api_key
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    print("✅ API key is fully activated and working")
                    return True
                elif response.status == 401:
                    error_data = await response.json()
                    message = error_data.get('message', '')
                    
                    if 'Invalid API key' in message:
                        print("❌ API key is invalid")
                        print("Please check that you copied the key correctly")
                    elif 'not activated' in message.lower():
                        print("⏳ API key is not yet activated")
                        print("New API keys can take up to 2 hours to activate")
                    else:
                        print(f"❌ Authentication error: {message}")
                    
                    return False
                else:
                    print(f"⚠️ Unexpected response status: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Activation check failed: {e}")
        return False


async def provide_setup_instructions():
    """Provide setup instructions for OpenWeatherMap API."""
    print("\n📋 OpenWeatherMap API Setup Instructions:")
    print("=" * 50)
    print("1. 🌐 Go to: https://openweathermap.org/api")
    print("2. 📝 Sign up for a free account")
    print("3. 🔑 Generate an API key")
    print("4. ⏳ Wait up to 2 hours for activation")
    print("5. 🔧 Add the key to your .env file")
    print("\n💡 Free Plan Includes:")
    print("  • 1,000 API calls per day")
    print("  • Current weather data")
    print("  • 5-day weather forecast")
    print("  • Historical weather data")
    print("\n🔗 Useful Links:")
    print("  • API Documentation: https://openweathermap.org/api")
    print("  • API Key Management: https://home.openweathermap.org/api_keys")


async def main():
    """Run API key tests."""
    print("🚀 OpenWeatherMap API Key Validation")
    print("=" * 40)
    
    # Test 1: Simple API key test
    simple_test = await test_api_key_simple()
    
    if not simple_test:
        # Test 2: Check activation status
        await test_api_key_activation()
        
        # Provide setup instructions
        await provide_setup_instructions()
        
        print("\n🔄 What to do next:")
        print("  1. If the API key is new, wait 1-2 hours for activation")
        print("  2. If the key is old, check it's still valid at:")
        print("     https://home.openweathermap.org/api_keys")
        print("  3. Re-run this test: python test_api_key.py")
        
    else:
        print("\n🎉 Success! Your OpenWeatherMap API is ready to use!")
        print("You can now run the full weather integration test:")
        print("  python test_openweather.py")


if __name__ == "__main__":
    asyncio.run(main())
