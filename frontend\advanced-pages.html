<!-- AI Agents Page -->
<div class="page" id="ai-agents-page">
    <div class="page-header">
        <h1><i class="fas fa-robot"></i> AI Agent Management</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Deploy Agent
            </button>
            <button class="btn-secondary">
                <i class="fas fa-brain"></i> Train Model
            </button>
            <button class="btn-secondary">
                <i class="fas fa-cogs"></i> Configure
            </button>
        </div>
    </div>

    <!-- AI Agents Dashboard -->
    <div class="ai-agents-dashboard">
        <!-- Agent Status Overview -->
        <div class="agents-overview">
            <div class="agent-card climate-agent">
                <div class="agent-header">
                    <div class="agent-icon">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <div class="agent-info">
                        <h3>Climate Analysis Agent</h3>
                        <div class="agent-status active">Active</div>
                    </div>
                    <div class="agent-actions">
                        <button class="btn-icon" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn-icon" title="View Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="agent-metrics">
                    <div class="metric">
                        <span class="metric-label">Tasks Completed</span>
                        <span class="metric-value">1,247</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Accuracy</span>
                        <span class="metric-value">94.7%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Uptime</span>
                        <span class="metric-value">99.2%</span>
                    </div>
                </div>
                <div class="agent-activity">
                    <canvas id="climateAgentActivity"></canvas>
                </div>
            </div>

            <div class="agent-card treatment-agent">
                <div class="agent-header">
                    <div class="agent-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="agent-info">
                        <h3>Treatment Optimization Agent</h3>
                        <div class="agent-status active">Active</div>
                    </div>
                    <div class="agent-actions">
                        <button class="btn-icon" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn-icon" title="View Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="agent-metrics">
                    <div class="metric">
                        <span class="metric-label">Optimizations</span>
                        <span class="metric-value">892</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Efficiency Gain</span>
                        <span class="metric-value">+12.3%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Energy Saved</span>
                        <span class="metric-value">847 kWh</span>
                    </div>
                </div>
                <div class="agent-activity">
                    <canvas id="treatmentAgentActivity"></canvas>
                </div>
            </div>

            <div class="agent-card energy-agent">
                <div class="agent-header">
                    <div class="agent-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="agent-info">
                        <h3>Energy Efficiency Agent</h3>
                        <div class="agent-status active">Active</div>
                    </div>
                    <div class="agent-actions">
                        <button class="btn-icon" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn-icon" title="View Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="agent-metrics">
                    <div class="metric">
                        <span class="metric-label">Optimizations</span>
                        <span class="metric-value">634</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Cost Savings</span>
                        <span class="metric-value">$2,847</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">CO₂ Reduced</span>
                        <span class="metric-value">1.2 tons</span>
                    </div>
                </div>
                <div class="agent-activity">
                    <canvas id="energyAgentActivity"></canvas>
                </div>
            </div>

            <div class="agent-card risk-agent">
                <div class="agent-header">
                    <div class="agent-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="agent-info">
                        <h3>Risk Analysis Agent</h3>
                        <div class="agent-status active">Active</div>
                    </div>
                    <div class="agent-actions">
                        <button class="btn-icon" title="Configure">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="btn-icon" title="View Logs">
                            <i class="fas fa-file-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="agent-metrics">
                    <div class="metric">
                        <span class="metric-label">Risk Assessments</span>
                        <span class="metric-value">423</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Threats Detected</span>
                        <span class="metric-value">17</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Prevention Rate</span>
                        <span class="metric-value">98.8%</span>
                    </div>
                </div>
                <div class="agent-activity">
                    <canvas id="riskAgentActivity"></canvas>
                </div>
            </div>
        </div>

        <!-- Agent Communication Network -->
        <div class="panel communication-panel">
            <div class="panel-header">
                <h3>Agent Communication Network</h3>
                <div class="panel-controls">
                    <button class="btn-icon active" data-view="network">
                        <i class="fas fa-network-wired"></i>
                    </button>
                    <button class="btn-icon" data-view="messages">
                        <i class="fas fa-comments"></i>
                    </button>
                    <button class="btn-icon" data-view="performance">
                        <i class="fas fa-tachometer-alt"></i>
                    </button>
                </div>
            </div>
            <div class="communication-network">
                <svg id="agentNetwork" width="100%" height="400"></svg>
            </div>
        </div>

        <!-- Task Queue and Execution -->
        <div class="panel tasks-panel">
            <div class="panel-header">
                <h3>Task Queue & Execution</h3>
                <div class="task-stats">
                    <span class="task-stat">
                        <i class="fas fa-clock"></i>
                        <span>23 Pending</span>
                    </span>
                    <span class="task-stat">
                        <i class="fas fa-play"></i>
                        <span>8 Running</span>
                    </span>
                    <span class="task-stat">
                        <i class="fas fa-check"></i>
                        <span>1,247 Completed</span>
                    </span>
                </div>
            </div>
            <div class="tasks-list">
                <div class="task-item running">
                    <div class="task-icon">
                        <i class="fas fa-cog fa-spin"></i>
                    </div>
                    <div class="task-content">
                        <div class="task-title">Climate Data Analysis - Sector 7</div>
                        <div class="task-description">Analyzing temperature and precipitation patterns</div>
                        <div class="task-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 67%"></div>
                            </div>
                            <span class="progress-text">67% Complete</span>
                        </div>
                    </div>
                    <div class="task-meta">
                        <span class="task-agent">Climate Agent</span>
                        <span class="task-time">Started 5 min ago</span>
                    </div>
                </div>

                <div class="task-item pending">
                    <div class="task-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="task-content">
                        <div class="task-title">Treatment Process Optimization</div>
                        <div class="task-description">Optimize chemical dosing for improved efficiency</div>
                    </div>
                    <div class="task-meta">
                        <span class="task-agent">Treatment Agent</span>
                        <span class="task-time">Queued 2 min ago</span>
                    </div>
                </div>

                <div class="task-item completed">
                    <div class="task-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="task-content">
                        <div class="task-title">Energy Consumption Analysis</div>
                        <div class="task-description">Daily energy usage pattern analysis completed</div>
                    </div>
                    <div class="task-meta">
                        <span class="task-agent">Energy Agent</span>
                        <span class="task-time">Completed 15 min ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Climate Impact Page -->
<div class="page" id="climate-impact-page">
    <div class="page-header">
        <h1><i class="fas fa-cloud-sun"></i> Climate Impact Analysis</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> New Analysis
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Report
            </button>
            <button class="btn-secondary">
                <i class="fas fa-globe"></i> Global View
            </button>
        </div>
    </div>

    <!-- Climate Dashboard -->
    <div class="climate-dashboard">
        <!-- Climate Metrics -->
        <div class="climate-metrics">
            <div class="climate-card temperature">
                <div class="climate-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="climate-content">
                    <div class="climate-value">+1.5°C</div>
                    <div class="climate-label">Temperature Rise</div>
                    <div class="climate-trend critical">Above 1.5°C target</div>
                </div>
                <div class="climate-chart">
                    <canvas id="temperatureRiseChart"></canvas>
                </div>
            </div>

            <div class="climate-card co2">
                <div class="climate-icon">
                    <i class="fas fa-smog"></i>
                </div>
                <div class="climate-content">
                    <div class="climate-value">421 ppm</div>
                    <div class="climate-label">CO₂ Concentration</div>
                    <div class="climate-trend warning">Increasing</div>
                </div>
                <div class="climate-chart">
                    <canvas id="co2Chart"></canvas>
                </div>
            </div>

            <div class="climate-card renewable">
                <div class="climate-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="climate-content">
                    <div class="climate-value">29.0%</div>
                    <div class="climate-label">Renewable Energy</div>
                    <div class="climate-trend positive">Growing</div>
                </div>
                <div class="climate-chart">
                    <canvas id="renewableChart"></canvas>
                </div>
            </div>

            <div class="climate-card sea-level">
                <div class="climate-icon">
                    <i class="fas fa-water"></i>
                </div>
                <div class="climate-content">
                    <div class="climate-value">22.7 cm</div>
                    <div class="climate-label">Sea Level Rise</div>
                    <div class="climate-trend warning">Accelerating</div>
                </div>
                <div class="climate-chart">
                    <canvas id="seaLevelChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Global Climate Map -->
        <div class="panel climate-map-panel">
            <div class="panel-header">
                <h3>Global Climate Impact Map</h3>
                <div class="panel-controls">
                    <select class="climate-layer-select">
                        <option value="temperature">Temperature Anomaly</option>
                        <option value="precipitation">Precipitation Change</option>
                        <option value="extreme">Extreme Weather Events</option>
                        <option value="risk">Climate Risk Index</option>
                    </select>
                    <button class="btn-icon" data-action="fullscreen">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            <div class="climate-map" id="globalClimateMap"></div>
        </div>

        <!-- Climate Projections -->
        <div class="panel projections-panel">
            <div class="panel-header">
                <h3>Climate Projections</h3>
                <div class="panel-controls">
                    <select class="scenario-select">
                        <option value="rcp26">RCP 2.6 (Best Case)</option>
                        <option value="rcp45">RCP 4.5 (Moderate)</option>
                        <option value="rcp85">RCP 8.5 (Worst Case)</option>
                    </select>
                </div>
            </div>
            <div class="projections-chart">
                <canvas id="climateProjectionsChart"></canvas>
            </div>
        </div>
    </div>
</div>
