#!/usr/bin/env python3
"""
Simple HTTP server to serve the water management dashboard frontend
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Configuration
PORT = 8080
DIRECTORY = Path(__file__).parent

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    """Start the development server"""
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🌊 Water Management Dashboard Server")
            print(f"📍 Serving at: http://localhost:{PORT}")
            print(f"📁 Directory: {DIRECTORY}")
            print(f"🚀 Opening browser...")
            
            # Open browser automatically
            webbrowser.open(f'http://localhost:{PORT}')
            
            print(f"✅ Server running. Press Ctrl+C to stop.")
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n🛑 Server stopped.")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use. Try a different port.")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
