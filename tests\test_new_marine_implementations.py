#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Suite for New Marine Conservation Implementations
Testing the latest 4 implemented components
"""

import asyncio
import pytest
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import new marine conservation components
from marine_conservation.agents.sustainability_marine_agent import SustainabilityMarineAgent
from marine_conservation.recycling.ai_recycling_optimizer import AIRecyclingOptimizer
from marine_conservation.agents.risk_analysis_marine_agent import RiskAnalysisMarineAgent
from marine_conservation.ml_models.debris_categorization import MLDebrisCategorizer


class TestNewMarineImplementations:
    """Test suite for the latest marine conservation implementations"""
    
    @pytest.fixture
    def test_area_bbox(self):
        """Test area: Mediterranean Sea near Barcelona"""
        return (2.0, 41.0, 3.0, 42.0)
    
    @pytest.fixture
    def taiwan_test_area(self):
        """Test area: Taiwan Strait"""
        return (119.0, 23.0, 121.0, 25.0)
    
    @pytest.mark.asyncio
    async def test_sustainability_marine_agent(self, test_area_bbox):
        """Test Sustainability Marine Agent functionality"""
        print("\n🌱 Testing Sustainability Marine Agent")
        
        try:
            agent = SustainabilityMarineAgent()
            
            # Test ecosystem assessment
            assessment = await agent.assess_marine_ecosystem(test_area_bbox, assessment_depth="comprehensive")
            
            assert hasattr(assessment, 'assessment_id'), "Assessment should have ID"
            assert hasattr(assessment, 'overall_health_score'), "Should have health score"
            assert hasattr(assessment, 'ecosystem_metrics'), "Should have ecosystem metrics"
            assert hasattr(assessment, 'sustainability_recommendations'), "Should have recommendations"
            assert isinstance(assessment.ecosystem_metrics, list), "Ecosystem metrics should be list"
            
            # Validate ecosystem metrics
            if assessment.ecosystem_metrics:
                metric = assessment.ecosystem_metrics[0]
                assert hasattr(metric, 'biodiversity_index'), "Should have biodiversity index"
                assert hasattr(metric, 'water_quality_score'), "Should have water quality score"
                assert hasattr(metric, 'conservation_priority'), "Should have conservation priority"
                assert 0 <= metric.biodiversity_index <= 1, "Biodiversity index should be 0-1"
                assert 0 <= metric.water_quality_score <= 1, "Water quality should be 0-1"
            
            # Test sustainability recommendations
            assert isinstance(assessment.sustainability_recommendations, list), "Recommendations should be list"
            
            print(f"   ✅ Sustainability agent test passed")
            print(f"      Health Score: {assessment.overall_health_score:.2f}")
            print(f"      Ecosystem Points: {len(assessment.ecosystem_metrics)}")
            print(f"      Recommendations: {len(assessment.sustainability_recommendations)}")
            print(f"      Biodiversity Hotspots: {len(assessment.biodiversity_hotspots)}")
            return True
            
        except Exception as e:
            print(f"   ❌ Sustainability agent test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_ai_recycling_optimizer(self, test_area_bbox):
        """Test AI Recycling Optimizer functionality"""
        print("\n♻️ Testing AI Recycling Optimizer")
        
        try:
            optimizer = AIRecyclingOptimizer()
            categorizer = MLDebrisCategorizer()
            
            # First get some debris data to optimize
            debris_classifications = await categorizer.classify_debris_in_area(test_area_bbox)
            
            if not debris_classifications:
                # Create mock debris data for testing
                from marine_conservation.ml_models.debris_categorization import DebrisClassificationResult, DebrisCategory
                
                mock_category = DebrisCategory(
                    category_id="test_cat_1",
                    primary_type="plastic",
                    sub_type="bottle",
                    confidence=0.9,
                    size_category="medium",
                    degradation_state="weathered",
                    environmental_impact="high",
                    recyclability="recyclable",
                    source_probability={"shipping": 0.6, "tourism": 0.4}
                )
                
                mock_debris = DebrisClassificationResult(
                    classification_id="test_class_1",
                    location=(41.5, 2.5),
                    bounding_box=(100, 100, 50, 50),
                    categories=[mock_category],
                    primary_category=mock_category,
                    detection_confidence=0.9,
                    image_quality_score=0.8,
                    processing_method="spectral_analysis",
                    timestamp=datetime.now(),
                    metadata={"size_estimate_m2": 2.0}
                )
                
                debris_classifications = [mock_debris] * 5  # Create 5 mock debris items
            
            # Test recycling optimization
            optimization_result = await optimizer.optimize_recycling_pathways(
                debris_classifications,
                optimization_objectives=['maximize_revenue', 'minimize_environmental_impact'],
                budget_constraint=50000.0
            )
            
            assert hasattr(optimization_result, 'optimization_id'), "Should have optimization ID"
            assert hasattr(optimization_result, 'recommended_pathways'), "Should have recommended pathways"
            assert hasattr(optimization_result, 'economic_analysis'), "Should have economic analysis"
            assert hasattr(optimization_result, 'environmental_impact'), "Should have environmental impact"
            assert isinstance(optimization_result.recommended_pathways, list), "Pathways should be list"
            
            print(f"   ✅ Recycling optimizer test passed")
            print(f"      Input Debris: {len(optimization_result.input_debris_data)}")
            print(f"      Recommended Pathways: {len(optimization_result.recommended_pathways)}")
            print(f"      Market Opportunities: {len(optimization_result.market_opportunities)}")
            return True
            
        except Exception as e:
            print(f"   ❌ Recycling optimizer test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_risk_analysis_marine_agent(self, test_area_bbox):
        """Test Risk Analysis Marine Agent functionality"""
        print("\n⚠️ Testing Risk Analysis Marine Agent")
        
        try:
            agent = RiskAnalysisMarineAgent()
            
            # Test risk assessment
            risk_assessment = await agent.assess_marine_conservation_risks(
                test_area_bbox,
                assessment_depth="comprehensive",
                prediction_horizon_months=12
            )
            
            assert hasattr(risk_assessment, 'assessment_id'), "Assessment should have ID"
            assert hasattr(risk_assessment, 'overall_risk_score'), "Should have overall risk score"
            assert hasattr(risk_assessment, 'risk_level'), "Should have risk level"
            assert hasattr(risk_assessment, 'risk_factors'), "Should have risk factors"
            assert hasattr(risk_assessment, 'predictive_scenarios'), "Should have predictive scenarios"
            assert isinstance(risk_assessment.risk_factors, list), "Risk factors should be list"
            assert isinstance(risk_assessment.predictive_scenarios, list), "Scenarios should be list"
            assert 0 <= risk_assessment.overall_risk_score <= 1, "Risk score should be 0-1"
            assert risk_assessment.risk_level in ['low', 'medium', 'high', 'critical', 'unknown'], "Valid risk level"
            
            # Validate risk factors
            if risk_assessment.risk_factors:
                risk_factor = risk_assessment.risk_factors[0]
                assert hasattr(risk_factor, 'factor_name'), "Risk factor should have name"
                assert hasattr(risk_factor, 'risk_category'), "Should have category"
                assert hasattr(risk_factor, 'current_level'), "Should have current level"
                assert hasattr(risk_factor, 'mitigation_strategies'), "Should have mitigation strategies"
                assert 0 <= risk_factor.current_level <= 1, "Risk level should be 0-1"
            
            print(f"   ✅ Risk analysis agent test passed")
            print(f"      Overall Risk Score: {risk_assessment.overall_risk_score:.2f}")
            print(f"      Risk Level: {risk_assessment.risk_level}")
            print(f"      Risk Factors: {len(risk_assessment.risk_factors)}")
            print(f"      Predictive Scenarios: {len(risk_assessment.predictive_scenarios)}")
            print(f"      Early Warning Indicators: {len(risk_assessment.early_warning_indicators)}")
            return True
            
        except Exception as e:
            print(f"   ❌ Risk analysis agent test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_ml_debris_categorization(self, test_area_bbox):
        """Test ML Debris Categorization functionality"""
        print("\n🔍 Testing ML Debris Categorization")
        
        try:
            categorizer = MLDebrisCategorizer()
            
            # Test debris classification
            classifications = await categorizer.classify_debris_in_area(
                test_area_bbox,
                classification_depth="comprehensive"
            )
            
            assert isinstance(classifications, list), "Classifications should be list"
            
            # If we have classifications, validate them
            if classifications:
                classification = classifications[0]
                assert hasattr(classification, 'classification_id'), "Should have classification ID"
                assert hasattr(classification, 'primary_category'), "Should have primary category"
                assert hasattr(classification, 'detection_confidence'), "Should have confidence"
                assert hasattr(classification, 'processing_method'), "Should have processing method"
                
                # Validate primary category
                category = classification.primary_category
                assert hasattr(category, 'primary_type'), "Category should have primary type"
                assert hasattr(category, 'confidence'), "Category should have confidence"
                assert hasattr(category, 'environmental_impact'), "Should have environmental impact"
                assert hasattr(category, 'recyclability'), "Should have recyclability"
                assert 0 <= category.confidence <= 1, "Confidence should be 0-1"
            
            # Test category statistics generation
            if classifications:
                # Create mock statistics
                total_classified = len(classifications)
                category_distribution = {}
                for c in classifications:
                    cat_type = c.primary_category.primary_type
                    category_distribution[cat_type] = category_distribution.get(cat_type, 0) + 1
                
                print(f"      Total Classified: {total_classified}")
                print(f"      Category Distribution: {category_distribution}")
            
            print(f"   ✅ ML debris categorization test passed")
            print(f"      Classifications: {len(classifications)}")
            return True
            
        except Exception as e:
            print(f"   ❌ ML debris categorization test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_integrated_new_workflow(self, test_area_bbox):
        """Test integrated workflow with all new components"""
        print("\n🔄 Testing Integrated New Components Workflow")
        
        try:
            # Step 1: Sustainability assessment
            sustainability_agent = SustainabilityMarineAgent()
            ecosystem_assessment = await sustainability_agent.assess_marine_ecosystem(test_area_bbox)
            
            # Step 2: Risk analysis
            risk_agent = RiskAnalysisMarineAgent()
            risk_assessment = await risk_agent.assess_marine_conservation_risks(test_area_bbox)
            
            # Step 3: Debris categorization
            categorizer = MLDebrisCategorizer()
            debris_classifications = await categorizer.classify_debris_in_area(test_area_bbox)
            
            # Step 4: Recycling optimization (if we have debris)
            if debris_classifications:
                recycling_optimizer = AIRecyclingOptimizer()
                recycling_optimization = await recycling_optimizer.optimize_recycling_pathways(debris_classifications)
            else:
                recycling_optimization = None
            
            # Validate integration
            assert hasattr(ecosystem_assessment, 'overall_health_score'), "Ecosystem assessment should work"
            assert hasattr(risk_assessment, 'overall_risk_score'), "Risk assessment should work"
            assert isinstance(debris_classifications, list), "Debris classification should work"
            
            # Create integrated summary
            integrated_summary = {
                'ecosystem_health': ecosystem_assessment.overall_health_score,
                'risk_level': risk_assessment.risk_level,
                'risk_score': risk_assessment.overall_risk_score,
                'debris_classified': len(debris_classifications),
                'recycling_pathways': len(recycling_optimization.recommended_pathways) if recycling_optimization else 0,
                'sustainability_recommendations': len(ecosystem_assessment.sustainability_recommendations),
                'risk_mitigation_strategies': len(risk_assessment.mitigation_recommendations)
            }
            
            print("   ✅ Integrated new components workflow test passed")
            print(f"      Ecosystem Health: {integrated_summary['ecosystem_health']:.2f}")
            print(f"      Risk Level: {integrated_summary['risk_level']} ({integrated_summary['risk_score']:.2f})")
            print(f"      Debris Classified: {integrated_summary['debris_classified']}")
            print(f"      Recycling Pathways: {integrated_summary['recycling_pathways']}")
            print(f"      Sustainability Recommendations: {integrated_summary['sustainability_recommendations']}")
            print(f"      Risk Mitigation Strategies: {integrated_summary['risk_mitigation_strategies']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Integrated new components workflow test failed: {e}")
            return False


async def run_new_implementations_tests():
    """Run all new marine conservation implementation tests"""
    print("🧪 Starting New Marine Conservation Implementation Tests")
    print("=" * 70)
    
    test_suite = TestNewMarineImplementations()
    test_area = (2.0, 41.0, 3.0, 42.0)  # Mediterranean Sea
    
    results = {}
    
    # Run individual component tests
    results['sustainability_agent'] = await test_suite.test_sustainability_marine_agent(test_area)
    results['recycling_optimizer'] = await test_suite.test_ai_recycling_optimizer(test_area)
    results['risk_analysis_agent'] = await test_suite.test_risk_analysis_marine_agent(test_area)
    results['ml_categorization'] = await test_suite.test_ml_debris_categorization(test_area)
    results['integrated_workflow'] = await test_suite.test_integrated_new_workflow(test_area)
    
    # Summary
    print("\n" + "=" * 70)
    print("🏆 NEW IMPLEMENTATIONS TEST RESULTS")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nNew Components: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL NEW MARINE CONSERVATION COMPONENTS WORKING CORRECTLY!")
        print("🚀 Enhanced platform ready for advanced deployment")
    else:
        print(f"\n⚠️  {total - passed} new component tests failed - review implementation")
    
    return results


if __name__ == "__main__":
    # Run tests
    results = asyncio.run(run_new_implementations_tests())
    
    # Exit with appropriate code
    if all(results.values()):
        print("\n✅ All new implementation tests passed!")
        exit(0)
    else:
        print("\n❌ Some new implementation tests failed")
        exit(1)
