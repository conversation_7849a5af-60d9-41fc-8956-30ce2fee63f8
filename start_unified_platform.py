#!/usr/bin/env python3
"""
Unified Environmental Platform Startup Script
Starts both the backend API and frontend development server
"""

import asyncio
import subprocess
import sys
import os
import time
import signal
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnifiedPlatformLauncher:
    def __init__(self):
        self.processes = []
        self.running = True
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info("🛑 Shutdown signal received, stopping all processes...")
        self.running = False
        self.stop_all_processes()
        sys.exit(0)
    
    def stop_all_processes(self):
        """Stop all running processes"""
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            except Exception as e:
                logger.error(f"Error stopping process: {e}")
    
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        logger.info("🔍 Checking dependencies...")
        
        # Check Python dependencies
        try:
            import fastapi
            import uvicorn
            logger.info("✅ Python dependencies found")
        except ImportError as e:
            logger.error(f"❌ Missing Python dependency: {e}")
            logger.info("💡 Install with: pip install -r requirements.txt")
            return False
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Node.js found: {result.stdout.strip()}")
            else:
                logger.error("❌ Node.js not found")
                return False
        except FileNotFoundError:
            logger.error("❌ Node.js not found")
            logger.info("💡 Install Node.js from: https://nodejs.org/")
            return False
        
        # Check if frontend dependencies are installed
        frontend_path = Path("frontend")
        if not (frontend_path / "node_modules").exists():
            logger.info("📦 Installing frontend dependencies...")
            try:
                subprocess.run(['npm', 'install'], cwd=frontend_path, check=True)
                logger.info("✅ Frontend dependencies installed")
            except subprocess.CalledProcessError:
                logger.error("❌ Failed to install frontend dependencies")
                return False
        
        return True
    
    def start_backend(self):
        """Start the FastAPI backend server"""
        logger.info("🚀 Starting backend server...")
        
        try:
            # Start the unified API server
            process = subprocess.Popen([
                sys.executable, '-m', 'uvicorn',
                'src.api.unified_api:app',
                '--host', '0.0.0.0',
                '--port', '8000',
                '--reload'
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            
            self.processes.append(process)
            logger.info("✅ Backend server started on http://localhost:8000")
            return process
            
        except Exception as e:
            logger.error(f"❌ Failed to start backend server: {e}")
            return None
    
    def start_frontend(self):
        """Start the React frontend development server"""
        logger.info("🎨 Starting frontend server...")
        
        frontend_path = Path("frontend")
        if not frontend_path.exists():
            logger.error("❌ Frontend directory not found")
            return None
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['REACT_APP_API_URL'] = 'http://localhost:8000'
            env['REACT_APP_WS_URL'] = 'ws://localhost:8000'
            env['BROWSER'] = 'none'  # Don't auto-open browser
            
            process = subprocess.Popen([
                'npm', 'start'
            ], cwd=frontend_path, env=env, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            
            self.processes.append(process)
            logger.info("✅ Frontend server started on http://localhost:3000")
            return process
            
        except Exception as e:
            logger.error(f"❌ Failed to start frontend server: {e}")
            return None
    
    def wait_for_backend(self, timeout=30):
        """Wait for backend to be ready"""
        import requests
        
        logger.info("⏳ Waiting for backend to be ready...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get('http://localhost:8000/health', timeout=5)
                if response.status_code == 200:
                    logger.info("✅ Backend is ready!")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(2)
        
        logger.error("❌ Backend failed to start within timeout")
        return False
    
    def monitor_processes(self):
        """Monitor running processes and restart if needed"""
        while self.running:
            for i, process in enumerate(self.processes):
                if process.poll() is not None:
                    logger.warning(f"⚠️ Process {i} has stopped")
                    # In a production environment, you might want to restart the process
            
            time.sleep(5)
    
    def run(self):
        """Main run method"""
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        logger.info("🌊💧 Starting Unified Environmental Platform")
        logger.info("=" * 60)
        
        # Check dependencies
        if not self.check_dependencies():
            logger.error("❌ Dependency check failed")
            return 1
        
        # Start backend
        backend_process = self.start_backend()
        if not backend_process:
            logger.error("❌ Failed to start backend")
            return 1
        
        # Wait for backend to be ready
        if not self.wait_for_backend():
            logger.error("❌ Backend not ready")
            self.stop_all_processes()
            return 1
        
        # Start frontend
        frontend_process = self.start_frontend()
        if not frontend_process:
            logger.error("❌ Failed to start frontend")
            self.stop_all_processes()
            return 1
        
        # Wait a bit for frontend to start
        time.sleep(5)
        
        logger.info("🎉 Unified Environmental Platform is running!")
        logger.info("📊 Frontend: http://localhost:3000")
        logger.info("🔌 Backend API: http://localhost:8000")
        logger.info("📚 API Docs: http://localhost:8000/docs")
        logger.info("🔧 Health Check: http://localhost:8000/health")
        logger.info("")
        logger.info("Press Ctrl+C to stop all services")
        
        try:
            # Monitor processes
            self.monitor_processes()
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested")
        finally:
            self.stop_all_processes()
        
        return 0

def main():
    """Main entry point"""
    launcher = UnifiedPlatformLauncher()
    return launcher.run()

if __name__ == "__main__":
    sys.exit(main())
