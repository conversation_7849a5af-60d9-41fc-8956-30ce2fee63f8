/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #3d6cb9 100%);
    color: #ffffff;
    overflow-x: hidden;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
    color: #4ade80;
}

.logo span {
    font-size: 1.2rem;
    font-weight: 600;
}

.logo small {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-left: 0.5rem;
}

.nav-tabs {
    display: flex;
    gap: 1rem;
}

.nav-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.nav-tab:hover,
.nav-tab.active {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.status-indicators {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.9rem;
}

.status-online {
    color: #4ade80;
}

.uptime {
    color: #4ade80;
    font-weight: 500;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu i {
    font-size: 1.1rem;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.user-menu i:hover {
    opacity: 1;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4ade80;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #1e3c72;
    cursor: pointer;
}

.btn-test {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin-right: 8px;
}

.btn-test:hover {
    color: #4ade80;
    background: rgba(74, 222, 128, 0.1);
}

/* Main Content */
.main-content {
    margin-left: 0;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

.system-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.status-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.last-update {
    opacity: 0.7;
    font-size: 0.9rem;
}

.btn-help {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-help:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Chart Section */
.chart-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-btn:hover,
.chart-btn.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.main-chart {
    height: 300px;
    position: relative;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #4ade80;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.stat-trend {
    font-size: 0.8rem;
    font-weight: 500;
}

.stat-trend.positive {
    color: #4ade80;
}

.stat-trend.negative {
    color: #f87171;
}

/* Bottom Grid */
.bottom-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 2rem;
}

.panel h3 {
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

/* Performance Panel */
.performance-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.perf-label {
    flex: 1;
    font-size: 0.9rem;
}

.perf-bar {
    flex: 2;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.perf-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ade80, #22c55e);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.perf-value {
    font-weight: 600;
    color: #4ade80;
}

/* Alerts Panel */
.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
}

.alert-warning {
    color: #fbbf24;
}

.alert-info {
    color: #60a5fa;
}

.alert-success {
    color: #4ade80;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.alert-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
}

.risk-level {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.risk-level.high {
    background: #dc2626;
    color: #ffffff;
}

.climate-indicators {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 2rem 0;
}

.indicator {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
}

.indicator-value {
    font-weight: 600;
    color: #4ade80;
}

.climate-impacts h4 {
    margin-bottom: 1rem;
}

.impact-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 3D Visualization Styles */
.climate-visualization {
    position: relative;
    height: 400px;
    background: radial-gradient(circle at center, rgba(74, 222, 128, 0.2), rgba(30, 60, 114, 0.8));
    border-radius: 1rem;
    overflow: hidden;
    margin: 2rem 0;
}

.heatmap-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(239, 68, 68, 0.6) 0%, transparent 30%),
        radial-gradient(circle at 60% 20%, rgba(251, 191, 36, 0.5) 0%, transparent 25%),
        radial-gradient(circle at 80% 60%, rgba(239, 68, 68, 0.7) 0%, transparent 35%),
        radial-gradient(circle at 30% 80%, rgba(34, 197, 94, 0.4) 0%, transparent 40%);
    animation: heatmapPulse 4s ease-in-out infinite;
}

@keyframes heatmapPulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.visualization-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.visualization-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.visualization-subtitle {
    opacity: 0.8;
    margin-bottom: 1rem;
}

.heatmap-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.heatmap-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* System Status Indicators */
.system-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.status-indicator {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
}

.status-indicator.online {
    border-left: 4px solid #4ade80;
}

.status-indicator.healthy {
    border-left: 4px solid #22c55e;
}

.status-indicator.optimal {
    border-left: 4px solid #06b6d4;
}

.status-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #4ade80;
}

.status-description {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

/* Page Management */
.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #4ade80, #22c55e);
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.status-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-settings, .btn-fullscreen {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-settings:hover, .btn-fullscreen:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Water Quality Page Styles */
.water-quality-dashboard {
    display: grid;
    gap: 2rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.metric-card .metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.ph-card .metric-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.turbidity-card .metric-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.chlorine-card .metric-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.bacteria-card .metric-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.metric-content {
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.metric-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.metric-status.normal {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.metric-status.good {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.metric-status.excellent {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
}

.metric-chart {
    height: 60px;
    margin-top: 1rem;
}

/* Panel Styles */
.panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.panel-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.time-range-select, .climate-layer-select, .scenario-select, .variable-select, .prediction-type {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-radius: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-icon:hover, .btn-icon.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Trends Panel */
.trends-chart, .energy-flow-chart, .correlation-chart, .predictive-chart {
    height: 300px;
    position: relative;
}

/* Map Panel */
.sensor-map, .sensor-network-map, .climate-map {
    height: 400px;
    border-radius: 0.5rem;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.2);
    position: relative;
}

/* Alert Styles */
.alert-summary {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.alert-count {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.alert-count.critical {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.alert-count.warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.alert-count.info {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    border-left: 4px solid transparent;
}

.alert-item.critical {
    border-left-color: #ef4444;
}

.alert-item.warning {
    border-left-color: #fbbf24;
}

.alert-item.info {
    border-left-color: #3b82f6;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.alert-item.critical .alert-icon {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.alert-item.warning .alert-icon {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.alert-item.info .alert-icon {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.alert-description {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.alert-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    opacity: 0.7;
}

.alert-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-alert-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-alert-action:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }

    .nav-tabs {
        display: none;
    }

    .bottom-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .climate-indicators {
        grid-template-columns: 1fr;
    }

    .system-status-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* Energy Grid Styles */
.energy-dashboard {
    display: grid;
    gap: 2rem;
}

.energy-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.energy-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.energy-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.energy-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.energy-card.consumption .energy-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.energy-card.generation .energy-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.energy-card.efficiency .energy-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.energy-card.cost .energy-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.energy-content {
    flex: 1;
}

.energy-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.energy-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.energy-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.energy-change.positive {
    color: #22c55e;
}

.energy-change.negative {
    color: #ef4444;
}

.energy-gauge {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
}

/* Grid Topology */
.grid-visualization {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0.5rem;
    overflow: hidden;
}

#gridTopology {
    background: transparent;
}

/* AI Agents Styles */
.ai-agents-dashboard {
    display: grid;
    gap: 2rem;
}

.agents-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
}

.agent-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.agent-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.agent-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.agent-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.climate-agent .agent-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.treatment-agent .agent-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.energy-agent .agent-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.risk-agent .agent-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.agent-info {
    flex: 1;
}

.agent-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.agent-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.agent-status.active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.agent-actions {
    display: flex;
    gap: 0.5rem;
}

.agent-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric {
    text-align: center;
}

.metric-label {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.metric-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #4ade80;
}

.agent-activity {
    height: 80px;
}

/* Communication Network */
.communication-network {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0.5rem;
    overflow: hidden;
    height: 400px;
}

#agentNetwork {
    background: transparent;
}

/* Task Management */
.task-stats {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.task-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.tasks-list {
    max-height: 400px;
    overflow-y: auto;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid transparent;
}

.task-item.running {
    border-left-color: #3b82f6;
}

.task-item.pending {
    border-left-color: #f59e0b;
}

.task-item.completed {
    border-left-color: #22c55e;
}

.task-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.task-item.running .task-icon {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.task-item.pending .task-icon {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.task-item.completed .task-icon {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.task-content {
    flex: 1;
}

.task-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.task-description {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    font-weight: 500;
    color: #3b82f6;
}

.task-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.8rem;
    opacity: 0.7;
    text-align: right;
}

.task-agent {
    font-weight: 500;
}

/* Climate Impact Styles */
.climate-dashboard {
    display: grid;
    gap: 2rem;
}

.climate-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.climate-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.climate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.climate-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.climate-card.temperature .climate-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.climate-card.co2 .climate-icon {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

.climate-card.renewable .climate-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.climate-card.sea-level .climate-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.climate-content {
    flex: 1;
}

.climate-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.climate-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.climate-trend {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    display: inline-block;
}

.climate-trend.critical {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.climate-trend.warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.climate-trend.positive {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.climate-chart {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
}

/* Global Climate Map */
.climate-map-panel {
    grid-column: 1 / -1;
}

#globalClimateMap {
    background: radial-gradient(circle at center, rgba(74, 222, 128, 0.1), rgba(30, 60, 114, 0.8));
    position: relative;
}

/* Climate Projections */
.projections-chart {
    height: 350px;
    position: relative;
}

/* Sensors Page Styles */
.sensors-dashboard {
    display: grid;
    gap: 2rem;
}

.sensor-overview {
    margin-bottom: 2rem;
}

.sensor-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    backdrop-filter: blur(10px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon.online {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.stat-icon.offline {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.stat-icon.maintenance {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.stat-icon.data {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Sensor Types Grid */
.sensor-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.sensor-type-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.sensor-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.sensor-type-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.sensor-type-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.sensor-type-card.water-quality .sensor-type-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.sensor-type-card.flow-pressure .sensor-type-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.sensor-type-card.energy .sensor-type-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.sensor-type-card.environmental .sensor-type-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.sensor-type-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.sensor-count {
    font-size: 0.9rem;
    opacity: 0.8;
}

.sensor-type-metrics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.sensor-type-status {
    margin-top: 1rem;
}

.status-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    margin-bottom: 0.5rem;
}

.status-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.status-fill.online {
    background: linear-gradient(90deg, #22c55e, #16a34a);
}

.status-fill.offline {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.status-fill.maintenance {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.status-legend {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.legend-item.online {
    color: #22c55e;
}

.legend-item.offline {
    color: #ef4444;
}

.legend-item.maintenance {
    color: #f59e0b;
}

/* Sensor Network Map */
.sensor-map-panel {
    grid-column: 1 / -1;
}

.map-filters {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
    background: rgba(255, 255, 255, 0.2);
}

/* Data Streams */
.stream-controls {
    display: flex;
    gap: 0.5rem;
}

.data-streams {
    max-height: 400px;
    overflow-y: auto;
}

.stream-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.stream-sensor {
    flex-shrink: 0;
    width: 120px;
}

.sensor-id {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.sensor-location {
    font-size: 0.8rem;
    opacity: 0.8;
}

.stream-data {
    flex: 1;
    display: flex;
    gap: 2rem;
}

.data-point {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.data-label {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.data-value {
    font-weight: 600;
    color: #4ade80;
}

.stream-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.stream-status.online {
    color: #22c55e;
}

.stream-status.offline {
    color: #ef4444;
}

/* Analytics Page Styles */
.analytics-dashboard {
    display: grid;
    gap: 2rem;
}

.kpi-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.kpi-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.kpi-card.efficiency .kpi-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.kpi-card.cost-savings .kpi-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.kpi-card.carbon-reduction .kpi-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.kpi-card.water-saved .kpi-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.kpi-content {
    flex: 1;
}

.kpi-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.kpi-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.kpi-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.kpi-change.positive {
    color: #22c55e;
}

.kpi-change.negative {
    color: #ef4444;
}

.kpi-sparkline {
    width: 80px;
    height: 40px;
    flex-shrink: 0;
}

/* Advanced Charts */
.advanced-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* ML Insights */
.ml-model-status {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.model-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
}

.model-status.active {
    color: #22c55e;
}

.ml-insights {
    max-height: 400px;
    overflow-y: auto;
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #4ade80;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(74, 222, 128, 0.2);
    color: #4ade80;
    flex-shrink: 0;
}

.insight-content {
    flex: 1;
}

.insight-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.insight-description {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.insight-impact {
    display: flex;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.impact-label {
    opacity: 0.8;
}

.impact-value {
    font-weight: 600;
    color: #4ade80;
}

.insight-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.btn-insight {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-insight:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Additional responsive styles */
@media (max-width: 1024px) {
    .advanced-charts {
        grid-template-columns: 1fr;
    }

    .sensor-types-grid {
        grid-template-columns: 1fr;
    }

    .agents-overview {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .stream-data {
        flex-direction: column;
        gap: 1rem;
    }

    .sensor-type-metrics {
        grid-template-columns: 1fr;
    }

    .agent-metrics {
        grid-template-columns: 1fr;
    }
}

/* Notifications */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.notification-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

/* Interactive feedback */
.btn-primary:active,
.btn-secondary:active,
.btn-icon:active {
    transform: scale(0.95);
}

.btn-primary:hover,
.btn-secondary:hover,
.btn-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dropdown feedback */
select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    border-color: #3b82f6;
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced responsive design for notifications */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
