#!/usr/bin/env python3
"""Simple System Test for Water Management Decarbonisation System."""

import os
import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleSystemTester:
    """Simple system tester to validate core functionality."""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def test_file_structure(self):
        """Test that all required files exist."""
        logger.info("🔍 Testing file structure...")
        
        required_files = [
            # Phase 1: Infrastructure
            'src/database/connection.py',
            'src/cache/redis_manager.py',
            'src/data/pipeline.py',
            'src/data/climate_data.py',
            'src/monitoring/data_quality.py',
            'src/backup/backup_manager.py',
            'src/api/gateway.py',
            'src/monitoring/system_monitor.py',
            
            # Phase 2: LLM & AI
            'src/llm/gemini_integration.py',
            'src/llm/langchain_integration.py',
            'src/ai/climate_analysis_agent.py',
            'src/ai/treatment_optimization_agent.py',
            'src/ai/predictive_maintenance_agent.py',
            'src/ai/energy_efficiency_agent.py',
            'src/ai/water_quality_agent.py',
            'src/ai/multi_agent_coordination.py',
            'src/llm/advanced_reasoning.py',
            'src/llm/conversation_memory.py',
            
            # Phase 3: ML & Optimization
            'src/ml/deep_neural_networks.py',
            'src/ml/ensemble_models.py',
            'src/ml/time_series_models.py',
            'src/ml/reinforcement_learning.py',
            'src/ml/federated_learning.py',
            'src/ml/transfer_learning.py',
            'src/ml/automl_pipeline.py',
            'src/ml/model_interpretability.py',
            'src/optimization/genetic_algorithm.py',
            'src/ml/hyperparameter_optimization.py',
            
            # Phase 4: Integration
            'src/web/dashboard.py',
            'src/api/main.py',
            'src/websocket/manager.py',
            'src/notifications/manager.py',
            'src/reports/generator.py',
            'src/auth/user_manager.py',
            
            # Phase 5: Research
            'src/digital_twin/water_system_twin.py',
            'src/blockchain/water_blockchain.py',
            'src/iot/sensor_manager.py',
            'src/analytics/advanced_analytics.py',
            'src/ai/innovation_agent.py',
            'src/sustainability/metrics_calculator.py'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path):
                existing_files.append(file_path)
                logger.info(f"  ✅ {file_path}")
            else:
                missing_files.append(file_path)
                logger.warning(f"  ❌ {file_path}")
        
        self.total_tests += len(required_files)
        self.passed_tests += len(existing_files)
        self.failed_tests += len(missing_files)
        
        self.test_results['file_structure'] = {
            'total_files': len(required_files),
            'existing_files': len(existing_files),
            'missing_files': len(missing_files),
            'completion_rate': len(existing_files) / len(required_files) * 100
        }
        
        logger.info(f"📊 File Structure: {len(existing_files)}/{len(required_files)} files exist ({len(existing_files)/len(required_files)*100:.1f}%)")
        
        return len(missing_files) == 0
    
    def test_module_imports(self):
        """Test that modules can be imported without errors."""
        logger.info("🔍 Testing module imports...")
        
        test_modules = [
            ('src.database.connection', 'DatabaseManager'),
            ('src.cache.redis_manager', 'RedisManager'),
            ('src.data.pipeline', 'DataPipeline'),
            ('src.data.climate_data', 'ClimateDataCollector'),
            ('src.llm.gemini_integration', 'GeminiLLM'),
            ('src.ai.climate_analysis_agent', 'ClimateAnalysisAgent'),
            ('src.ai.treatment_optimization_agent', 'TreatmentOptimizationAgent'),
            ('src.ml.deep_neural_networks', 'WaterQualityDNN'),
            ('src.ml.reinforcement_learning', 'ReinforcementLearningSystem'),
            ('src.optimization.genetic_algorithm', 'WaterManagementGA')
        ]
        
        successful_imports = 0
        failed_imports = 0
        
        # Add src to path
        sys.path.insert(0, 'src')
        
        for module_name, class_name in test_modules:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                logger.info(f"  ✅ {module_name}.{class_name}")
                successful_imports += 1
            except Exception as e:
                logger.warning(f"  ❌ {module_name}.{class_name}: {e}")
                failed_imports += 1
        
        self.total_tests += len(test_modules)
        self.passed_tests += successful_imports
        self.failed_tests += failed_imports
        
        self.test_results['module_imports'] = {
            'total_modules': len(test_modules),
            'successful_imports': successful_imports,
            'failed_imports': failed_imports,
            'success_rate': successful_imports / len(test_modules) * 100
        }
        
        logger.info(f"📊 Module Imports: {successful_imports}/{len(test_modules)} successful ({successful_imports/len(test_modules)*100:.1f}%)")
        
        return failed_imports == 0
    
    def test_configuration_files(self):
        """Test configuration files exist."""
        logger.info("🔍 Testing configuration files...")
        
        config_files = [
            'docker-compose.yml',
            'requirements.txt',
            'README.md',
            '.env.example'
        ]
        
        existing_configs = 0
        
        for config_file in config_files:
            if os.path.exists(config_file):
                logger.info(f"  ✅ {config_file}")
                existing_configs += 1
            else:
                logger.warning(f"  ❌ {config_file}")
        
        self.total_tests += len(config_files)
        self.passed_tests += existing_configs
        self.failed_tests += (len(config_files) - existing_configs)
        
        self.test_results['configuration_files'] = {
            'total_configs': len(config_files),
            'existing_configs': existing_configs,
            'completion_rate': existing_configs / len(config_files) * 100
        }
        
        logger.info(f"📊 Configuration Files: {existing_configs}/{len(config_files)} exist ({existing_configs/len(config_files)*100:.1f}%)")
        
        return existing_configs == len(config_files)
    
    def test_directory_structure(self):
        """Test directory structure."""
        logger.info("🔍 Testing directory structure...")
        
        required_dirs = [
            'src',
            'src/ai',
            'src/api',
            'src/auth',
            'src/backup',
            'src/blockchain',
            'src/cache',
            'src/data',
            'src/database',
            'src/digital_twin',
            'src/iot',
            'src/llm',
            'src/ml',
            'src/monitoring',
            'src/notifications',
            'src/optimization',
            'src/reports',
            'src/sustainability',
            'src/utils',
            'src/web',
            'src/websocket',
            'tests'
        ]
        
        existing_dirs = 0
        
        for directory in required_dirs:
            if os.path.isdir(directory):
                logger.info(f"  ✅ {directory}/")
                existing_dirs += 1
            else:
                logger.warning(f"  ❌ {directory}/")
        
        self.total_tests += len(required_dirs)
        self.passed_tests += existing_dirs
        self.failed_tests += (len(required_dirs) - existing_dirs)
        
        self.test_results['directory_structure'] = {
            'total_dirs': len(required_dirs),
            'existing_dirs': existing_dirs,
            'completion_rate': existing_dirs / len(required_dirs) * 100
        }
        
        logger.info(f"📊 Directory Structure: {existing_dirs}/{len(required_dirs)} directories exist ({existing_dirs/len(required_dirs)*100:.1f}%)")
        
        return existing_dirs == len(required_dirs)
    
    def run_all_tests(self):
        """Run all tests."""
        logger.info("🚀 STARTING SIMPLE SYSTEM TESTING")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # Run tests
        tests = [
            ("Directory Structure", self.test_directory_structure),
            ("File Structure", self.test_file_structure),
            ("Configuration Files", self.test_configuration_files),
            ("Module Imports", self.test_module_imports)
        ]
        
        all_passed = True
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 {test_name}")
            logger.info("-" * 40)
            
            try:
                result = test_func()
                if not result:
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ {test_name} failed with error: {e}")
                all_passed = False
        
        # Generate summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        logger.info("\n" + "=" * 60)
        logger.info("🎯 SIMPLE SYSTEM TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"📊 Total Tests: {self.total_tests}")
        logger.info(f"✅ Passed: {self.passed_tests}")
        logger.info(f"❌ Failed: {self.failed_tests}")
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        logger.info(f"⏱️ Duration: {duration:.2f} seconds")
        
        if success_rate >= 95:
            logger.info("🎉 EXCELLENT! System structure is complete!")
        elif success_rate >= 85:
            logger.info("✅ GOOD! System structure is mostly complete.")
        elif success_rate >= 70:
            logger.info("⚠️ FAIR! Some components are missing.")
        else:
            logger.info("❌ POOR! Significant components are missing.")
        
        logger.info("=" * 60)
        
        return all_passed and success_rate >= 80


def main():
    """Main test execution."""
    print("🚀 WATER MANAGEMENT SYSTEM - SIMPLE VALIDATION TEST")
    print("=" * 60)
    
    tester = SimpleSystemTester()
    success = tester.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
