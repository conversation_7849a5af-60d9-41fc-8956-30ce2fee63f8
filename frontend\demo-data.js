// Demo Data for Water Management Dashboard
const DemoData = {
    // Water Quality Data
    waterQuality: {
        current: {
            ph: 7.2,
            turbidity: 1.5,
            chlorine: 1.0,
            bacteria: 0,
            temperature: 23.4,
            conductivity: 450,
            dissolvedOxygen: 8.2
        },
        trends: {
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
            ph: [7.1, 7.0, 7.2, 7.3, 7.1, 7.2, 7.2],
            turbidity: [1.2, 1.4, 1.6, 1.8, 1.5, 1.3, 1.5],
            chlorine: [0.9, 1.1, 1.0, 0.8, 1.2, 1.0, 1.0],
            bacteria: [0, 0, 0, 1, 0, 0, 0]
        }
    },

    // Energy Data
    energy: {
        consumption: {
            current: 2847,
            daily: [2200, 2400, 2600, 2800, 2900, 2700, 2500],
            weekly: [18500, 19200, 17800, 20100, 19600, 18900, 19400]
        },
        generation: {
            renewable: 1923,
            solar: 1200,
            wind: 723,
            efficiency: 87.3
        },
        cost: {
            daily: 342.18,
            monthly: 9847.32,
            savings: 2847.19
        }
    },

    // Sensor Network Data
    sensors: {
        total: 1247,
        online: 1224,
        offline: 23,
        maintenance: 8,
        types: {
            waterQuality: 342,
            flowPressure: 198,
            energy: 156,
            environmental: 89,
            security: 67,
            weather: 45
        },
        locations: [
            { id: 'WQ-001', type: 'water-quality', lat: 40.7128, lon: -74.0060, status: 'online' },
            { id: 'WQ-002', type: 'water-quality', lat: 40.7589, lon: -73.9851, status: 'online' },
            { id: 'EN-045', type: 'energy', lat: 40.7505, lon: -73.9934, status: 'online' },
            { id: 'FL-023', type: 'flow-pressure', lat: 40.7282, lon: -74.0776, status: 'offline' }
        ]
    },

    // AI Agents Data
    aiAgents: {
        climate: {
            status: 'active',
            tasksCompleted: 1247,
            accuracy: 94.7,
            uptime: 99.2,
            currentTask: 'Climate Data Analysis - Sector 7'
        },
        treatment: {
            status: 'active',
            optimizations: 892,
            efficiencyGain: 12.3,
            energySaved: 847,
            currentTask: 'Treatment Process Optimization'
        },
        energy: {
            status: 'active',
            optimizations: 634,
            costSavings: 2847,
            co2Reduced: 1.2,
            currentTask: 'Energy Consumption Analysis'
        },
        risk: {
            status: 'active',
            assessments: 423,
            threatsDetected: 17,
            preventionRate: 98.8,
            currentTask: 'Risk Assessment - Infrastructure'
        }
    },

    // Climate Data
    climate: {
        temperature: {
            current: 23.4,
            rise: 1.5,
            trend: 'increasing',
            projections: [1.2, 1.5, 1.8, 2.1, 2.4, 2.7, 3.0]
        },
        co2: {
            current: 421,
            trend: 'increasing',
            historical: [315, 350, 380, 400, 415, 421]
        },
        renewable: {
            percentage: 29.0,
            trend: 'increasing',
            target: 50.0
        },
        seaLevel: {
            rise: 22.7,
            trend: 'accelerating',
            projections: [20, 25, 30, 35, 40, 45, 50]
        }
    },

    // Performance Metrics
    performance: {
        waterQualityIndex: 94,
        treatmentEfficiency: 87,
        energyOptimization: 92,
        systemReliability: 98,
        overallEfficiency: 94.7
    },

    // Alerts Data
    alerts: [
        {
            id: 'alert_001',
            type: 'critical',
            title: 'pH Level Critical - Sector 7',
            description: 'pH level dropped to 5.8, immediate attention required',
            timestamp: new Date(Date.now() - 3 * 60 * 1000), // 3 minutes ago
            sensor: 'WQ-007',
            acknowledged: false
        },
        {
            id: 'alert_002',
            type: 'warning',
            title: 'Turbidity Elevated - Sector 3',
            description: 'Turbidity reading 3.2 NTU, above normal threshold',
            timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
            sensor: 'WQ-003',
            acknowledged: false
        },
        {
            id: 'alert_003',
            type: 'info',
            title: 'Routine Calibration Complete',
            description: 'Sensor WQ-012 calibration completed successfully',
            timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
            sensor: 'WQ-012',
            acknowledged: true
        },
        {
            id: 'alert_004',
            type: 'warning',
            title: 'High Energy Consumption - Pump Station 3',
            description: 'Energy usage 15% above normal levels',
            timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
            sensor: 'EN-045',
            acknowledged: false
        }
    ],

    // Analytics Data
    analytics: {
        kpis: {
            efficiency: {
                value: 94.7,
                change: 2.3,
                trend: 'positive'
            },
            costSavings: {
                value: 47832,
                change: 12.4,
                trend: 'positive'
            },
            carbonReduction: {
                value: 23.4,
                change: 8.7,
                trend: 'positive'
            },
            waterSaved: {
                value: 1200000,
                change: 5.2,
                trend: 'positive'
            }
        },
        predictions: {
            demand: [2200, 2400, 2600, 2800, 2900, 2700, 2500],
            maintenance: ['Pump Station 3', 'Treatment Plant A', 'Sensor WQ-045'],
            quality: [94, 95, 93, 96, 94, 95, 97]
        },
        insights: [
            {
                type: 'optimization',
                title: 'Optimization Opportunity Detected',
                description: 'Pump Station 3 shows 15% efficiency improvement potential during off-peak hours',
                impact: '$2,847/month',
                confidence: 92
            },
            {
                type: 'anomaly',
                title: 'Anomaly Pattern Identified',
                description: 'Unusual water quality fluctuations in Sector 7 may indicate sensor drift',
                impact: 'Quality Risk',
                confidence: 87
            }
        ]
    },

    // System Status
    systemStatus: {
        overall: 'operational',
        uptime: 99.9,
        lastUpdate: new Date(),
        services: {
            dataStreams: 'online',
            monitoring: 'optimal',
            apiServices: 'healthy',
            backup: 'active'
        }
    },

    // Grid Topology Data
    gridTopology: {
        nodes: [
            { id: 'main', type: 'source', x: 100, y: 200, status: 'active' },
            { id: 'pump1', type: 'pump', x: 200, y: 150, status: 'active' },
            { id: 'pump2', type: 'pump', x: 200, y: 250, status: 'active' },
            { id: 'treatment', type: 'treatment', x: 300, y: 200, status: 'active' },
            { id: 'distribution', type: 'distribution', x: 400, y: 200, status: 'active' }
        ],
        connections: [
            { from: 'main', to: 'pump1', flow: 150 },
            { from: 'main', to: 'pump2', flow: 120 },
            { from: 'pump1', to: 'treatment', flow: 140 },
            { from: 'pump2', to: 'treatment', flow: 110 },
            { from: 'treatment', to: 'distribution', flow: 240 }
        ]
    }
};

// Utility functions for demo data
const DemoUtils = {
    // Generate random variation for real-time updates
    addVariation: (value, percentage = 5) => {
        const variation = (Math.random() - 0.5) * 2 * (percentage / 100);
        return value * (1 + variation);
    },

    // Format timestamp for display
    formatTimestamp: (date) => {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        
        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        return date.toLocaleDateString();
    },

    // Update real-time data
    updateRealTimeData: () => {
        // Update water quality
        DemoData.waterQuality.current.ph = DemoUtils.addVariation(7.2, 2);
        DemoData.waterQuality.current.turbidity = DemoUtils.addVariation(1.5, 10);
        DemoData.waterQuality.current.chlorine = DemoUtils.addVariation(1.0, 5);
        
        // Update energy consumption
        DemoData.energy.consumption.current = Math.round(DemoUtils.addVariation(2847, 8));
        
        // Update performance metrics
        DemoData.performance.waterQualityIndex = Math.round(DemoUtils.addVariation(94, 3));
        DemoData.performance.treatmentEfficiency = Math.round(DemoUtils.addVariation(87, 5));
        DemoData.performance.energyOptimization = Math.round(DemoUtils.addVariation(92, 4));
        
        // Update system status
        DemoData.systemStatus.lastUpdate = new Date();
    },

    // Get sensor data by type
    getSensorsByType: (type) => {
        return DemoData.sensors.locations.filter(sensor => sensor.type === type);
    },

    // Get alerts by type
    getAlertsByType: (type) => {
        return DemoData.alerts.filter(alert => alert.type === type);
    },

    // Get unacknowledged alerts
    getUnacknowledgedAlerts: () => {
        return DemoData.alerts.filter(alert => !alert.acknowledged);
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DemoData, DemoUtils };
}
