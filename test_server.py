#!/usr/bin/env python3
"""
Test server to check if FastAPI is working correctly
"""

import os
from fastapi import FastAP<PERSON>
from fastapi.responses import HTMLResponse
import uvicorn

# Simple FastAPI app
app = FastAPI(title="Test Server")

@app.get("/")
async def root():
    return {"message": "Test server is working!"}

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    print("🚀 Starting test server...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
