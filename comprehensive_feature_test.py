#!/usr/bin/env python3
"""
Comprehensive Feature Test for Unified Environmental Platform
Tests all features in both frontend and backend
"""

import asyncio
import sys
import logging
import time
import requests
import json
from pathlib import Path
from datetime import datetime
import subprocess
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveFeatureTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.test_results = []
        self.feature_coverage = {
            'backend': {},
            'frontend': {}
        }
        
    def log_test_result(self, category: str, test_name: str, success: bool, message: str = "", details: dict = None):
        """Log test result with category"""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} [{category}] {test_name}: {message}")
        
        self.test_results.append({
            'category': category,
            'test': test_name,
            'success': success,
            'message': message,
            'details': details or {},
            'timestamp': datetime.now()
        })
    
    def test_backend_core_apis(self):
        """Test all backend API endpoints"""
        logger.info("\n🔧 TESTING BACKEND CORE APIs")
        logger.info("-" * 50)
        
        endpoints = [
            ("/health", "Health Check", "GET"),
            ("/", "Root Endpoint", "GET"),
            ("/api/status", "System Status", "GET"),
            ("/api/dashboard", "Dashboard Data", "GET"),
            ("/docs", "API Documentation", "GET"),
            ("/openapi.json", "OpenAPI Schema", "GET")
        ]
        
        all_success = True
        for endpoint, name, method in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=10)
                success = response.status_code == 200
                
                details = {
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'content_type': response.headers.get('content-type', 'unknown')
                }
                
                if endpoint == "/api/dashboard" and success:
                    data = response.json()
                    success = data.get('success', False) and 'data' in data
                    details['has_data'] = 'data' in data
                    details['data_keys'] = list(data.get('data', {}).keys()) if 'data' in data else []
                
                self.log_test_result(
                    "BACKEND-API", 
                    name, 
                    success, 
                    f"Status: {response.status_code}, Time: {response.elapsed.total_seconds():.2f}s",
                    details
                )
                all_success = all_success and success
                
            except Exception as e:
                self.log_test_result("BACKEND-API", name, False, f"Error: {e}")
                all_success = False
        
        self.feature_coverage['backend']['core_apis'] = all_success
        return all_success
    
    def test_backend_marine_conservation(self):
        """Test marine conservation features in backend"""
        logger.info("\n🌊 TESTING BACKEND MARINE CONSERVATION")
        logger.info("-" * 50)
        
        try:
            response = requests.get(f"{self.backend_url}/api/dashboard", timeout=10)
            if response.status_code != 200:
                self.log_test_result("BACKEND-MARINE", "API Access", False, "Dashboard API not accessible")
                return False
            
            data = response.json().get('data', {})
            marine_data = data.get('marine_conservation', {})
            
            # Check required marine conservation fields
            required_fields = [
                'health_score', 'debris_count', 'vessel_count', 'risk_level',
                'biodiversity_index', 'conservation_actions', 'monitoring_stations'
            ]
            
            missing_fields = []
            present_fields = []
            
            for field in required_fields:
                if field in marine_data:
                    present_fields.append(field)
                else:
                    missing_fields.append(field)
            
            success = len(missing_fields) == 0
            
            self.log_test_result(
                "BACKEND-MARINE", 
                "Data Structure", 
                success, 
                f"Fields: {len(present_fields)}/{len(required_fields)}, Missing: {missing_fields}",
                {
                    'present_fields': present_fields,
                    'missing_fields': missing_fields,
                    'sample_data': marine_data
                }
            )
            
            # Test data quality
            if marine_data:
                health_score = marine_data.get('health_score', 0)
                debris_count = marine_data.get('debris_count', 0)
                vessel_count = marine_data.get('vessel_count', 0)
                
                data_quality_success = (
                    isinstance(health_score, (int, float)) and 0 <= health_score <= 1 and
                    isinstance(debris_count, int) and debris_count >= 0 and
                    isinstance(vessel_count, int) and vessel_count >= 0
                )
                
                self.log_test_result(
                    "BACKEND-MARINE", 
                    "Data Quality", 
                    data_quality_success, 
                    f"Health: {health_score}, Debris: {debris_count}, Vessels: {vessel_count}"
                )
                
                success = success and data_quality_success
            
            self.feature_coverage['backend']['marine_conservation'] = success
            return success
            
        except Exception as e:
            self.log_test_result("BACKEND-MARINE", "Marine Conservation Test", False, f"Error: {e}")
            self.feature_coverage['backend']['marine_conservation'] = False
            return False
    
    def test_backend_water_management(self):
        """Test water management features in backend"""
        logger.info("\n💧 TESTING BACKEND WATER MANAGEMENT")
        logger.info("-" * 50)
        
        try:
            response = requests.get(f"{self.backend_url}/api/dashboard", timeout=10)
            if response.status_code != 200:
                self.log_test_result("BACKEND-WATER", "API Access", False, "Dashboard API not accessible")
                return False
            
            data = response.json().get('data', {})
            water_data = data.get('water_management', {})
            
            # Check required water management fields
            required_fields = [
                'treatment_efficiency', 'energy_efficiency', 'carbon_footprint',
                'status', 'daily_capacity', 'active_plants'
            ]
            
            missing_fields = []
            present_fields = []
            
            for field in required_fields:
                if field in water_data:
                    present_fields.append(field)
                else:
                    missing_fields.append(field)
            
            success = len(missing_fields) == 0
            
            self.log_test_result(
                "BACKEND-WATER", 
                "Data Structure", 
                success, 
                f"Fields: {len(present_fields)}/{len(required_fields)}, Missing: {missing_fields}",
                {
                    'present_fields': present_fields,
                    'missing_fields': missing_fields,
                    'sample_data': water_data
                }
            )
            
            # Test data quality
            if water_data:
                treatment_eff = water_data.get('treatment_efficiency', 0)
                energy_eff = water_data.get('energy_efficiency', 0)
                carbon_footprint = water_data.get('carbon_footprint', 0)
                
                data_quality_success = (
                    isinstance(treatment_eff, (int, float)) and 0 <= treatment_eff <= 1 and
                    isinstance(energy_eff, (int, float)) and 0 <= energy_eff <= 1 and
                    isinstance(carbon_footprint, (int, float)) and carbon_footprint >= 0
                )
                
                self.log_test_result(
                    "BACKEND-WATER", 
                    "Data Quality", 
                    data_quality_success, 
                    f"Treatment: {treatment_eff:.2f}, Energy: {energy_eff:.2f}, Carbon: {carbon_footprint}"
                )
                
                success = success and data_quality_success
            
            self.feature_coverage['backend']['water_management'] = success
            return success
            
        except Exception as e:
            self.log_test_result("BACKEND-WATER", "Water Management Test", False, f"Error: {e}")
            self.feature_coverage['backend']['water_management'] = False
            return False
    
    def test_backend_integrated_analytics(self):
        """Test integrated analytics features in backend"""
        logger.info("\n📊 TESTING BACKEND INTEGRATED ANALYTICS")
        logger.info("-" * 50)
        
        try:
            response = requests.get(f"{self.backend_url}/api/dashboard", timeout=10)
            if response.status_code != 200:
                self.log_test_result("BACKEND-ANALYTICS", "API Access", False, "Dashboard API not accessible")
                return False
            
            data = response.json().get('data', {})
            analytics_data = data.get('integrated_analytics', {})
            
            # Check required analytics fields
            required_fields = [
                'environmental_score', 'synergy_score', 'correlations',
                'recommendations', 'cross_system_insights'
            ]
            
            missing_fields = []
            present_fields = []
            
            for field in required_fields:
                if field in analytics_data:
                    present_fields.append(field)
                else:
                    missing_fields.append(field)
            
            success = len(missing_fields) == 0
            
            self.log_test_result(
                "BACKEND-ANALYTICS", 
                "Data Structure", 
                success, 
                f"Fields: {len(present_fields)}/{len(required_fields)}, Missing: {missing_fields}",
                {
                    'present_fields': present_fields,
                    'missing_fields': missing_fields,
                    'sample_data': analytics_data
                }
            )
            
            # Test analytics quality
            if analytics_data:
                env_score = analytics_data.get('environmental_score', 0)
                synergy_score = analytics_data.get('synergy_score', 0)
                correlations = analytics_data.get('correlations', [])
                recommendations = analytics_data.get('recommendations', [])
                
                analytics_quality_success = (
                    isinstance(env_score, (int, float)) and 0 <= env_score <= 1 and
                    isinstance(synergy_score, (int, float)) and 0 <= synergy_score <= 1 and
                    isinstance(correlations, list) and
                    isinstance(recommendations, list)
                )
                
                self.log_test_result(
                    "BACKEND-ANALYTICS", 
                    "Analytics Quality", 
                    analytics_quality_success, 
                    f"Env: {env_score:.2f}, Synergy: {synergy_score:.2f}, Correlations: {len(correlations)}, Recommendations: {len(recommendations)}"
                )
                
                success = success and analytics_quality_success
            
            self.feature_coverage['backend']['integrated_analytics'] = success
            return success
            
        except Exception as e:
            self.log_test_result("BACKEND-ANALYTICS", "Integrated Analytics Test", False, f"Error: {e}")
            self.feature_coverage['backend']['integrated_analytics'] = False
            return False
    
    def test_frontend_accessibility(self):
        """Test frontend accessibility and structure"""
        logger.info("\n🎨 TESTING FRONTEND ACCESSIBILITY")
        logger.info("-" * 50)
        
        try:
            response = requests.get(self.frontend_url, timeout=10)
            success = response.status_code == 200
            
            details = {
                'status_code': response.status_code,
                'content_type': response.headers.get('content-type', 'unknown'),
                'content_length': len(response.content)
            }
            
            self.log_test_result(
                "FRONTEND-ACCESS", 
                "Frontend Accessibility", 
                success, 
                f"Status: {response.status_code}, Size: {len(response.content)} bytes",
                details
            )
            
            if success:
                content = response.text
                
                # Check for key frontend elements
                required_elements = [
                    'Environmental AI',
                    'Dashboard',
                    'Marine Conservation',
                    'Water Management',
                    'Analytics',
                    'chart.js',
                    'leaflet'
                ]
                
                missing_elements = []
                present_elements = []
                
                for element in required_elements:
                    if element.lower() in content.lower():
                        present_elements.append(element)
                    else:
                        missing_elements.append(element)
                
                structure_success = len(missing_elements) == 0
                
                self.log_test_result(
                    "FRONTEND-STRUCTURE", 
                    "Frontend Structure", 
                    structure_success, 
                    f"Elements: {len(present_elements)}/{len(required_elements)}, Missing: {missing_elements}",
                    {
                        'present_elements': present_elements,
                        'missing_elements': missing_elements
                    }
                )
                
                success = success and structure_success
            
            self.feature_coverage['frontend']['accessibility'] = success
            return success
            
        except Exception as e:
            self.log_test_result("FRONTEND-ACCESS", "Frontend Accessibility Test", False, f"Error: {e}")
            self.feature_coverage['frontend']['accessibility'] = False
            return False
    
    def test_frontend_backend_integration(self):
        """Test frontend-backend integration"""
        logger.info("\n🔗 TESTING FRONTEND-BACKEND INTEGRATION")
        logger.info("-" * 50)
        
        try:
            # Test CORS configuration
            headers = {'Origin': self.frontend_url}
            response = requests.get(f"{self.backend_url}/api/dashboard", headers=headers, timeout=10)
            
            cors_success = response.status_code == 200
            cors_headers = [
                'access-control-allow-origin',
                'access-control-allow-methods',
                'access-control-allow-headers'
            ]
            
            has_cors = any(header in response.headers for header in cors_headers)
            
            self.log_test_result(
                "INTEGRATION-CORS", 
                "CORS Configuration", 
                cors_success and has_cors, 
                f"Status: {response.status_code}, CORS Headers: {has_cors}"
            )
            
            # Test data format compatibility
            if cors_success:
                data = response.json()
                dashboard_data = data.get('data', {})
                
                # Check if data structure matches frontend expectations
                expected_sections = ['marine_conservation', 'water_management', 'integrated_analytics', 'real_time']
                
                missing_sections = []
                present_sections = []
                
                for section in expected_sections:
                    if section in dashboard_data:
                        present_sections.append(section)
                    else:
                        missing_sections.append(section)
                
                compatibility_success = len(missing_sections) == 0
                
                self.log_test_result(
                    "INTEGRATION-DATA", 
                    "Data Compatibility", 
                    compatibility_success, 
                    f"Sections: {len(present_sections)}/{len(expected_sections)}, Missing: {missing_sections}"
                )
                
                integration_success = cors_success and has_cors and compatibility_success
            else:
                integration_success = False
            
            self.feature_coverage['frontend']['backend_integration'] = integration_success
            return integration_success
            
        except Exception as e:
            self.log_test_result("INTEGRATION", "Frontend-Backend Integration Test", False, f"Error: {e}")
            self.feature_coverage['frontend']['backend_integration'] = False
            return False
    
    def run_comprehensive_test(self):
        """Run all comprehensive tests"""
        logger.info("🧪 COMPREHENSIVE FEATURE TEST - UNIFIED ENVIRONMENTAL PLATFORM")
        logger.info("=" * 80)
        
        # Backend tests
        backend_results = []
        backend_results.append(self.test_backend_core_apis())
        backend_results.append(self.test_backend_marine_conservation())
        backend_results.append(self.test_backend_water_management())
        backend_results.append(self.test_backend_integrated_analytics())
        
        # Frontend tests
        frontend_results = []
        frontend_results.append(self.test_frontend_accessibility())
        frontend_results.append(self.test_frontend_backend_integration())
        
        # Generate comprehensive report
        self.generate_comprehensive_report(backend_results, frontend_results)
        
        return all(backend_results) and all(frontend_results)
    
    def generate_comprehensive_report(self, backend_results, frontend_results):
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        backend_passed = sum(backend_results)
        frontend_passed = sum(frontend_results)
        
        logger.info("\n📊 COMPREHENSIVE FEATURE REPORT")
        logger.info("=" * 80)
        logger.info(f"Backend Tests: {backend_passed}/{len(backend_results)} passed")
        logger.info(f"Frontend Tests: {frontend_passed}/{len(frontend_results)} passed")
        logger.info(f"Total Tests: {passed_tests}/{total_tests} passed")
        logger.info(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        # Feature coverage summary
        logger.info("\n🎯 FEATURE COVERAGE SUMMARY")
        logger.info("-" * 50)
        
        logger.info("BACKEND FEATURES:")
        for feature, status in self.feature_coverage['backend'].items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {feature.replace('_', ' ').title()}")
        
        logger.info("\nFRONTEND FEATURES:")
        for feature, status in self.feature_coverage['frontend'].items():
            status_icon = "✅" if status else "❌"
            logger.info(f"  {status_icon} {feature.replace('_', ' ').title()}")
        
        if failed_tests > 0:
            logger.info(f"\n❌ FAILED TESTS ({failed_tests}):")
            for result in self.test_results:
                if not result['success']:
                    logger.info(f"  - [{result['category']}] {result['test']}: {result['message']}")
        
        logger.info("")
        if failed_tests == 0:
            logger.info("🎉 ALL FEATURES IMPLEMENTED AND WORKING!")
            logger.info("✅ Backend: Fully functional with all features")
            logger.info("✅ Frontend: Modern dashboard with complete integration")
            logger.info("✅ Integration: Perfect frontend-backend communication")
        else:
            logger.info(f"⚠️ {failed_tests} issues detected. Review above for details.")

def main():
    """Main test execution"""
    logger.info("🌊💧 Unified Environmental Platform - Comprehensive Feature Test")
    logger.info("=" * 80)
    
    tester = ComprehensiveFeatureTester()
    success = tester.run_comprehensive_test()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
