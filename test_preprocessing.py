"""
Test script for Climate Data Preprocessing Pipeline.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.data.preprocessing.climate_preprocessor import (
    ClimateDataPreprocessor, 
    ProcessedClimateData,
    preprocess_climate_data,
    create_climate_time_series
)


async def test_preprocessor_initialization():
    """Test preprocessor initialization."""
    print("🧪 Testing Climate Data Preprocessor Initialization...")
    
    try:
        preprocessor = ClimateDataPreprocessor()
        init_success = await preprocessor.initialize()
        
        if init_success:
            print("✅ Climate Data Preprocessor initialized successfully")
            return True
        else:
            print("❌ Failed to initialize preprocessor")
            return False
            
    except Exception as e:
        print(f"❌ Preprocessor initialization test failed: {e}")
        return False


async def test_openweather_preprocessing():
    """Test OpenWeatherMap data preprocessing."""
    print("\n🧪 Testing OpenWeatherMap Data Preprocessing...")
    
    try:
        # Sample OpenWeatherMap API response
        sample_data = {
            "coord": {"lon": -74.006, "lat": 40.7128},
            "weather": [{"id": 800, "main": "Clear", "description": "clear sky", "icon": "01d"}],
            "base": "stations",
            "main": {
                "temp": 22.5,
                "feels_like": 24.1,
                "temp_min": 20.2,
                "temp_max": 25.3,
                "pressure": 1013,
                "humidity": 65
            },
            "visibility": 10000,
            "wind": {"speed": 3.2, "deg": 180},
            "clouds": {"all": 20},
            "dt": int(datetime.now().timestamp()),
            "sys": {"type": 1, "id": 1234, "country": "US", "sunrise": 1234567890, "sunset": 1234567890},
            "timezone": -14400,
            "id": 5128581,
            "name": "New York",
            "cod": 200
        }
        
        preprocessor = ClimateDataPreprocessor()
        await preprocessor.initialize()
        
        print("🔄 Processing OpenWeatherMap sample data...")
        processed = await preprocessor.process_openweather_data(sample_data)
        
        if processed:
            print("✅ OpenWeatherMap data processed successfully")
            print(f"📍 Location: {processed.location}")
            print(f"🌡️ Temperature: {processed.temperature}°C")
            print(f"💧 Humidity: {processed.humidity}%")
            print(f"🌬️ Wind Speed: {processed.wind_speed} m/s")
            print(f"📊 Data Quality Score: {processed.data_quality_score:.2f}")
            
            if processed.missing_fields:
                print(f"⚠️ Missing fields: {processed.missing_fields}")
            
            if processed.anomaly_flags:
                print(f"🚨 Anomaly flags: {processed.anomaly_flags}")
            
            return True
        else:
            print("❌ Failed to process OpenWeatherMap data")
            return False
            
    except Exception as e:
        print(f"❌ OpenWeatherMap preprocessing test failed: {e}")
        return False


async def test_nasa_preprocessing():
    """Test NASA data preprocessing."""
    print("\n🧪 Testing NASA Data Preprocessing...")
    
    try:
        # Sample NASA POWER API response
        sample_data = {
            "latitude": 40.7128,
            "longitude": -74.006,
            "parameters": {
                "T2M": {
                    "20240101": 15.2,
                    "20240102": 16.8,
                    "20240103": 14.5
                },
                "RH2M": {
                    "20240101": 68.5,
                    "20240102": 72.1,
                    "20240103": 65.3
                },
                "PRECTOTCORR": {
                    "20240101": 0.0,
                    "20240102": 2.3,
                    "20240103": 0.0
                },
                "WS2M": {
                    "20240101": 4.2,
                    "20240102": 3.8,
                    "20240103": 5.1
                }
            },
            "metadata": {
                "source": "NASA POWER",
                "version": "2.0"
            }
        }
        
        preprocessor = ClimateDataPreprocessor()
        await preprocessor.initialize()
        
        print("🔄 Processing NASA sample data...")
        processed = await preprocessor.process_nasa_data(sample_data)
        
        if processed:
            print("✅ NASA data processed successfully")
            print(f"📍 Location: {processed.location}")
            print(f"🌡️ Temperature: {processed.temperature}°C")
            print(f"💧 Humidity: {processed.humidity}%")
            print(f"🌧️ Precipitation: {processed.precipitation} mm")
            print(f"📊 Data Quality Score: {processed.data_quality_score:.2f}")
            
            return True
        else:
            print("❌ Failed to process NASA data")
            return False
            
    except Exception as e:
        print(f"❌ NASA preprocessing test failed: {e}")
        return False


async def test_noaa_preprocessing():
    """Test NOAA data preprocessing."""
    print("\n🧪 Testing NOAA Data Preprocessing...")
    
    try:
        # Sample NOAA data point
        sample_data = {
            "timestamp": datetime.now(),
            "location": "NOAA Station NYC",
            "latitude": 40.7128,
            "longitude": -74.006,
            "datatype": "TMAX",
            "value": 25.3,
            "unit": "°C"
        }
        
        preprocessor = ClimateDataPreprocessor()
        await preprocessor.initialize()
        
        print("🔄 Processing NOAA sample data...")
        processed = await preprocessor.process_noaa_data(sample_data)
        
        if processed:
            print("✅ NOAA data processed successfully")
            print(f"📍 Location: {processed.location}")
            print(f"🌡️ Max Temperature: {processed.temperature_max}°C")
            print(f"📊 Data Quality Score: {processed.data_quality_score:.2f}")
            
            return True
        else:
            print("❌ Failed to process NOAA data")
            return False
            
    except Exception as e:
        print(f"❌ NOAA preprocessing test failed: {e}")
        return False


async def test_data_validation():
    """Test data validation and anomaly detection."""
    print("\n🧪 Testing Data Validation and Anomaly Detection...")
    
    try:
        preprocessor = ClimateDataPreprocessor()
        await preprocessor.initialize()
        
        # Create test data with anomalies
        test_data = ProcessedClimateData(
            timestamp=datetime.now(),
            location="Test Location",
            latitude=40.0,
            longitude=-74.0,
            source="test",
            temperature=150.0,  # Anomalous temperature
            humidity=150.0,     # Anomalous humidity
            pressure=500.0,     # Anomalous pressure
            wind_speed=200.0    # Anomalous wind speed
        )
        
        print("🔄 Validating data with anomalies...")
        validated = await preprocessor._validate_and_clean(test_data)
        
        print(f"📊 Data Quality Score: {validated.data_quality_score:.2f}")
        print(f"🚨 Anomaly Flags: {validated.anomaly_flags}")
        print(f"⚠️ Missing Fields: {validated.missing_fields}")
        
        if len(validated.anomaly_flags) > 0:
            print("✅ Anomaly detection working correctly")
            return True
        else:
            print("⚠️ Anomaly detection may not be working as expected")
            return False
            
    except Exception as e:
        print(f"❌ Data validation test failed: {e}")
        return False


async def test_multi_source_aggregation():
    """Test multi-source data aggregation."""
    print("\n🧪 Testing Multi-Source Data Aggregation...")
    
    try:
        preprocessor = ClimateDataPreprocessor()
        await preprocessor.initialize()
        
        # Create multiple data sources for the same location
        data_sources = [
            ProcessedClimateData(
                timestamp=datetime.now(),
                location="New York",
                latitude=40.7128,
                longitude=-74.006,
                source="openweathermap",
                temperature=22.5,
                humidity=65.0,
                data_quality_score=0.9
            ),
            ProcessedClimateData(
                timestamp=datetime.now(),
                location="New York",
                latitude=40.7128,
                longitude=-74.006,
                source="nasa",
                temperature=23.1,
                humidity=67.0,
                data_quality_score=0.8
            ),
            ProcessedClimateData(
                timestamp=datetime.now(),
                location="New York",
                latitude=40.7128,
                longitude=-74.006,
                source="noaa",
                temperature=22.8,
                humidity=66.0,
                data_quality_score=0.85
            )
        ]
        
        print("🔄 Aggregating data from multiple sources...")
        aggregated = await preprocessor.aggregate_multi_source_data(data_sources)
        
        if aggregated:
            print("✅ Multi-source aggregation successful")
            print(f"📍 Location: {aggregated.location}")
            print(f"🌡️ Aggregated Temperature: {aggregated.temperature:.1f}°C")
            print(f"📊 Combined Quality Score: {aggregated.data_quality_score:.2f}")
            print(f"🔗 Source: {aggregated.source}")
            
            return True
        else:
            print("❌ Failed to aggregate multi-source data")
            return False
            
    except Exception as e:
        print(f"❌ Multi-source aggregation test failed: {e}")
        return False


async def test_time_series_creation():
    """Test time series creation."""
    print("\n🧪 Testing Time Series Creation...")
    
    try:
        # Create sample time series data
        data_points = []
        base_time = datetime.now() - timedelta(hours=24)
        
        for i in range(24):  # 24 hours of data
            timestamp = base_time + timedelta(hours=i)
            data_point = ProcessedClimateData(
                timestamp=timestamp,
                location="Test Location",
                latitude=40.0,
                longitude=-74.0,
                source="test",
                temperature=20.0 + (i % 10),  # Varying temperature
                humidity=60.0 + (i % 20),     # Varying humidity
                pressure=1013.0 + (i % 5)    # Varying pressure
            )
            data_points.append(data_point)
        
        print(f"🔄 Creating time series from {len(data_points)} data points...")
        
        df = await create_climate_time_series(data_points)
        
        if not df.empty:
            print("✅ Time series created successfully")
            print(f"📊 DataFrame shape: {df.shape}")
            print(f"📅 Time range: {df.index.min()} to {df.index.max()}")
            print(f"🌡️ Temperature range: {df['temperature'].min():.1f}°C to {df['temperature'].max():.1f}°C")
            
            # Show sample data
            print("\n📈 Sample time series data:")
            print(df[['temperature', 'humidity', 'pressure']].head())
            
            return True
        else:
            print("❌ Failed to create time series")
            return False
            
    except Exception as e:
        print(f"❌ Time series creation test failed: {e}")
        return False


async def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing Convenience Functions...")
    
    try:
        # Test preprocess_climate_data function
        sample_openweather_data = {
            "coord": {"lon": -74.006, "lat": 40.7128},
            "main": {"temp": 22.5, "humidity": 65, "pressure": 1013},
            "wind": {"speed": 3.2, "deg": 180},
            "dt": int(datetime.now().timestamp()),
            "name": "New York"
        }
        
        print("🔄 Testing preprocess_climate_data function...")
        processed = await preprocess_climate_data(sample_openweather_data, 'openweathermap')
        
        if processed:
            print("✅ preprocess_climate_data working correctly")
            print(f"📍 Processed location: {processed.location}")
            print(f"🌡️ Temperature: {processed.temperature}°C")
            return True
        else:
            print("❌ preprocess_climate_data failed")
            return False
            
    except Exception as e:
        print(f"❌ Convenience functions test failed: {e}")
        return False


async def main():
    """Run all preprocessing tests."""
    print("🚀 Climate Data Preprocessing Pipeline Test Suite")
    print("=" * 65)
    
    test_results = []
    
    # Test 1: Preprocessor initialization
    init_result = await test_preprocessor_initialization()
    test_results.append(("Preprocessor Initialization", init_result))
    
    # Test 2: OpenWeatherMap preprocessing
    openweather_result = await test_openweather_preprocessing()
    test_results.append(("OpenWeatherMap Preprocessing", openweather_result))
    
    # Test 3: NASA preprocessing
    nasa_result = await test_nasa_preprocessing()
    test_results.append(("NASA Data Preprocessing", nasa_result))
    
    # Test 4: NOAA preprocessing
    noaa_result = await test_noaa_preprocessing()
    test_results.append(("NOAA Data Preprocessing", noaa_result))
    
    # Test 5: Data validation
    validation_result = await test_data_validation()
    test_results.append(("Data Validation & Anomaly Detection", validation_result))
    
    # Test 6: Multi-source aggregation
    aggregation_result = await test_multi_source_aggregation()
    test_results.append(("Multi-Source Data Aggregation", aggregation_result))
    
    # Test 7: Time series creation
    timeseries_result = await test_time_series_creation()
    test_results.append(("Time Series Creation", timeseries_result))
    
    # Test 8: Convenience functions
    convenience_result = await test_convenience_functions()
    test_results.append(("Convenience Functions", convenience_result))
    
    # Summary
    print("\n" + "=" * 65)
    print("📋 TEST SUMMARY")
    print("=" * 65)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("-" * 65)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All preprocessing tests passed!")
        print("Climate data preprocessing pipeline is ready for use.")
    elif passed >= 6:
        print(f"\n🎉 Preprocessing pipeline is functional! ({passed}/{total} tests passed)")
        print("Core data processing capabilities are available.")
    elif passed > 0:
        print(f"\n⚠️ Partial functionality ({passed}/{total} tests passed)")
        print("Some preprocessing features are working.")
    else:
        print("\n❌ Preprocessing pipeline needs attention.")
        print("Please check the implementation and dependencies.")
    
    print("\n📋 Next Steps:")
    if passed >= 6:
        print("  1. ✅ Data preprocessing pipeline is ready!")
        print("  2. ✅ Multi-source climate data can be processed")
        print("  3. ✅ Data validation and quality checks working")
        print("  4. ✅ Time series analysis capabilities available")
        print("  5. 🚀 Ready for data validation and quality assurance (Task 2.10)")
    else:
        print("  1. Review failed tests and fix implementation issues")
        print("  2. Ensure all dependencies are properly installed")
        print("  3. Re-run tests to verify fixes")
    
    print("\n🌍 Data Processing System Status:")
    print("  ✅ Multi-source climate data collection")
    print(f"  {'✅' if passed >= 6 else '⚠️'} Climate data preprocessing pipeline")
    print("  🚧 Data validation and quality assurance (next)")
    print("  📋 Data normalization and standardization (upcoming)")


if __name__ == "__main__":
    asyncio.run(main())
