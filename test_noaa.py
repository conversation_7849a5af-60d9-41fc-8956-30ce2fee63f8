"""
Test script for NOAA Climate Data API integration.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.data.collectors.noaa_collector import (
    NOAAClimateCollector, 
    get_noaa_climate_for_location,
    get_extreme_weather_for_location,
    get_climate_normals_for_location
)
from src.utils.config import get_settings


async def test_noaa_api_connection():
    """Test NOAA API connection and basic functionality."""
    print("🧪 Testing NOAA Climate Data API Integration...")
    
    try:
        settings = get_settings()
        
        if not settings.NOAA_API_TOKEN:
            print("⚠️ NOAA API token not configured")
            print("For full functionality, get a free API token from: https://www.ncdc.noaa.gov/cdo-web/token")
            print("Testing with limited functionality...")
        else:
            print(f"✅ NOAA API token found: {settings.NOAA_API_TOKEN[:10]}...")
        
        # Initialize collector
        collector = NOAAClimateCollector()
        init_success = await collector.initialize()
        
        if not init_success:
            print("❌ Failed to initialize NOAA collector")
            return False
        
        print("✅ NOAA Climate Data collector initialized successfully")
        
        await collector.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ NOAA API connection test failed: {e}")
        return False


async def test_datasets():
    """Test NOAA datasets retrieval."""
    print("\n🧪 Testing NOAA Datasets...")
    
    try:
        collector = NOAAClimateCollector()
        await collector.initialize()
        
        print("🔄 Fetching available NOAA datasets...")
        datasets = await collector.get_datasets()
        
        if datasets:
            print(f"✅ Retrieved {len(datasets)} NOAA datasets")
            
            # Show sample datasets
            print("📊 Sample datasets:")
            for dataset in datasets[:5]:
                name = dataset.get('name', 'Unknown')
                dataset_id = dataset.get('id', 'Unknown')
                print(f"  • {name} ({dataset_id})")
        else:
            print("⚠️ No datasets retrieved (may require API token)")
        
        await collector.shutdown()
        return len(datasets) > 0
        
    except Exception as e:
        print(f"❌ Datasets test failed: {e}")
        return False


async def test_weather_stations():
    """Test weather stations discovery."""
    print("\n🧪 Testing Weather Stations Discovery...")
    
    try:
        collector = NOAAClimateCollector()
        await collector.initialize()
        
        # Test location: New York City
        lat, lon = 40.7128, -74.0060
        
        print(f"🔄 Finding weather stations near New York City ({lat}, {lon})...")
        stations = await collector.find_stations(lat, lon, radius_km=100, limit=5)
        
        if stations:
            print(f"✅ Found {len(stations)} weather stations")
            
            print("🌡️ Sample weather stations:")
            for station in stations[:3]:
                distance = collector._calculate_distance(lat, lon, station.latitude, station.longitude)
                print(f"  • {station.name}")
                print(f"    ID: {station.id}")
                print(f"    Distance: {distance:.1f} km")
                print(f"    Data coverage: {station.datacoverage:.1%}")
                print(f"    Data period: {station.min_date} to {station.max_date}")
                print()
        else:
            print("⚠️ No weather stations found (may require API token)")
        
        await collector.shutdown()
        return len(stations) > 0
        
    except Exception as e:
        print(f"❌ Weather stations test failed: {e}")
        return False


async def test_station_data():
    """Test weather station data retrieval."""
    print("\n🧪 Testing Station Data Retrieval...")
    
    try:
        collector = NOAAClimateCollector()
        await collector.initialize()
        
        # Find a station first
        lat, lon = 40.7128, -74.0060
        stations = await collector.find_stations(lat, lon, limit=1)
        
        if not stations:
            print("⚠️ No stations found for data retrieval test")
            await collector.shutdown()
            return False
        
        station = stations[0]
        print(f"🔄 Getting data from station: {station.name}")
        
        # Get recent data (last 7 days)
        from datetime import datetime, timedelta
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        
        print(f"📅 Date range: {start_date} to {end_date}")
        
        data = await collector.get_station_data(
            station.id, start_date, end_date,
            ['TMAX', 'TMIN', 'PRCP']
        )
        
        if data:
            print(f"✅ Retrieved {len(data)} data points")
            
            # Group by data type
            data_by_type = {}
            for item in data:
                if item.datatype not in data_by_type:
                    data_by_type[item.datatype] = []
                data_by_type[item.datatype].append(item)
            
            print("📊 Data summary:")
            for datatype, items in data_by_type.items():
                name = collector.climate_datatypes.get(datatype, datatype)
                unit = collector._get_unit_for_datatype(datatype)
                values = [item.value for item in items]
                
                if values:
                    avg_value = sum(values) / len(values)
                    print(f"  • {name}: {len(values)} readings, avg: {avg_value:.1f} {unit}")
        else:
            print("⚠️ No station data retrieved (may require API token or data not available)")
        
        await collector.shutdown()
        return len(data) > 0
        
    except Exception as e:
        print(f"❌ Station data test failed: {e}")
        return False


async def test_climate_summary():
    """Test comprehensive climate summary."""
    print("\n🧪 Testing Climate Summary...")
    
    try:
        collector = NOAAClimateCollector()
        await collector.initialize()
        
        # Test location: London
        lat, lon = 51.5074, -0.1278
        location_name = "London"
        
        print(f"🔄 Generating climate summary for {location_name}...")
        
        summary = await collector.get_climate_summary_for_location(lat, lon, location_name, days_back=14)
        
        if summary and not summary.get('error'):
            print("✅ Climate summary generated successfully")
            print(f"📍 Location: {summary.get('location')}")
            print(f"🌡️ Stations found: {summary.get('stations_found', 0)}")
            
            primary_station = summary.get('primary_station', {})
            if primary_station:
                print(f"🏢 Primary station: {primary_station.get('name', 'Unknown')}")
                print(f"📏 Distance: {primary_station.get('distance_km', 0):.1f} km")
                print(f"📊 Data coverage: {primary_station.get('data_coverage', 0):.1%}")
            
            climate_data = summary.get('climate_data', {})
            if climate_data and not climate_data.get('error'):
                print("🌡️ Climate data analysis:")
                for datatype, analysis in climate_data.items():
                    if isinstance(analysis, dict):
                        name = analysis.get('name', datatype)
                        avg = analysis.get('average', 0)
                        unit = analysis.get('unit', '')
                        count = analysis.get('count', 0)
                        print(f"  • {name}: {avg:.1f} {unit} (avg from {count} readings)")
        else:
            error_msg = summary.get('error', 'Unknown error') if summary else 'No summary generated'
            print(f"⚠️ Climate summary: {error_msg}")
        
        await collector.shutdown()
        return bool(summary and not summary.get('error'))
        
    except Exception as e:
        print(f"❌ Climate summary test failed: {e}")
        return False


async def test_extreme_weather():
    """Test extreme weather events detection."""
    print("\n🧪 Testing Extreme Weather Events...")
    
    try:
        collector = NOAAClimateCollector()
        await collector.initialize()
        
        # Test location: Miami (hurricane-prone area)
        lat, lon = 25.7617, -80.1918
        
        # Look for events in the last 30 days
        from datetime import datetime, timedelta
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        
        print(f"🔄 Searching for extreme weather events near Miami...")
        print(f"📅 Period: {start_date} to {end_date}")
        
        events = await collector.get_extreme_weather_events(lat, lon, start_date, end_date)
        
        if events:
            print(f"✅ Found {len(events)} extreme weather events")
            
            print("⚡ Recent extreme weather events:")
            for event in events[:5]:  # Show first 5
                date = event.get('date', 'Unknown')
                event_type = event.get('event_type', 'Unknown')
                station = event.get('station', 'Unknown')
                distance = event.get('station_distance_km', 0)
                print(f"  • {date}: {event_type} (Station: {station}, {distance:.1f} km away)")
        else:
            print("⚠️ No extreme weather events found (may require API token or no events in period)")
        
        await collector.shutdown()
        return len(events) > 0
        
    except Exception as e:
        print(f"❌ Extreme weather test failed: {e}")
        return False


async def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing Convenience Functions...")
    
    try:
        print("🔄 Testing get_noaa_climate_for_location function...")
        
        climate_data = await get_noaa_climate_for_location("Tokyo", 35.6762, 139.6503, days_back=7)
        
        if climate_data and not climate_data.get('error'):
            print("✅ get_noaa_climate_for_location working correctly")
            print(f"📍 Location: {climate_data.get('location')}")
            stations = climate_data.get('stations_found', 0)
            print(f"🌡️ Stations: {stations}")
        else:
            error_msg = climate_data.get('error', 'Unknown error') if climate_data else 'No data returned'
            print(f"⚠️ get_noaa_climate_for_location: {error_msg}")
        
        return bool(climate_data and not climate_data.get('error'))
        
    except Exception as e:
        print(f"❌ Convenience functions test failed: {e}")
        return False


async def main():
    """Run all NOAA Climate Data tests."""
    print("🚀 NOAA Climate Data API Integration Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: API connection
    connection_result = await test_noaa_api_connection()
    test_results.append(("NOAA API Connection", connection_result))
    
    # Test 2: Datasets
    datasets_result = await test_datasets()
    test_results.append(("NOAA Datasets", datasets_result))
    
    # Test 3: Weather stations
    stations_result = await test_weather_stations()
    test_results.append(("Weather Stations Discovery", stations_result))
    
    # Test 4: Station data
    station_data_result = await test_station_data()
    test_results.append(("Station Data Retrieval", station_data_result))
    
    # Test 5: Climate summary
    summary_result = await test_climate_summary()
    test_results.append(("Climate Summary", summary_result))
    
    # Test 6: Extreme weather
    extreme_result = await test_extreme_weather()
    test_results.append(("Extreme Weather Events", extreme_result))
    
    # Test 7: Convenience functions
    convenience_result = await test_convenience_functions()
    test_results.append(("Convenience Functions", convenience_result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed >= 2:  # At least basic functionality working
        print("\n🎉 NOAA Climate Data integration is functional!")
        print("Historical climate data collection capabilities are available.")
    elif passed > 0:
        print(f"\n⚠️ Partial functionality ({passed}/{total} tests passed)")
        print("Some NOAA data sources are working.")
    else:
        print("\n❌ NOAA integration needs attention.")
        print("Please check API configuration and internet connection.")
    
    print("\n📋 Next Steps:")
    if passed >= 2:
        print("  1. NOAA climate data integration is ready!")
        print("  2. Historical weather data enhances climate analysis")
        print("  3. Weather station data provides local climate insights")
        print("  4. Consider getting a NOAA API token for full functionality")
    else:
        print("  1. Get a free NOAA API token from: https://www.ncdc.noaa.gov/cdo-web/token")
        print("  2. Add NOAA_API_TOKEN=your_token_here to your .env file")
        print("  3. Re-run this test")
    
    print("\n🌍 Climate Data Collection System Status:")
    print("  ✅ OpenWeatherMap: Real-time weather data")
    print("  ✅ NASA: Satellite climate observations")
    print("  ✅ World Bank: Global climate indicators")
    print(f"  {'✅' if passed >= 2 else '⚠️'} NOAA: Historical climate records")


if __name__ == "__main__":
    asyncio.run(main())
