#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Marine Conservation Integration Tests
Testing all implemented marine conservation components
"""

import asyncio
import pytest
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import marine conservation components
from marine_conservation.agents.climate_marine_agent import ClimateMarineAgent, analyze_climate_marine_impact
from marine_conservation.dashboard.debris_tracking_dashboard import RealTimeDebrisDashboard, create_debris_dashboard
from marine_conservation.agents.marine_debris_ai_agent import MarineDebrisAIAgent, create_marine_debris_agent
from marine_conservation.computer_vision.hotspot_detection import ComputerVisionHotspotDetector, detect_debris_hotspots
from marine_conservation.taiwan_collaboration.government_platform import TaiwanGovernmentPlatform, create_taiwan_collaboration_platform


class TestMarineConservationIntegration:
    """Comprehensive test suite for marine conservation integration"""
    
    @pytest.fixture
    def test_area_bbox(self):
        """Test area: Mediterranean Sea near Barcelona"""
        return (2.0, 41.0, 3.0, 42.0)
    
    @pytest.fixture
    def taiwan_test_area(self):
        """Test area: Taiwan Strait"""
        return (119.0, 23.0, 121.0, 25.0)
    
    @pytest.mark.asyncio
    async def test_climate_marine_agent(self, test_area_bbox):
        """Test Climate Marine Agent functionality"""
        print("\n🌡️ Testing Climate Marine Agent")
        
        try:
            agent = ClimateMarineAgent()
            
            # Test correlation analysis
            correlations = await agent.analyze_climate_debris_correlation(test_area_bbox, analysis_period_days=7)
            assert isinstance(correlations, list), "Correlations should be a list"
            
            # Test climate impact prediction
            prediction = await agent.predict_climate_impact(test_area_bbox, prediction_days=30)
            assert hasattr(prediction, 'prediction_id'), "Prediction should have ID"
            assert hasattr(prediction, 'confidence'), "Prediction should have confidence"
            
            # Test comprehensive report generation
            report = await agent.generate_climate_report(test_area_bbox)
            assert 'report_id' in report, "Report should have ID"
            assert 'correlations' in report, "Report should include correlations"
            assert 'prediction' in report, "Report should include prediction"
            
            print(f"   ✅ Climate agent test passed - Report ID: {report.get('report_id', 'N/A')}")
            return True
            
        except Exception as e:
            print(f"   ❌ Climate agent test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_debris_tracking_dashboard(self, test_area_bbox, taiwan_test_area):
        """Test Real-Time Debris Tracking Dashboard"""
        print("\n📊 Testing Debris Tracking Dashboard")
        
        try:
            # Configure monitoring areas
            monitoring_areas = [
                {
                    'id': 'med_sea_test',
                    'name': 'Mediterranean Test Area',
                    'bbox': test_area_bbox,
                    'priority': 'high'
                },
                {
                    'id': 'taiwan_test',
                    'name': 'Taiwan Strait Test',
                    'bbox': taiwan_test_area,
                    'priority': 'critical'
                }
            ]
            
            # Create dashboard
            dashboard = await create_debris_dashboard(monitoring_areas)
            assert len(dashboard.monitoring_areas) == 2, "Should have 2 monitoring areas"
            
            # Collect real-time data
            dashboard_data = await dashboard.collect_real_time_data()
            assert hasattr(dashboard_data, 'metrics'), "Dashboard data should have metrics"
            assert hasattr(dashboard_data, 'debris_detections'), "Should have debris detections"
            assert hasattr(dashboard_data, 'vessel_data'), "Should have vessel data"
            assert hasattr(dashboard_data, 'alerts'), "Should have alerts"
            
            # Test JSON export
            json_data = dashboard.get_dashboard_json()
            assert isinstance(json_data, str), "JSON export should be string"
            assert len(json_data) > 0, "JSON should not be empty"
            
            print(f"   ✅ Dashboard test passed - {dashboard_data.metrics.total_debris_detected} debris detected")
            return True
            
        except Exception as e:
            print(f"   ❌ Dashboard test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_marine_debris_ai_agent(self, test_area_bbox):
        """Test Marine Debris AI Management Agent"""
        print("\n🤖 Testing Marine Debris AI Agent")
        
        try:
            agent = await create_marine_debris_agent()
            
            # Test debris situation analysis
            analysis = await agent.analyze_debris_situation(test_area_bbox, analysis_depth="comprehensive")
            assert hasattr(analysis, 'analysis_id'), "Analysis should have ID"
            assert hasattr(analysis, 'total_debris_detected'), "Should have debris count"
            assert hasattr(analysis, 'hotspots_identified'), "Should identify hotspots"
            assert hasattr(analysis, 'risk_assessment'), "Should have risk assessment"
            
            # Test cleanup operation creation
            if analysis.hotspots_identified:
                hotspot = analysis.hotspots_identified[0]
                debris_targets = [{
                    'location': hotspot['center'],
                    'size_estimate': hotspot.get('total_size_m2', 100),
                    'debris_count': hotspot.get('debris_count', 5),
                    'priority': 'high'
                }]
                
                # Create small area around hotspot
                lat, lon = hotspot['center']
                hotspot_area = (lon - 0.01, lat - 0.01, lon + 0.01, lat + 0.01)
                
                operation = await agent.create_cleanup_operation(hotspot_area, debris_targets)
                assert hasattr(operation, 'operation_id'), "Operation should have ID"
                assert hasattr(operation, 'estimated_cost'), "Should have cost estimate"
                assert hasattr(operation, 'success_probability'), "Should have success probability"
            
            # Test comprehensive management report
            report = await agent.generate_management_report(test_area_bbox)
            assert 'report_id' in report, "Report should have ID"
            assert 'analysis' in report, "Report should include analysis"
            
            print(f"   ✅ AI agent test passed - {analysis.total_debris_detected} debris analyzed")
            return True
            
        except Exception as e:
            print(f"   ❌ AI agent test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_computer_vision_hotspot_detection(self, test_area_bbox):
        """Test Computer Vision Hotspot Detection"""
        print("\n🎯 Testing Computer Vision Hotspot Detection")
        
        try:
            detector = ComputerVisionHotspotDetector()
            
            # Test hotspot identification
            hotspots = await detector.identify_debris_hotspots(test_area_bbox, analysis_period_days=7)
            assert isinstance(hotspots, list), "Hotspots should be a list"
            
            # Test comprehensive analysis report
            report = await detector.generate_hotspot_analysis_report(test_area_bbox, analysis_period_days=7)
            assert 'report_id' in report, "Report should have ID"
            assert 'summary' in report, "Report should have summary"
            assert 'hotspots' in report, "Report should include hotspots"
            assert 'recommendations' in report, "Report should have recommendations"
            
            # Validate hotspot data structure
            for hotspot in hotspots:
                assert hasattr(hotspot, 'hotspot_id'), "Hotspot should have ID"
                assert hasattr(hotspot, 'center_location'), "Should have center location"
                assert hasattr(hotspot, 'confidence_score'), "Should have confidence score"
                assert hasattr(hotspot, 'risk_level'), "Should have risk level"
            
            print(f"   ✅ Computer vision test passed - {len(hotspots)} hotspots detected")
            return True
            
        except Exception as e:
            print(f"   ❌ Computer vision test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_taiwan_government_platform(self):
        """Test Taiwan Government Collaboration Platform"""
        print("\n🇹🇼 Testing Taiwan Government Platform")
        
        try:
            platform = await create_taiwan_collaboration_platform()
            
            # Test stakeholder registration
            company_id = await platform.register_stakeholder(
                name="Test Marine Tech Company",
                stakeholder_type=platform.StakeholderType.PRIVATE_COMPANY,
                contact_info={"email": "<EMAIL>", "phone": "+886-2-1234-5678"},
                capabilities=["ai_technology", "satellite_monitoring", "data_analytics"]
            )
            assert company_id is not None, "Should return stakeholder ID"
            
            # Test project creation
            project_id = await platform.create_collaboration_project(
                title="Test AI Marine Debris Detection Project",
                description="Test project for marine debris detection using AI",
                objectives=["Deploy AI algorithms", "Train local teams", "Monitor effectiveness"],
                target_areas=[(120.0, 22.0, 122.0, 25.0)],  # Taiwan waters
                lead_agency_id=list(platform.stakeholders.keys())[0],
                stakeholder_ids=list(platform.stakeholders.keys()),
                budget={"total": 1000000, "government": 600000, "private": 400000}
            )
            assert project_id is not None, "Should return project ID"
            
            # Test data sharing agreement
            agreement_id = await platform.create_data_sharing_agreement(
                parties=list(platform.stakeholders.keys())[:2],
                data_types=["marine_debris_data", "vessel_tracking", "environmental_monitoring"],
                access_levels={"read": "all", "write": "restricted"},
                usage_restrictions=["research_only", "non_commercial"]
            )
            assert agreement_id is not None, "Should return agreement ID"
            
            # Test collaboration report
            report = await platform.generate_collaboration_report()
            assert 'platform_overview' in report, "Report should have overview"
            assert 'stakeholder_distribution' in report, "Should have stakeholder analysis"
            assert 'recent_projects' in report, "Should include recent projects"
            
            print(f"   ✅ Taiwan platform test passed - {len(platform.stakeholders)} stakeholders")
            return True
            
        except Exception as e:
            print(f"   ❌ Taiwan platform test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_integrated_workflow(self, test_area_bbox):
        """Test integrated workflow across all components"""
        print("\n🔄 Testing Integrated Workflow")
        
        try:
            # Step 1: Climate analysis
            climate_agent = ClimateMarineAgent()
            climate_report = await climate_agent.generate_climate_report(test_area_bbox)
            
            # Step 2: Hotspot detection
            hotspots = await detect_debris_hotspots(test_area_bbox, analysis_days=7)
            
            # Step 3: AI agent analysis
            ai_agent = await create_marine_debris_agent()
            management_report = await ai_agent.generate_management_report(test_area_bbox)
            
            # Step 4: Dashboard integration
            monitoring_areas = [{
                'id': 'integrated_test',
                'name': 'Integrated Test Area',
                'bbox': test_area_bbox,
                'priority': 'high'
            }]
            dashboard = await create_debris_dashboard(monitoring_areas)
            dashboard_data = await dashboard.collect_real_time_data()
            
            # Step 5: Taiwan platform coordination
            platform = await create_taiwan_collaboration_platform()
            platform_report = await platform.generate_collaboration_report()
            
            # Validate integration
            assert 'report_id' in climate_report, "Climate report should be generated"
            assert isinstance(hotspots, list), "Hotspots should be detected"
            assert 'report_id' in management_report, "Management report should be generated"
            assert hasattr(dashboard_data, 'metrics'), "Dashboard should collect data"
            assert 'platform_overview' in platform_report, "Platform should be operational"
            
            print("   ✅ Integrated workflow test passed - All components working together")
            return True
            
        except Exception as e:
            print(f"   ❌ Integrated workflow test failed: {e}")
            return False


async def run_comprehensive_tests():
    """Run all marine conservation integration tests"""
    print("🧪 Starting Comprehensive Marine Conservation Integration Tests")
    print("=" * 70)
    
    test_suite = TestMarineConservationIntegration()
    test_area = (2.0, 41.0, 3.0, 42.0)  # Mediterranean Sea
    taiwan_area = (119.0, 23.0, 121.0, 25.0)  # Taiwan Strait
    
    results = {}
    
    # Run individual component tests
    results['climate_agent'] = await test_suite.test_climate_marine_agent(test_area)
    results['dashboard'] = await test_suite.test_debris_tracking_dashboard(test_area, taiwan_area)
    results['ai_agent'] = await test_suite.test_marine_debris_ai_agent(test_area)
    results['computer_vision'] = await test_suite.test_computer_vision_hotspot_detection(test_area)
    results['taiwan_platform'] = await test_suite.test_taiwan_government_platform()
    results['integrated_workflow'] = await test_suite.test_integrated_workflow(test_area)
    
    # Summary
    print("\n" + "=" * 70)
    print("🏆 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL MARINE CONSERVATION COMPONENTS WORKING CORRECTLY!")
        print("🚀 Ready for production deployment with Taiwan government collaboration")
    else:
        print(f"\n⚠️  {total - passed} tests failed - review implementation")
    
    return results


if __name__ == "__main__":
    # Run tests
    results = asyncio.run(run_comprehensive_tests())
    
    # Exit with appropriate code
    if all(results.values()):
        print("\n✅ All tests passed - Marine conservation integration complete!")
        exit(0)
    else:
        print("\n❌ Some tests failed - check implementation")
        exit(1)
