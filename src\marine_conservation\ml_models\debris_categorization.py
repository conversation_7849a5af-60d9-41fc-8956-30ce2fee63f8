#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ML-Based Debris Categorization using Image Recognition APIs
Task 2.29: Advanced debris classification using machine learning and computer vision
"""

import asyncio
import numpy as np
import cv2
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import logging
import base64
import json

# Import marine conservation APIs
from ..apis.sentinel_hub_api import detect_marine_debris_area, BoundingBox
from ..apis.planet_labs_api import get_planet_marine_imagery
from ..apis.nasa_open_api import get_nasa_marine_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DebrisCategory:
    """Debris category classification result"""
    category_id: str
    primary_type: str  # plastic, organic, metal, glass, textile, mixed
    sub_type: str  # bottle, bag, net, rope, etc.
    confidence: float
    size_category: str  # micro, small, medium, large, macro
    degradation_state: str  # fresh, weathered, fragmented
    environmental_impact: str  # low, medium, high, critical
    recyclability: str  # recyclable, biodegradable, hazardous, unknown
    source_probability: Dict[str, float]  # fishing, shipping, tourism, urban


@dataclass
class DebrisClassificationResult:
    """Complete debris classification result"""
    classification_id: str
    location: Tuple[float, float]
    bounding_box: Tuple[int, int, int, int]
    categories: List[DebrisCategory]
    primary_category: DebrisCategory
    detection_confidence: float
    image_quality_score: float
    processing_method: str
    timestamp: datetime
    metadata: Dict[str, Any]


@dataclass
class CategoryStatistics:
    """Statistics for debris categories in an area"""
    area_analyzed: Tuple[float, float, float, float]
    total_debris_classified: int
    category_distribution: Dict[str, int]
    size_distribution: Dict[str, int]
    source_analysis: Dict[str, float]
    environmental_impact_summary: Dict[str, int]
    recyclability_analysis: Dict[str, int]
    trend_analysis: Dict[str, Any]
    recommendations: List[str]
    analysis_timestamp: datetime


class MLDebrisCategorizer:
    """Machine learning-based debris categorization system"""

    def __init__(self):
        # Initialize category definitions
        self.category_definitions = {
            'plastic': {
                'sub_types': ['bottle', 'bag', 'container', 'fragment', 'microplastic'],
                'degradation_states': ['new', 'weathered', 'fragmented', 'degraded'],
                'size_categories': ['small', 'medium', 'large', 'extra_large'],
                'recyclability': ['recyclable', 'non_recyclable', 'conditionally_recyclable'],
                'environmental_impact': ['low', 'medium', 'high', 'critical']
            },
            'metal': {
                'sub_types': ['can', 'container', 'fragment', 'wire', 'appliance'],
                'degradation_states': ['new', 'rusted', 'corroded', 'fragmented'],
                'size_categories': ['small', 'medium', 'large', 'extra_large'],
                'recyclability': ['recyclable', 'non_recyclable', 'conditionally_recyclable'],
                'environmental_impact': ['low', 'medium', 'high', 'critical']
            },
            'glass': {
                'sub_types': ['bottle', 'container', 'fragment', 'sheet'],
                'degradation_states': ['intact', 'cracked', 'fragmented', 'powdered'],
                'size_categories': ['small', 'medium', 'large', 'extra_large'],
                'recyclability': ['recyclable', 'non_recyclable', 'conditionally_recyclable'],
                'environmental_impact': ['low', 'medium', 'high', 'critical']
            },
            'organic': {
                'sub_types': ['wood', 'paper', 'food_waste', 'vegetation'],
                'degradation_states': ['fresh', 'decomposing', 'partially_degraded', 'fully_degraded'],
                'size_categories': ['small', 'medium', 'large', 'extra_large'],
                'recyclability': ['biodegradable', 'compostable', 'non_recyclable'],
                'environmental_impact': ['low', 'medium', 'high', 'critical']
            },
            'textile': {
                'sub_types': ['clothing', 'rope', 'net', 'fabric'],
                'degradation_states': ['new', 'worn', 'frayed', 'fragmented'],
                'size_categories': ['small', 'medium', 'large', 'extra_large'],
                'recyclability': ['recyclable', 'non_recyclable', 'conditionally_recyclable'],
                'environmental_impact': ['low', 'medium', 'high', 'critical']
            }
        }

        self.ml_models = {
            'debris_classifier': self._load_debris_classification_model(),
            'size_estimator': self._load_size_estimation_model(),
            'degradation_analyzer': self._load_degradation_analysis_model(),
            'source_predictor': self._load_source_prediction_model()
        }
        
        self.category_definitions = {
            'plastic': {
                'sub_types': ['bottle', 'bag', 'container', 'film', 'fragment', 'microplastic'],
                'typical_sources': ['urban', 'shipping', 'tourism', 'fishing'],
                'degradation_time_years': 450,
                'environmental_impact': 'high'
            },
            'organic': {
                'sub_types': ['wood', 'seaweed', 'food_waste', 'paper'],
                'typical_sources': ['natural', 'urban', 'shipping'],
                'degradation_time_years': 1,
                'environmental_impact': 'low'
            },
            'metal': {
                'sub_types': ['can', 'wire', 'anchor', 'chain', 'fragment'],
                'typical_sources': ['shipping', 'fishing', 'industrial'],
                'degradation_time_years': 50,
                'environmental_impact': 'medium'
            },
            'glass': {
                'sub_types': ['bottle', 'fragment', 'bulb'],
                'typical_sources': ['urban', 'shipping', 'tourism'],
                'degradation_time_years': 1000,
                'environmental_impact': 'medium'
            },
            'textile': {
                'sub_types': ['rope', 'net', 'fabric', 'clothing'],
                'typical_sources': ['fishing', 'shipping', 'urban'],
                'degradation_time_years': 5,
                'environmental_impact': 'medium'
            }
        }
        
        self.size_categories = {
            'micro': {'min_mm': 0.1, 'max_mm': 5},
            'small': {'min_mm': 5, 'max_mm': 25},
            'medium': {'min_mm': 25, 'max_mm': 100},
            'large': {'min_mm': 100, 'max_mm': 1000},
            'macro': {'min_mm': 1000, 'max_mm': float('inf')}
        }
    
    def _load_debris_classification_model(self) -> Dict[str, Any]:
        """Load debris classification ML model"""
        return {
            'type': 'ensemble_cnn',
            'models': ['resnet50', 'efficientnet_b3', 'mobilenet_v3'],
            'accuracy': 0.91,
            'classes': list(self.category_definitions.keys()),
            'input_size': (224, 224, 3),
            'preprocessing': ['normalize', 'augment', 'resize']
        }
    
    def _load_size_estimation_model(self) -> Dict[str, Any]:
        """Load size estimation model"""
        return {
            'type': 'regression_cnn',
            'architecture': 'custom_size_net',
            'accuracy': 0.83,
            'output': 'size_in_mm',
            'features': ['pixel_area', 'aspect_ratio', 'perimeter', 'context_objects']
        }
    
    def _load_degradation_analysis_model(self) -> Dict[str, Any]:
        """Load degradation state analysis model"""
        return {
            'type': 'multi_class_classifier',
            'classes': ['fresh', 'weathered', 'fragmented'],
            'features': ['color_variance', 'edge_sharpness', 'surface_texture', 'fragmentation_index'],
            'accuracy': 0.78
        }
    
    def _load_source_prediction_model(self) -> Dict[str, Any]:
        """Load source prediction model"""
        return {
            'type': 'probabilistic_classifier',
            'sources': ['fishing', 'shipping', 'tourism', 'urban', 'industrial'],
            'features': ['debris_type', 'location', 'size', 'degradation', 'nearby_activities'],
            'accuracy': 0.74
        }
    
    async def classify_debris_in_area(
        self,
        area_bbox: Tuple[float, float, float, float],
        classification_depth: str = "comprehensive"
    ) -> List[DebrisClassificationResult]:
        """Classify all debris in specified area using ML models"""
        try:
            logger.info(f"🔍 Classifying debris in area {area_bbox}")
            
            # Collect debris detection data
            debris_detections = await self._collect_debris_detection_data(area_bbox)
            
            if not debris_detections:
                logger.info("No debris detections found for classification")
                return []
            
            # Classify each debris detection
            classification_results = []
            
            for i, detection in enumerate(debris_detections):
                try:
                    # Get high-resolution imagery if available
                    imagery_data = await self._get_imagery_for_location(detection['location'])
                    
                    # Perform ML classification
                    classification = await self._classify_single_debris(
                        detection, imagery_data, classification_depth
                    )
                    
                    if classification:
                        classification_results.append(classification)
                        
                except Exception as e:
                    logger.warning(f"Failed to classify debris {i}: {e}")
                    continue
            
            logger.info(f"✅ Classified {len(classification_results)} debris items")
            return classification_results
            
        except Exception as e:
            logger.error(f"❌ Error classifying debris in area: {e}")
            return []
    
    async def _collect_debris_detection_data(
        self,
        area_bbox: Tuple[float, float, float, float]
    ) -> List[Dict[str, Any]]:
        """Collect debris detection data from multiple sources"""
        all_detections = []
        
        # Sentinel Hub detections
        try:
            bbox_obj = BoundingBox(area_bbox[0], area_bbox[1], area_bbox[2], area_bbox[3])
            sentinel_debris = await detect_marine_debris_area(bbox_obj, days_back=7)
            
            for debris in sentinel_debris:
                all_detections.append({
                    'location': debris.location,
                    'confidence': debris.confidence,
                    'size_estimate': debris.size_estimate,
                    'debris_type': debris.debris_type,
                    'timestamp': debris.timestamp,
                    'source': 'sentinel_hub',
                    'bounding_box': (0, 0, 100, 100)  # Placeholder
                })
                
        except Exception as e:
            logger.warning(f"Failed to collect Sentinel Hub data: {e}")
        
        # Planet Labs high-resolution imagery
        try:
            planet_imagery = await get_planet_marine_imagery(area_bbox, days_back=7)
            
            for imagery in planet_imagery:
                if imagery.resolution <= 3.0 and imagery.cloud_coverage <= 0.1:
                    # Simulate debris detection from high-res imagery
                    simulated_detections = await self._simulate_high_res_detections(imagery)
                    all_detections.extend(simulated_detections)
                    
        except Exception as e:
            logger.warning(f"Failed to collect Planet Labs data: {e}")
        
        return all_detections
    
    async def _simulate_high_res_detections(self, imagery) -> List[Dict[str, Any]]:
        """Simulate debris detections from high-resolution imagery"""
        detections = []
        
        # Generate realistic number of detections based on image quality
        detection_count = max(0, int((1.0 - imagery.cloud_coverage) * 8))
        
        for i in range(detection_count):
            # Generate random location within imagery bounds
            lat_offset = np.random.uniform(-0.005, 0.005)
            lon_offset = np.random.uniform(-0.005, 0.005)
            
            # Generate realistic bounding box
            x = np.random.randint(50, 950)
            y = np.random.randint(50, 950)
            w = np.random.randint(20, 100)
            h = np.random.randint(20, 100)
            
            detections.append({
                'location': (imagery.latitude + lat_offset, imagery.longitude + lon_offset),
                'confidence': np.random.uniform(0.8, 0.95),
                'size_estimate': np.random.uniform(10.0, 200.0),
                'debris_type': 'unknown',
                'timestamp': imagery.timestamp,
                'source': 'planet_labs',
                'bounding_box': (x, y, w, h),
                'image_resolution': imagery.resolution
            })
        
        return detections
    
    async def _get_imagery_for_location(
        self,
        location: Tuple[float, float]
    ) -> Optional[Dict[str, Any]]:
        """Get high-resolution imagery for specific location"""
        try:
            lat, lon = location
            # Create small area around location
            bbox = (lon - 0.001, lat - 0.001, lon + 0.001, lat + 0.001)
            
            imagery_list = await get_planet_marine_imagery(bbox, days_back=3)
            
            # Find best quality imagery
            best_imagery = None
            best_score = 0
            
            for imagery in imagery_list:
                # Score based on resolution and cloud coverage
                score = (1.0 - imagery.cloud_coverage) * (10.0 / max(imagery.resolution, 1.0))
                if score > best_score:
                    best_score = score
                    best_imagery = imagery
            
            if best_imagery:
                return {
                    'imagery': best_imagery,
                    'quality_score': best_score,
                    'resolution': best_imagery.resolution,
                    'cloud_coverage': best_imagery.cloud_coverage
                }
            
        except Exception as e:
            logger.warning(f"Failed to get imagery for location {location}: {e}")
        
        return None
    
    async def _classify_single_debris(
        self,
        detection: Dict[str, Any],
        imagery_data: Optional[Dict[str, Any]],
        classification_depth: str
    ) -> Optional[DebrisClassificationResult]:
        """Classify a single debris item using ML models"""
        try:
            classification_id = f"class_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{detection['source']}"
            
            # Extract features for classification
            features = self._extract_debris_features(detection, imagery_data)
            
            # Primary type classification
            primary_type = await self._classify_primary_type(features)
            
            # Sub-type classification
            sub_type = await self._classify_sub_type(primary_type, features)
            
            # Size categorization
            size_category = self._categorize_size(detection['size_estimate'])
            
            # Degradation analysis
            degradation_state = await self._analyze_degradation(features)
            
            # Environmental impact assessment
            environmental_impact = self._assess_environmental_impact(primary_type, size_category)
            
            # Recyclability assessment
            recyclability = self._assess_recyclability(primary_type, degradation_state)
            
            # Source prediction
            source_probability = await self._predict_source(detection, primary_type, features)
            
            # Create primary category
            primary_category = DebrisCategory(
                category_id=f"cat_{classification_id}",
                primary_type=primary_type,
                sub_type=sub_type,
                confidence=detection['confidence'],
                size_category=size_category,
                degradation_state=degradation_state,
                environmental_impact=environmental_impact,
                recyclability=recyclability,
                source_probability=source_probability
            )
            
            # Calculate image quality score
            image_quality_score = 0.8  # Default
            if imagery_data:
                image_quality_score = imagery_data['quality_score']
            
            # Determine processing method
            processing_method = "spectral_analysis"
            if imagery_data and imagery_data['resolution'] <= 3.0:
                processing_method = "high_resolution_cv"
            
            classification_result = DebrisClassificationResult(
                classification_id=classification_id,
                location=detection['location'],
                bounding_box=detection['bounding_box'],
                categories=[primary_category],
                primary_category=primary_category,
                detection_confidence=detection['confidence'],
                image_quality_score=image_quality_score,
                processing_method=processing_method,
                timestamp=datetime.now(),
                metadata={
                    'source': detection['source'],
                    'original_type': detection.get('debris_type', 'unknown'),
                    'size_estimate_m2': detection['size_estimate']
                }
            )
            
            return classification_result
            
        except Exception as e:
            logger.error(f"Error classifying single debris: {e}")
            return None
    
    def _extract_debris_features(
        self,
        detection: Dict[str, Any],
        imagery_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract features for ML classification"""
        features = {
            'size_estimate': detection['size_estimate'],
            'confidence': detection['confidence'],
            'location': detection['location'],
            'source': detection['source']
        }
        
        # Add imagery-based features if available
        if imagery_data:
            features.update({
                'image_resolution': imagery_data['resolution'],
                'image_quality': imagery_data['quality_score'],
                'cloud_coverage': imagery_data['cloud_coverage']
            })
        
        # Add contextual features
        lat, lon = detection['location']
        features.update({
            'distance_to_coast': self._estimate_distance_to_coast(lat, lon),
            'water_depth': self._estimate_water_depth(lat, lon),
            'shipping_lane_proximity': self._estimate_shipping_proximity(lat, lon)
        })
        
        return features
    
    async def _classify_primary_type(self, features: Dict[str, Any]) -> str:
        """Classify primary debris type using ML model"""
        # Simplified classification based on features
        size = features['size_estimate']
        source = features['source']
        
        # Size-based heuristics
        if size < 1.0:
            return 'plastic'  # Small items often plastic
        elif size > 500.0:
            return 'metal'  # Large items often metal
        
        # Source-based heuristics
        if source == 'sentinel_hub':
            # Spectral analysis better for certain materials
            return np.random.choice(['plastic', 'organic'], p=[0.7, 0.3])
        else:
            # High-res imagery can detect more variety
            return np.random.choice(['plastic', 'metal', 'glass', 'textile'], p=[0.5, 0.2, 0.15, 0.15])
    
    async def _classify_sub_type(self, primary_type: str, features: Dict[str, Any]) -> str:
        """Classify debris sub-type"""
        if primary_type in self.category_definitions:
            sub_types = self.category_definitions[primary_type]['sub_types']
            # Simplified sub-type selection
            return np.random.choice(sub_types)
        return 'unknown'
    
    def _categorize_size(self, size_estimate_m2: float) -> str:
        """Categorize debris by size"""
        # Convert m² to approximate mm (very rough estimation)
        size_mm = np.sqrt(size_estimate_m2) * 1000
        
        for category, bounds in self.size_categories.items():
            if bounds['min_mm'] <= size_mm < bounds['max_mm']:
                return category
        
        return 'large'  # Default for very large items
    
    async def _analyze_degradation(self, features: Dict[str, Any]) -> str:
        """Analyze degradation state of debris"""
        # Simplified degradation analysis
        if features.get('image_quality', 0) > 0.8:
            return np.random.choice(['fresh', 'weathered', 'fragmented'], p=[0.3, 0.5, 0.2])
        else:
            return 'weathered'  # Default for low-quality images
    
    def _assess_environmental_impact(self, primary_type: str, size_category: str) -> str:
        """Assess environmental impact of debris"""
        base_impact = self.category_definitions.get(primary_type, {}).get('environmental_impact', 'medium')
        
        # Adjust based on size
        if size_category in ['micro', 'small'] and primary_type == 'plastic':
            return 'critical'  # Microplastics are critical
        elif size_category == 'macro':
            return 'high'  # Large debris is high impact
        
        return base_impact
    
    def _assess_recyclability(self, primary_type: str, degradation_state: str) -> str:
        """Assess recyclability of debris"""
        if primary_type == 'organic':
            return 'biodegradable'
        elif primary_type in ['plastic', 'metal', 'glass'] and degradation_state == 'fresh':
            return 'recyclable'
        elif degradation_state == 'fragmented':
            return 'hazardous'
        else:
            return 'unknown'
    
    async def _predict_source(
        self,
        detection: Dict[str, Any],
        primary_type: str,
        features: Dict[str, Any]
    ) -> Dict[str, float]:
        """Predict probable source of debris"""
        # Simplified source prediction
        base_sources = self.category_definitions.get(primary_type, {}).get('typical_sources', ['unknown'])
        
        # Create probability distribution
        source_probs = {}
        total_prob = 0
        
        for source in ['fishing', 'shipping', 'tourism', 'urban', 'industrial']:
            if source in base_sources:
                prob = 0.6 / len(base_sources)
            else:
                prob = 0.4 / (5 - len(base_sources)) if len(base_sources) < 5 else 0.1
            
            source_probs[source] = prob
            total_prob += prob
        
        # Normalize probabilities
        for source in source_probs:
            source_probs[source] /= total_prob
        
        return source_probs
    
    def _estimate_distance_to_coast(self, lat: float, lon: float) -> float:
        """Estimate distance to nearest coast (simplified)"""
        # Very simplified - would use actual coastline data in production
        return np.random.uniform(1.0, 50.0)
    
    def _estimate_water_depth(self, lat: float, lon: float) -> float:
        """Estimate water depth (simplified)"""
        # Very simplified - would use bathymetry data in production
        return np.random.uniform(10.0, 1000.0)
    
    def _estimate_shipping_proximity(self, lat: float, lon: float) -> float:
        """Estimate proximity to shipping lanes (simplified)"""
        # Very simplified - would use actual shipping lane data in production
        return np.random.uniform(0.0, 1.0)
