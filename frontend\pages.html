<!-- Water Quality Page -->
<div class="page" id="water-quality-page">
    <div class="page-header">
        <h1><i class="fas fa-tint"></i> Water Quality Management</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Add Sensor
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Data
            </button>
            <button class="btn-secondary">
                <i class="fas fa-cog"></i> Configure
            </button>
        </div>
    </div>

    <!-- Water Quality Dashboard -->
    <div class="water-quality-dashboard">
        <!-- Real-time Metrics -->
        <div class="metrics-grid">
            <div class="metric-card ph-card">
                <div class="metric-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">7.2</div>
                    <div class="metric-label">pH Level</div>
                    <div class="metric-status normal">Normal</div>
                </div>
                <div class="metric-chart">
                    <canvas id="phChart"></canvas>
                </div>
            </div>

            <div class="metric-card turbidity-card">
                <div class="metric-icon">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">1.5 NTU</div>
                    <div class="metric-label">Turbidity</div>
                    <div class="metric-status good">Good</div>
                </div>
                <div class="metric-chart">
                    <canvas id="turbidityChart"></canvas>
                </div>
            </div>

            <div class="metric-card chlorine-card">
                <div class="metric-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">1.0 mg/L</div>
                    <div class="metric-label">Chlorine Residual</div>
                    <div class="metric-status normal">Normal</div>
                </div>
                <div class="metric-chart">
                    <canvas id="chlorineChart"></canvas>
                </div>
            </div>

            <div class="metric-card bacteria-card">
                <div class="metric-icon">
                    <i class="fas fa-microscope"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">0 CFU</div>
                    <div class="metric-label">E. Coli</div>
                    <div class="metric-status excellent">Excellent</div>
                </div>
                <div class="metric-chart">
                    <canvas id="bacteriaChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Water Quality Trends -->
        <div class="panel trends-panel">
            <div class="panel-header">
                <h3>Water Quality Trends</h3>
                <div class="panel-controls">
                    <select class="time-range-select">
                        <option value="24h">Last 24 Hours</option>
                        <option value="7d">Last 7 Days</option>
                        <option value="30d">Last 30 Days</option>
                        <option value="90d">Last 90 Days</option>
                    </select>
                </div>
            </div>
            <div class="trends-chart">
                <canvas id="waterQualityTrendsChart"></canvas>
            </div>
        </div>

        <!-- Sensor Network Map -->
        <div class="panel map-panel">
            <div class="panel-header">
                <h3>Sensor Network</h3>
                <div class="panel-controls">
                    <button class="btn-icon active" data-layer="sensors">
                        <i class="fas fa-satellite-dish"></i>
                    </button>
                    <button class="btn-icon" data-layer="quality">
                        <i class="fas fa-tint"></i>
                    </button>
                    <button class="btn-icon" data-layer="alerts">
                        <i class="fas fa-exclamation-triangle"></i>
                    </button>
                </div>
            </div>
            <div class="sensor-map" id="sensorMap"></div>
        </div>

        <!-- Quality Alerts -->
        <div class="panel alerts-panel">
            <div class="panel-header">
                <h3>Quality Alerts</h3>
                <div class="alert-summary">
                    <span class="alert-count critical">2 Critical</span>
                    <span class="alert-count warning">5 Warning</span>
                    <span class="alert-count info">12 Info</span>
                </div>
            </div>
            <div class="alerts-list">
                <div class="alert-item critical">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">pH Level Critical - Sector 7</div>
                        <div class="alert-description">pH level dropped to 5.8, immediate attention required</div>
                        <div class="alert-meta">
                            <span class="alert-time">3 minutes ago</span>
                            <span class="alert-sensor">Sensor WQ-007</span>
                        </div>
                    </div>
                    <div class="alert-actions">
                        <button class="btn-alert-action">Acknowledge</button>
                        <button class="btn-alert-action">View Details</button>
                    </div>
                </div>

                <div class="alert-item warning">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">Turbidity Elevated - Sector 3</div>
                        <div class="alert-description">Turbidity reading 3.2 NTU, above normal threshold</div>
                        <div class="alert-meta">
                            <span class="alert-time">15 minutes ago</span>
                            <span class="alert-sensor">Sensor WQ-003</span>
                        </div>
                    </div>
                    <div class="alert-actions">
                        <button class="btn-alert-action">Acknowledge</button>
                        <button class="btn-alert-action">View Details</button>
                    </div>
                </div>

                <div class="alert-item info">
                    <div class="alert-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">Routine Calibration Complete</div>
                        <div class="alert-description">Sensor WQ-012 calibration completed successfully</div>
                        <div class="alert-meta">
                            <span class="alert-time">1 hour ago</span>
                            <span class="alert-sensor">Sensor WQ-012</span>
                        </div>
                    </div>
                    <div class="alert-actions">
                        <button class="btn-alert-action">Acknowledge</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Energy Grid Page -->
<div class="page" id="energy-grid-page">
    <div class="page-header">
        <h1><i class="fas fa-bolt"></i> Energy Grid Management</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Add Grid Point
            </button>
            <button class="btn-secondary">
                <i class="fas fa-sync"></i> Optimize Grid
            </button>
            <button class="btn-secondary">
                <i class="fas fa-chart-line"></i> Energy Report
            </button>
        </div>
    </div>

    <!-- Energy Dashboard -->
    <div class="energy-dashboard">
        <!-- Energy Overview Cards -->
        <div class="energy-overview">
            <div class="energy-card consumption">
                <div class="energy-icon">
                    <i class="fas fa-plug"></i>
                </div>
                <div class="energy-content">
                    <div class="energy-value">2,847 kWh</div>
                    <div class="energy-label">Current Consumption</div>
                    <div class="energy-change positive">-12% from yesterday</div>
                </div>
                <div class="energy-gauge">
                    <canvas id="consumptionGauge"></canvas>
                </div>
            </div>

            <div class="energy-card generation">
                <div class="energy-icon">
                    <i class="fas fa-solar-panel"></i>
                </div>
                <div class="energy-content">
                    <div class="energy-value">1,923 kWh</div>
                    <div class="energy-label">Renewable Generation</div>
                    <div class="energy-change positive">+8% from yesterday</div>
                </div>
                <div class="energy-gauge">
                    <canvas id="generationGauge"></canvas>
                </div>
            </div>

            <div class="energy-card efficiency">
                <div class="energy-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="energy-content">
                    <div class="energy-value">87.3%</div>
                    <div class="energy-label">Grid Efficiency</div>
                    <div class="energy-change positive">+2.1% this week</div>
                </div>
                <div class="energy-gauge">
                    <canvas id="efficiencyGauge"></canvas>
                </div>
            </div>

            <div class="energy-card cost">
                <div class="energy-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="energy-content">
                    <div class="energy-value">$342.18</div>
                    <div class="energy-label">Daily Cost</div>
                    <div class="energy-change negative">+$23.45 from yesterday</div>
                </div>
                <div class="energy-gauge">
                    <canvas id="costGauge"></canvas>
                </div>
            </div>
        </div>

        <!-- Grid Topology -->
        <div class="panel grid-topology-panel">
            <div class="panel-header">
                <h3>Grid Topology</h3>
                <div class="panel-controls">
                    <button class="btn-icon active" data-view="topology">
                        <i class="fas fa-project-diagram"></i>
                    </button>
                    <button class="btn-icon" data-view="load">
                        <i class="fas fa-weight-hanging"></i>
                    </button>
                    <button class="btn-icon" data-view="flow">
                        <i class="fas fa-stream"></i>
                    </button>
                </div>
            </div>
            <div class="grid-visualization">
                <svg id="gridTopology" width="100%" height="400"></svg>
            </div>
        </div>

        <!-- Energy Flow Chart -->
        <div class="panel energy-flow-panel">
            <div class="panel-header">
                <h3>Energy Flow Analysis</h3>
                <div class="panel-controls">
                    <select class="time-range-select">
                        <option value="realtime">Real-time</option>
                        <option value="1h">Last Hour</option>
                        <option value="24h">Last 24 Hours</option>
                        <option value="7d">Last 7 Days</option>
                    </select>
                </div>
            </div>
            <div class="energy-flow-chart">
                <canvas id="energyFlowChart"></canvas>
            </div>
        </div>
    </div>
</div>
