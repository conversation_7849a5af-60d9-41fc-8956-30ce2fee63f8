"""
Test script for NASA Climate Data API integration.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.data.collectors.nasa_collector import NASAClimateCollector, get_nasa_climate_for_location, get_global_climate_indicators
from src.utils.config import get_settings


async def test_nasa_api_connection():
    """Test NASA API connection and basic functionality."""
    print("🧪 Testing NASA Climate Data API Integration...")
    
    try:
        settings = get_settings()
        
        if not settings.NASA_API_KEY:
            print("⚠️ NASA API key not configured, using DEMO_KEY")
            print("For full functionality, get a free API key from: https://api.nasa.gov/")
        else:
            print(f"✅ NASA API key found: {settings.NASA_API_KEY[:10]}...")
        
        # Initialize collector
        collector = NASAClimateCollector()
        init_success = await collector.initialize()
        
        if not init_success:
            print("❌ Failed to initialize NASA collector")
            return False
        
        print("✅ NASA Climate Data collector initialized successfully")
        
        # Test APOD endpoint (always available)
        print("\n🔄 Testing NASA API connection with APOD...")
        
        # This is handled in the initialization test
        print("✅ NASA API connection test completed")
        
        await collector.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ NASA API connection test failed: {e}")
        return False


async def test_giss_temperature_data():
    """Test GISS global temperature data collection."""
    print("\n🧪 Testing GISS Global Temperature Data...")
    
    try:
        collector = NASAClimateCollector()
        await collector.initialize()
        
        print("🔄 Fetching GISS global temperature data...")
        temp_data = await collector.get_giss_temperature_data()
        
        if temp_data:
            print("✅ GISS temperature data retrieved successfully")
            print(f"📊 Dataset: {temp_data.get('dataset', 'Unknown')}")
            print(f"📝 Description: {temp_data.get('description', 'N/A')}")
            
            data_points = temp_data.get('data', [])
            if data_points:
                print(f"📈 Data points: {len(data_points)}")
                print("🌡️ Recent temperature anomalies:")
                
                for point in data_points[-5:]:  # Show last 5 years
                    year = point.get('year', 'Unknown')
                    anomaly = point.get('temperature_anomaly', 'N/A')
                    print(f"  {year}: {anomaly:+.2f}°C" if isinstance(anomaly, (int, float)) else f"  {year}: {anomaly}")
        else:
            print("⚠️ GISS temperature data not available")
        
        await collector.shutdown()
        return temp_data is not None
        
    except Exception as e:
        print(f"❌ GISS temperature data test failed: {e}")
        return False


async def test_nasa_power_data():
    """Test NASA POWER meteorological data."""
    print("\n🧪 Testing NASA POWER Meteorological Data...")
    
    try:
        collector = NASAClimateCollector()
        await collector.initialize()
        
        # Test location: London
        lat, lon = 51.5074, -0.1278
        
        # Get data for last 7 days
        from datetime import datetime, timedelta
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y%m%d")
        
        print(f"🔄 Fetching NASA POWER data for London ({lat}, {lon})...")
        print(f"📅 Date range: {start_date} to {end_date}")
        
        power_data = await collector.get_power_data(lat, lon, start_date, end_date)
        
        if power_data:
            print("✅ NASA POWER data retrieved successfully")
            print(f"📍 Location: {power_data.location}")
            print(f"🗂️ Dataset: {power_data.dataset}")
            print(f"📊 Parameters available: {len(power_data.parameters)}")
            
            # Show some key parameters
            params = power_data.parameters
            if params:
                print("🌡️ Key meteorological parameters:")
                for param_name, param_data in list(params.items())[:3]:  # Show first 3
                    if isinstance(param_data, dict) and param_data:
                        sample_value = next(iter(param_data.values()))
                        print(f"  {param_name}: {sample_value} (sample value)")
        else:
            print("⚠️ NASA POWER data not available (may require API key)")
        
        await collector.shutdown()
        return power_data is not None
        
    except Exception as e:
        print(f"❌ NASA POWER data test failed: {e}")
        return False


async def test_earth_imagery():
    """Test NASA Earth Imagery API."""
    print("\n🧪 Testing NASA Earth Imagery...")
    
    try:
        collector = NASAClimateCollector()
        await collector.initialize()
        
        # Test location: New York
        lat, lon = 40.7128, -74.0060
        
        print(f"🔄 Fetching Earth imagery for New York ({lat}, {lon})...")
        
        imagery_data = await collector.get_earth_imagery(lat, lon)
        
        if imagery_data:
            print("✅ Earth imagery data retrieved successfully")
            print(f"📍 Coordinates: {imagery_data.get('latitude')}, {imagery_data.get('longitude')}")
            print(f"📅 Date: {imagery_data.get('date')}")
            print(f"🖼️ Image URL available: {bool(imagery_data.get('image_url'))}")
        else:
            print("⚠️ Earth imagery not available (may require API key)")
        
        await collector.shutdown()
        return imagery_data is not None
        
    except Exception as e:
        print(f"❌ Earth imagery test failed: {e}")
        return False


async def test_climate_summary():
    """Test comprehensive climate summary."""
    print("\n🧪 Testing NASA Climate Summary...")
    
    try:
        collector = NASAClimateCollector()
        await collector.initialize()
        
        # Test location: Tokyo
        lat, lon = 35.6762, 139.6503
        location_name = "Tokyo"
        
        print(f"🔄 Generating climate summary for {location_name}...")
        
        summary = await collector.get_climate_summary(lat, lon, location_name)
        
        if summary:
            print("✅ Climate summary generated successfully")
            print(f"📍 Location: {summary.get('location')}")
            print(f"📊 Data sources: {summary.get('data_sources', [])}")
            print(f"📈 Data availability score: {summary.get('data_availability_score', 0):.1%}")
            
            if summary.get('meteorological_data'):
                print("🌡️ Meteorological data: Available")
            
            if summary.get('earth_imagery'):
                print("🛰️ Earth imagery: Available")
            
            if summary.get('global_temperature_context'):
                print("🌍 Global temperature context: Available")
        else:
            print("⚠️ Climate summary could not be generated")
        
        await collector.shutdown()
        return bool(summary)
        
    except Exception as e:
        print(f"❌ Climate summary test failed: {e}")
        return False


async def test_global_indicators():
    """Test global climate indicators collection."""
    print("\n🧪 Testing Global Climate Indicators...")
    
    try:
        print("🔄 Collecting global climate indicators...")
        
        indicators = await get_global_climate_indicators()
        
        if indicators:
            print(f"✅ Collected {len(indicators)} global climate indicators")
            
            print("📊 Available indicators:")
            for indicator in indicators:
                name = indicator.get('indicator', 'Unknown')
                importance = indicator.get('importance', 'Unknown')
                description = indicator.get('description', 'No description')
                print(f"  • {name} ({importance}): {description[:60]}...")
        else:
            print("⚠️ No global climate indicators collected")
        
        return len(indicators) > 0
        
    except Exception as e:
        print(f"❌ Global indicators test failed: {e}")
        return False


async def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing Convenience Functions...")
    
    try:
        print("🔄 Testing get_nasa_climate_for_location function...")
        
        climate_data = await get_nasa_climate_for_location("Sydney", -33.8688, 151.2093)
        
        if climate_data:
            print("✅ Convenience function working correctly")
            print(f"📍 Location: {climate_data.get('location')}")
            print(f"📊 Data sources: {len(climate_data.get('data_sources', []))}")
            return True
        else:
            print("⚠️ Convenience function returned no data")
            return False
            
    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        return False


async def main():
    """Run all NASA Climate Data tests."""
    print("🚀 NASA Climate Data API Integration Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: API connection
    connection_result = await test_nasa_api_connection()
    test_results.append(("NASA API Connection", connection_result))
    
    # Test 2: GISS temperature data (always available)
    giss_result = await test_giss_temperature_data()
    test_results.append(("GISS Temperature Data", giss_result))
    
    # Test 3: NASA POWER data
    power_result = await test_nasa_power_data()
    test_results.append(("NASA POWER Data", power_result))
    
    # Test 4: Earth imagery
    imagery_result = await test_earth_imagery()
    test_results.append(("Earth Imagery", imagery_result))
    
    # Test 5: Climate summary
    summary_result = await test_climate_summary()
    test_results.append(("Climate Summary", summary_result))
    
    # Test 6: Global indicators
    indicators_result = await test_global_indicators()
    test_results.append(("Global Climate Indicators", indicators_result))
    
    # Test 7: Convenience functions
    convenience_result = await test_convenience_functions()
    test_results.append(("Convenience Functions", convenience_result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed >= 2:  # At least basic functionality working
        print("\n🎉 NASA Climate Data integration is functional!")
        print("Climate data collection capabilities are available.")
    elif passed > 0:
        print(f"\n⚠️ Partial functionality ({passed}/{total} tests passed)")
        print("Some NASA data sources are working.")
    else:
        print("\n❌ NASA integration needs attention.")
        print("Please check API configuration and internet connection.")
    
    print("\n📋 Next Steps:")
    if passed >= 2:
        print("  1. NASA climate data integration is ready!")
        print("  2. Consider getting a NASA API key for full functionality")
        print("  3. Climate data will enhance water treatment optimization")
    else:
        print("  1. Get a free NASA API key from: https://api.nasa.gov/")
        print("  2. Add NASA_API_KEY=your_key_here to your .env file")
        print("  3. Re-run this test")


if __name__ == "__main__":
    asyncio.run(main())
