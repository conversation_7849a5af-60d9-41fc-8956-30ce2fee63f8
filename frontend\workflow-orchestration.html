<!-- Workflow Orchestration Page -->
<div class="page" id="workflow-orchestration-page">
    <div class="page-header">
        <h1><i class="fas fa-project-diagram"></i> Workflow Orchestration</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Create Workflow
            </button>
            <button class="btn-secondary">
                <i class="fas fa-play"></i> Run All
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Workflows
            </button>
        </div>
    </div>

    <!-- Workflow Orchestration Dashboard -->
    <div class="workflow-orchestration-dashboard">
        <!-- Active Workflows -->
        <div class="panel active-workflows-panel">
            <div class="panel-header">
                <h3>Active Workflows</h3>
                <div class="workflow-stats">
                    <span class="workflow-stat">
                        <i class="fas fa-play"></i>
                        <span>8 Running</span>
                    </span>
                    <span class="workflow-stat">
                        <i class="fas fa-pause"></i>
                        <span>3 Paused</span>
                    </span>
                    <span class="workflow-stat">
                        <i class="fas fa-check"></i>
                        <span>247 Completed</span>
                    </span>
                </div>
            </div>
            <div class="active-workflows-content">
                <div class="workflow-item running">
                    <div class="workflow-header">
                        <div class="workflow-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="workflow-info">
                            <h4>Water Quality Analysis Pipeline</h4>
                            <div class="workflow-description">Automated water quality monitoring and analysis</div>
                            <div class="workflow-status running">Running</div>
                        </div>
                        <div class="workflow-actions">
                            <button class="btn-icon" title="Pause">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="btn-icon" title="Monitor">
                                <i class="fas fa-chart-line"></i>
                            </button>
                            <button class="btn-icon" title="Configure">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div class="workflow-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 67%"></div>
                        </div>
                        <span class="progress-text">Step 4 of 6: Data Analysis</span>
                    </div>
                    <div class="workflow-steps">
                        <div class="step completed">
                            <i class="fas fa-check"></i>
                            <span>Data Collection</span>
                        </div>
                        <div class="step completed">
                            <i class="fas fa-check"></i>
                            <span>Data Validation</span>
                        </div>
                        <div class="step completed">
                            <i class="fas fa-check"></i>
                            <span>Preprocessing</span>
                        </div>
                        <div class="step active">
                            <i class="fas fa-cog fa-spin"></i>
                            <span>Analysis</span>
                        </div>
                        <div class="step pending">
                            <i class="fas fa-clock"></i>
                            <span>Reporting</span>
                        </div>
                        <div class="step pending">
                            <i class="fas fa-clock"></i>
                            <span>Notification</span>
                        </div>
                    </div>
                    <div class="workflow-metrics">
                        <div class="metric">
                            <span class="metric-label">Duration</span>
                            <span class="metric-value">12m 34s</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Success Rate</span>
                            <span class="metric-value">98.7%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Last Run</span>
                            <span class="metric-value">5 min ago</span>
                        </div>
                    </div>
                </div>

                <div class="workflow-item running">
                    <div class="workflow-header">
                        <div class="workflow-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="workflow-info">
                            <h4>Energy Optimization Workflow</h4>
                            <div class="workflow-description">Continuous energy consumption optimization</div>
                            <div class="workflow-status running">Running</div>
                        </div>
                        <div class="workflow-actions">
                            <button class="btn-icon" title="Pause">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="btn-icon" title="Monitor">
                                <i class="fas fa-chart-line"></i>
                            </button>
                            <button class="btn-icon" title="Configure">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div class="workflow-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                        </div>
                        <span class="progress-text">Step 2 of 5: Data Processing</span>
                    </div>
                    <div class="workflow-steps">
                        <div class="step completed">
                            <i class="fas fa-check"></i>
                            <span>Energy Monitoring</span>
                        </div>
                        <div class="step active">
                            <i class="fas fa-cog fa-spin"></i>
                            <span>Data Processing</span>
                        </div>
                        <div class="step pending">
                            <i class="fas fa-clock"></i>
                            <span>ML Optimization</span>
                        </div>
                        <div class="step pending">
                            <i class="fas fa-clock"></i>
                            <span>Implementation</span>
                        </div>
                        <div class="step pending">
                            <i class="fas fa-clock"></i>
                            <span>Validation</span>
                        </div>
                    </div>
                    <div class="workflow-metrics">
                        <div class="metric">
                            <span class="metric-label">Duration</span>
                            <span class="metric-value">8m 17s</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Success Rate</span>
                            <span class="metric-value">94.2%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Last Run</span>
                            <span class="metric-value">2 min ago</span>
                        </div>
                    </div>
                </div>

                <div class="workflow-item paused">
                    <div class="workflow-header">
                        <div class="workflow-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="workflow-info">
                            <h4>Predictive Maintenance</h4>
                            <div class="workflow-description">Equipment health monitoring and maintenance scheduling</div>
                            <div class="workflow-status paused">Paused</div>
                        </div>
                        <div class="workflow-actions">
                            <button class="btn-icon" title="Resume">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn-icon" title="Monitor">
                                <i class="fas fa-chart-line"></i>
                            </button>
                            <button class="btn-icon" title="Configure">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div class="workflow-progress">
                        <div class="progress-bar">
                            <div class="progress-fill paused" style="width: 30%"></div>
                        </div>
                        <span class="progress-text">Paused at Step 2 of 7</span>
                    </div>
                    <div class="workflow-metrics">
                        <div class="metric">
                            <span class="metric-label">Duration</span>
                            <span class="metric-value">--</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Success Rate</span>
                            <span class="metric-value">96.8%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Last Run</span>
                            <span class="metric-value">1 hour ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Designer -->
        <div class="panel workflow-designer-panel">
            <div class="panel-header">
                <h3>Workflow Designer</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="new">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon" data-action="save">
                        <i class="fas fa-save"></i>
                    </button>
                    <button class="btn-icon" data-action="validate">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </div>
            <div class="workflow-designer-content">
                <div class="designer-toolbar">
                    <div class="tool-group">
                        <h5>Data Sources</h5>
                        <div class="tool-item" draggable="true" data-type="sensor-data">
                            <i class="fas fa-satellite-dish"></i>
                            <span>Sensor Data</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="api-data">
                            <i class="fas fa-cloud"></i>
                            <span>API Data</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="database">
                            <i class="fas fa-database"></i>
                            <span>Database</span>
                        </div>
                    </div>
                    <div class="tool-group">
                        <h5>Processing</h5>
                        <div class="tool-item" draggable="true" data-type="filter">
                            <i class="fas fa-filter"></i>
                            <span>Filter</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="transform">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Transform</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="aggregate">
                            <i class="fas fa-calculator"></i>
                            <span>Aggregate</span>
                        </div>
                    </div>
                    <div class="tool-group">
                        <h5>AI/ML</h5>
                        <div class="tool-item" draggable="true" data-type="ml-model">
                            <i class="fas fa-brain"></i>
                            <span>ML Model</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="ai-agent">
                            <i class="fas fa-robot"></i>
                            <span>AI Agent</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="optimization">
                            <i class="fas fa-magic"></i>
                            <span>Optimization</span>
                        </div>
                    </div>
                    <div class="tool-group">
                        <h5>Output</h5>
                        <div class="tool-item" draggable="true" data-type="notification">
                            <i class="fas fa-bell"></i>
                            <span>Notification</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="report">
                            <i class="fas fa-file-alt"></i>
                            <span>Report</span>
                        </div>
                        <div class="tool-item" draggable="true" data-type="action">
                            <i class="fas fa-play"></i>
                            <span>Action</span>
                        </div>
                    </div>
                </div>
                <div class="designer-canvas">
                    <div class="canvas-grid">
                        <!-- Workflow nodes will be placed here -->
                        <div class="workflow-node" style="left: 100px; top: 100px;" data-type="sensor-data">
                            <div class="node-header">
                                <i class="fas fa-satellite-dish"></i>
                                <span>Water Quality Sensors</span>
                            </div>
                            <div class="node-ports">
                                <div class="port output" data-port="data"></div>
                            </div>
                        </div>
                        <div class="workflow-node" style="left: 300px; top: 100px;" data-type="filter">
                            <div class="node-header">
                                <i class="fas fa-filter"></i>
                                <span>Data Filter</span>
                            </div>
                            <div class="node-ports">
                                <div class="port input" data-port="input"></div>
                                <div class="port output" data-port="filtered"></div>
                            </div>
                        </div>
                        <div class="workflow-node" style="left: 500px; top: 100px;" data-type="ml-model">
                            <div class="node-header">
                                <i class="fas fa-brain"></i>
                                <span>Quality Prediction</span>
                            </div>
                            <div class="node-ports">
                                <div class="port input" data-port="data"></div>
                                <div class="port output" data-port="prediction"></div>
                            </div>
                        </div>
                        <div class="workflow-node" style="left: 700px; top: 100px;" data-type="notification">
                            <div class="node-header">
                                <i class="fas fa-bell"></i>
                                <span>Alert System</span>
                            </div>
                            <div class="node-ports">
                                <div class="port input" data-port="alerts"></div>
                            </div>
                        </div>
                        <!-- Connection lines -->
                        <svg class="connection-layer">
                            <line x1="150" y1="115" x2="300" y2="115" stroke="#4ade80" stroke-width="2"/>
                            <line x1="350" y1="115" x2="500" y2="115" stroke="#4ade80" stroke-width="2"/>
                            <line x1="550" y1="115" x2="700" y2="115" stroke="#4ade80" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Templates -->
        <div class="panel workflow-templates-panel">
            <div class="panel-header">
                <h3>Workflow Templates</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="import">
                        <i class="fas fa-upload"></i>
                    </button>
                    <button class="btn-icon" data-action="export">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="workflow-templates-content">
                <div class="template-grid">
                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="template-info">
                                <h4>Water Quality Monitoring</h4>
                                <div class="template-description">Automated water quality analysis and alerting</div>
                            </div>
                        </div>
                        <div class="template-steps">
                            <span class="step-count">6 Steps</span>
                            <span class="step-duration">~15 minutes</span>
                        </div>
                        <div class="template-actions">
                            <button class="btn-template">Use Template</button>
                            <button class="btn-template-secondary">Preview</button>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="template-info">
                                <h4>Energy Optimization</h4>
                                <div class="template-description">AI-powered energy consumption optimization</div>
                            </div>
                        </div>
                        <div class="template-steps">
                            <span class="step-count">8 Steps</span>
                            <span class="step-duration">~25 minutes</span>
                        </div>
                        <div class="template-actions">
                            <button class="btn-template">Use Template</button>
                            <button class="btn-template-secondary">Preview</button>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="template-info">
                                <h4>Predictive Maintenance</h4>
                                <div class="template-description">Equipment health monitoring and maintenance scheduling</div>
                            </div>
                        </div>
                        <div class="template-steps">
                            <span class="step-count">7 Steps</span>
                            <span class="step-duration">~20 minutes</span>
                        </div>
                        <div class="template-actions">
                            <button class="btn-template">Use Template</button>
                            <button class="btn-template-secondary">Preview</button>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <div class="template-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="template-info">
                                <h4>Sustainability Assessment</h4>
                                <div class="template-description">Comprehensive environmental impact analysis</div>
                            </div>
                        </div>
                        <div class="template-steps">
                            <span class="step-count">10 Steps</span>
                            <span class="step-duration">~35 minutes</span>
                        </div>
                        <div class="template-actions">
                            <button class="btn-template">Use Template</button>
                            <button class="btn-template-secondary">Preview</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Execution History -->
        <div class="panel execution-history-panel">
            <div class="panel-header">
                <h3>Execution History</h3>
                <div class="panel-controls">
                    <select class="filter-select">
                        <option value="all">All Workflows</option>
                        <option value="successful">Successful</option>
                        <option value="failed">Failed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
            </div>
            <div class="execution-history-content">
                <div class="history-timeline">
                    <div class="history-item successful">
                        <div class="history-time">5 min ago</div>
                        <div class="history-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="history-content">
                            <div class="history-title">Water Quality Analysis Pipeline</div>
                            <div class="history-details">Completed successfully in 12m 34s</div>
                        </div>
                        <div class="history-status success">Success</div>
                    </div>

                    <div class="history-item successful">
                        <div class="history-time">15 min ago</div>
                        <div class="history-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="history-content">
                            <div class="history-title">Energy Optimization Workflow</div>
                            <div class="history-details">Completed successfully in 18m 47s</div>
                        </div>
                        <div class="history-status success">Success</div>
                    </div>

                    <div class="history-item failed">
                        <div class="history-time">1 hour ago</div>
                        <div class="history-icon">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="history-content">
                            <div class="history-title">Predictive Maintenance</div>
                            <div class="history-details">Failed at step 3: Data validation error</div>
                        </div>
                        <div class="history-status failed">Failed</div>
                    </div>

                    <div class="history-item successful">
                        <div class="history-time">2 hours ago</div>
                        <div class="history-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="history-content">
                            <div class="history-title">Sustainability Assessment</div>
                            <div class="history-details">Completed successfully in 32m 15s</div>
                        </div>
                        <div class="history-status success">Success</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
