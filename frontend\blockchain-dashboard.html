<!-- Blockchain Dashboard Page -->
<div class="page" id="blockchain-dashboard-page">
    <div class="page-header">
        <h1><i class="fas fa-link"></i> Blockchain Dashboard</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> New Transaction
            </button>
            <button class="btn-secondary">
                <i class="fas fa-shield-alt"></i> Verify Chain
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Ledger
            </button>
        </div>
    </div>

    <!-- Blockchain Dashboard -->
    <div class="blockchain-dashboard">
        <!-- Blockchain Overview -->
        <div class="blockchain-overview">
            <div class="blockchain-metric">
                <div class="metric-icon">
                    <i class="fas fa-cubes"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">12,847</div>
                    <div class="metric-label">Total Blocks</div>
                    <div class="metric-change positive">+23 today</div>
                </div>
            </div>

            <div class="blockchain-metric">
                <div class="metric-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">847,392</div>
                    <div class="metric-label">Total Transactions</div>
                    <div class="metric-change positive">+1,247 today</div>
                </div>
            </div>

            <div class="blockchain-metric">
                <div class="metric-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">47</div>
                    <div class="metric-label">Active Nodes</div>
                    <div class="metric-change neutral">No change</div>
                </div>
            </div>

            <div class="blockchain-metric">
                <div class="metric-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="metric-content">
                    <div class="metric-value">100%</div>
                    <div class="metric-label">Chain Integrity</div>
                    <div class="metric-change positive">Verified</div>
                </div>
            </div>
        </div>

        <!-- Recent Blocks -->
        <div class="panel recent-blocks-panel">
            <div class="panel-header">
                <h3>Recent Blocks</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="refresh">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn-icon" data-action="mine-block">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="recent-blocks-content">
                <div class="block-list">
                    <div class="block-item">
                        <div class="block-header">
                            <div class="block-number">#12847</div>
                            <div class="block-hash">0x7a8f9b2c...d4e5f6a1</div>
                            <div class="block-timestamp">2 minutes ago</div>
                        </div>
                        <div class="block-details">
                            <div class="block-detail">
                                <span class="detail-label">Transactions:</span>
                                <span class="detail-value">23</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Size:</span>
                                <span class="detail-value">2.4 KB</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Miner:</span>
                                <span class="detail-value">Node-07</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Difficulty:</span>
                                <span class="detail-value">1,247,893</span>
                            </div>
                        </div>
                        <div class="block-actions">
                            <button class="btn-block" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-block" title="Verify">
                                <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>

                    <div class="block-item">
                        <div class="block-header">
                            <div class="block-number">#12846</div>
                            <div class="block-hash">0x3c4d5e6f...a1b2c3d4</div>
                            <div class="block-timestamp">5 minutes ago</div>
                        </div>
                        <div class="block-details">
                            <div class="block-detail">
                                <span class="detail-label">Transactions:</span>
                                <span class="detail-value">18</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Size:</span>
                                <span class="detail-value">1.9 KB</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Miner:</span>
                                <span class="detail-value">Node-03</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Difficulty:</span>
                                <span class="detail-value">1,247,891</span>
                            </div>
                        </div>
                        <div class="block-actions">
                            <button class="btn-block" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-block" title="Verify">
                                <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>

                    <div class="block-item">
                        <div class="block-header">
                            <div class="block-number">#12845</div>
                            <div class="block-hash">0x9e8f7d6c...5b4a3928</div>
                            <div class="block-timestamp">8 minutes ago</div>
                        </div>
                        <div class="block-details">
                            <div class="block-detail">
                                <span class="detail-label">Transactions:</span>
                                <span class="detail-value">31</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Size:</span>
                                <span class="detail-value">3.1 KB</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Miner:</span>
                                <span class="detail-value">Node-12</span>
                            </div>
                            <div class="block-detail">
                                <span class="detail-label">Difficulty:</span>
                                <span class="detail-value">1,247,889</span>
                            </div>
                        </div>
                        <div class="block-actions">
                            <button class="btn-block" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-block" title="Verify">
                                <i class="fas fa-check"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Pool -->
        <div class="panel transaction-pool-panel">
            <div class="panel-header">
                <h3>Transaction Pool</h3>
                <div class="pool-stats">
                    <span class="pool-stat">
                        <i class="fas fa-clock"></i>
                        <span>47 Pending</span>
                    </span>
                    <span class="pool-stat">
                        <i class="fas fa-check"></i>
                        <span>1,247 Confirmed</span>
                    </span>
                </div>
            </div>
            <div class="transaction-pool-content">
                <div class="transaction-list">
                    <div class="transaction-item pending">
                        <div class="transaction-header">
                            <div class="transaction-hash">0xa1b2c3d4...e5f6a7b8</div>
                            <div class="transaction-type">Water Quality Data</div>
                            <div class="transaction-status pending">Pending</div>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-detail">
                                <span class="detail-label">From:</span>
                                <span class="detail-value">Sensor-WQ-001</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Data:</span>
                                <span class="detail-value">pH: 7.2, Turbidity: 1.5</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Timestamp:</span>
                                <span class="detail-value">2024-01-15 14:23:45</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Gas Fee:</span>
                                <span class="detail-value">0.001 ETH</span>
                            </div>
                        </div>
                    </div>

                    <div class="transaction-item confirmed">
                        <div class="transaction-header">
                            <div class="transaction-hash">0x9c8d7e6f...a5b4c3d2</div>
                            <div class="transaction-type">Energy Consumption</div>
                            <div class="transaction-status confirmed">Confirmed</div>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-detail">
                                <span class="detail-label">From:</span>
                                <span class="detail-value">Energy-Meter-045</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Data:</span>
                                <span class="detail-value">Consumption: 847 kWh</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Block:</span>
                                <span class="detail-value">#12847</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Confirmations:</span>
                                <span class="detail-value">3</span>
                            </div>
                        </div>
                    </div>

                    <div class="transaction-item confirmed">
                        <div class="transaction-header">
                            <div class="transaction-hash">0x5f4e3d2c...1b0a9876</div>
                            <div class="transaction-type">Treatment Process</div>
                            <div class="transaction-status confirmed">Confirmed</div>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-detail">
                                <span class="detail-label">From:</span>
                                <span class="detail-value">Treatment-Plant-A</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Data:</span>
                                <span class="detail-value">Efficiency: 94.7%</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Block:</span>
                                <span class="detail-value">#12846</span>
                            </div>
                            <div class="transaction-detail">
                                <span class="detail-label">Confirmations:</span>
                                <span class="detail-value">5</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Network Nodes -->
        <div class="panel network-nodes-panel">
            <div class="panel-header">
                <h3>Network Nodes</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="add-node">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon" data-action="sync-nodes">
                        <i class="fas fa-sync"></i>
                    </button>
                </div>
            </div>
            <div class="network-nodes-content">
                <div class="nodes-map">
                    <svg id="nodesNetworkMap" width="100%" height="300">
                        <!-- Central node -->
                        <circle cx="400" cy="150" r="25" fill="#4ade80" stroke="#22c55e" stroke-width="3"/>
                        <text x="400" y="155" text-anchor="middle" fill="white" font-weight="bold">Main</text>
                        
                        <!-- Connected nodes -->
                        <circle cx="200" cy="100" r="20" fill="#3b82f6" stroke="#2563eb" stroke-width="2"/>
                        <text x="200" y="105" text-anchor="middle" fill="white" font-size="12">Node-1</text>
                        
                        <circle cx="600" cy="100" r="20" fill="#3b82f6" stroke="#2563eb" stroke-width="2"/>
                        <text x="600" y="105" text-anchor="middle" fill="white" font-size="12">Node-2</text>
                        
                        <circle cx="200" cy="200" r="20" fill="#3b82f6" stroke="#2563eb" stroke-width="2"/>
                        <text x="200" y="205" text-anchor="middle" fill="white" font-size="12">Node-3</text>
                        
                        <circle cx="600" cy="200" r="20" fill="#3b82f6" stroke="#2563eb" stroke-width="2"/>
                        <text x="600" y="205" text-anchor="middle" fill="white" font-size="12">Node-4</text>
                        
                        <circle cx="300" cy="50" r="15" fill="#f59e0b" stroke="#d97706" stroke-width="2"/>
                        <text x="300" y="55" text-anchor="middle" fill="white" font-size="10">N-5</text>
                        
                        <circle cx="500" cy="50" r="15" fill="#f59e0b" stroke="#d97706" stroke-width="2"/>
                        <text x="500" y="55" text-anchor="middle" fill="white" font-size="10">N-6</text>
                        
                        <!-- Connections -->
                        <line x1="375" y1="130" x2="220" y2="120" stroke="#4ade80" stroke-width="2"/>
                        <line x1="425" y1="130" x2="580" y2="120" stroke="#4ade80" stroke-width="2"/>
                        <line x1="375" y1="170" x2="220" y2="180" stroke="#4ade80" stroke-width="2"/>
                        <line x1="425" y1="170" x2="580" y2="180" stroke="#4ade80" stroke-width="2"/>
                        <line x1="220" y1="90" x2="310" y2="60" stroke="#3b82f6" stroke-width="1"/>
                        <line x1="580" y1="90" x2="490" y2="60" stroke="#3b82f6" stroke-width="1"/>
                    </svg>
                </div>

                <div class="nodes-list">
                    <div class="node-item">
                        <div class="node-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="node-info">
                            <div class="node-name">Main Node</div>
                            <div class="node-address">*************</div>
                            <div class="node-role">Validator</div>
                        </div>
                        <div class="node-metrics">
                            <div class="node-metric">
                                <span class="metric-label">Blocks Mined:</span>
                                <span class="metric-value">2,847</span>
                            </div>
                            <div class="node-metric">
                                <span class="metric-label">Uptime:</span>
                                <span class="metric-value">99.9%</span>
                            </div>
                        </div>
                        <div class="node-status online">Online</div>
                    </div>

                    <div class="node-item">
                        <div class="node-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="node-info">
                            <div class="node-name">Node-07</div>
                            <div class="node-address">*************</div>
                            <div class="node-role">Miner</div>
                        </div>
                        <div class="node-metrics">
                            <div class="node-metric">
                                <span class="metric-label">Blocks Mined:</span>
                                <span class="metric-value">1,923</span>
                            </div>
                            <div class="node-metric">
                                <span class="metric-label">Hash Rate:</span>
                                <span class="metric-value">847 MH/s</span>
                            </div>
                        </div>
                        <div class="node-status online">Online</div>
                    </div>

                    <div class="node-item">
                        <div class="node-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="node-info">
                            <div class="node-name">Node-12</div>
                            <div class="node-address">*************</div>
                            <div class="node-role">Full Node</div>
                        </div>
                        <div class="node-metrics">
                            <div class="node-metric">
                                <span class="metric-label">Sync Status:</span>
                                <span class="metric-value">100%</span>
                            </div>
                            <div class="node-metric">
                                <span class="metric-label">Peers:</span>
                                <span class="metric-value">23</span>
                            </div>
                        </div>
                        <div class="node-status online">Online</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Smart Contracts -->
        <div class="panel smart-contracts-panel">
            <div class="panel-header">
                <h3>Smart Contracts</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="deploy-contract">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn-icon" data-action="audit-contracts">
                        <i class="fas fa-shield-alt"></i>
                    </button>
                </div>
            </div>
            <div class="smart-contracts-content">
                <div class="contract-list">
                    <div class="contract-item">
                        <div class="contract-header">
                            <div class="contract-icon">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <div class="contract-info">
                                <h4>Water Quality Verification</h4>
                                <div class="contract-address">0x7a8f9b2c...d4e5f6a1</div>
                                <div class="contract-status active">Active</div>
                            </div>
                        </div>
                        <div class="contract-metrics">
                            <div class="contract-metric">
                                <span class="metric-label">Executions:</span>
                                <span class="metric-value">12,847</span>
                            </div>
                            <div class="contract-metric">
                                <span class="metric-label">Gas Used:</span>
                                <span class="metric-value">2.4M</span>
                            </div>
                            <div class="contract-metric">
                                <span class="metric-label">Success Rate:</span>
                                <span class="metric-value">99.7%</span>
                            </div>
                        </div>
                        <div class="contract-actions">
                            <button class="btn-contract">Execute</button>
                            <button class="btn-contract">View Code</button>
                            <button class="btn-contract">Audit</button>
                        </div>
                    </div>

                    <div class="contract-item">
                        <div class="contract-header">
                            <div class="contract-icon">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <div class="contract-info">
                                <h4>Energy Trading</h4>
                                <div class="contract-address">0x3c4d5e6f...a1b2c3d4</div>
                                <div class="contract-status active">Active</div>
                            </div>
                        </div>
                        <div class="contract-metrics">
                            <div class="contract-metric">
                                <span class="metric-label">Trades:</span>
                                <span class="metric-value">8,923</span>
                            </div>
                            <div class="contract-metric">
                                <span class="metric-label">Volume:</span>
                                <span class="metric-value">847 MWh</span>
                            </div>
                            <div class="contract-metric">
                                <span class="metric-label">Revenue:</span>
                                <span class="metric-value">$234,567</span>
                            </div>
                        </div>
                        <div class="contract-actions">
                            <button class="btn-contract">Execute</button>
                            <button class="btn-contract">View Code</button>
                            <button class="btn-contract">Audit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blockchain Analytics -->
        <div class="panel blockchain-analytics-panel">
            <div class="panel-header">
                <h3>Blockchain Analytics</h3>
                <div class="panel-controls">
                    <select class="analytics-timeframe">
                        <option value="24h">Last 24 Hours</option>
                        <option value="7d">Last 7 Days</option>
                        <option value="30d">Last 30 Days</option>
                    </select>
                </div>
            </div>
            <div class="blockchain-analytics-content">
                <div class="analytics-charts">
                    <div class="chart-container">
                        <h4>Transaction Volume</h4>
                        <canvas id="transactionVolumeChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h4>Block Mining Rate</h4>
                        <canvas id="blockMiningChart"></canvas>
                    </div>
                </div>

                <div class="analytics-insights">
                    <h4>Key Insights</h4>
                    <div class="insight-list">
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Transaction Growth</div>
                                <div class="insight-description">Daily transactions increased by 23% this week</div>
                            </div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Security Status</div>
                                <div class="insight-description">No security incidents detected in the last 30 days</div>
                            </div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Network Health</div>
                                <div class="insight-description">All 47 nodes are synchronized and operational</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
