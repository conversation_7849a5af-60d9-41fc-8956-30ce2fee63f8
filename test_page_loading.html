<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Water Management System - Page Loading Test</h1>
        <p>This test verifies that the page content methods are working correctly.</p>
        
        <div class="test-buttons">
            <button class="test-button" onclick="testWaterQuality()">Test Water Quality Page</button>
            <button class="test-button" onclick="testTreatmentSystems()">Test Treatment Systems Page</button>
            <button class="test-button" onclick="testEnergyGrid()">Test Energy Grid Page</button>
            <button class="test-button" onclick="testAIAgents()">Test AI Agents Page</button>
            <button class="test-button" onclick="testAllPages()">Test All Pages</button>
        </div>
        
        <div id="testResults" class="test-result" style="display: none;">
            <h3>Test Results:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        // Mock PageManager class for testing
        class TestPageManager {
            async getWaterQualityPageContent() {
                return `<div class="page-header"><h1>Water Quality Management</h1></div>`;
            }
            
            async getTreatmentSystemsPageContent() {
                return `<div class="page-header"><h1>Treatment Systems</h1></div>`;
            }
            
            async getEnergyGridPageContent() {
                return `<div class="page-header"><h1>Energy Grid Management</h1></div>`;
            }
            
            async getAIAgentsPageContent() {
                return `<div class="page-header"><h1>AI Agent Management</h1></div>`;
            }
        }

        const testManager = new TestPageManager();

        function showResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultContent');
            
            resultsDiv.style.display = 'block';
            resultsDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            contentDiv.innerHTML = message;
        }

        async function testWaterQuality() {
            try {
                const content = await testManager.getWaterQualityPageContent();
                showResult(`✅ Water Quality page content loaded successfully! (${content.length} characters)`, true);
            } catch (error) {
                showResult(`❌ Water Quality page failed: ${error.message}`, false);
            }
        }

        async function testTreatmentSystems() {
            try {
                const content = await testManager.getTreatmentSystemsPageContent();
                showResult(`✅ Treatment Systems page content loaded successfully! (${content.length} characters)`, true);
            } catch (error) {
                showResult(`❌ Treatment Systems page failed: ${error.message}`, false);
            }
        }

        async function testEnergyGrid() {
            try {
                const content = await testManager.getEnergyGridPageContent();
                showResult(`✅ Energy Grid page content loaded successfully! (${content.length} characters)`, true);
            } catch (error) {
                showResult(`❌ Energy Grid page failed: ${error.message}`, false);
            }
        }

        async function testAIAgents() {
            try {
                const content = await testManager.getAIAgentsPageContent();
                showResult(`✅ AI Agents page content loaded successfully! (${content.length} characters)`, true);
            } catch (error) {
                showResult(`❌ AI Agents page failed: ${error.message}`, false);
            }
        }

        async function testAllPages() {
            const pages = ['Water Quality', 'Treatment Systems', 'Energy Grid', 'AI Agents'];
            const methods = [
                testManager.getWaterQualityPageContent,
                testManager.getTreatmentSystemsPageContent,
                testManager.getEnergyGridPageContent,
                testManager.getAIAgentsPageContent
            ];
            
            let results = [];
            
            for (let i = 0; i < pages.length; i++) {
                try {
                    const content = await methods[i].call(testManager);
                    results.push(`✅ ${pages[i]}: OK (${content.length} chars)`);
                } catch (error) {
                    results.push(`❌ ${pages[i]}: Failed - ${error.message}`);
                }
            }
            
            showResult(`<strong>All Pages Test Results:</strong><br>${results.join('<br>')}`, true);
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('Page loading test ready');
            showResult('Page loading test is ready. Click a button to test page content methods.', true);
        });
    </script>
</body>
</html>
