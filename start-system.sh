#!/bin/bash
# Water Management System Startup Script for Linux/Mac
# This script starts the integrated water management system

echo ""
echo "========================================================"
echo "🌊 Water Management System - Unix Startup"
echo "========================================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    echo ""
    exit 1
fi

print_status "Node.js found: $(node --version)"
echo ""

# Check if the startup script exists
if [ ! -f "start-system.js" ]; then
    print_error "start-system.js not found"
    echo "Please ensure you're running this from the project root directory"
    echo ""
    exit 1
fi

print_status "Startup script found"
echo ""

# Make the script executable if it isn't already
chmod +x start-system.js

# Start the system
echo "🚀 Starting Water Management System..."
echo ""

# Handle Ctrl+C gracefully
trap 'echo -e "\n🛑 Shutting down..."; exit 0' INT

node start-system.js

# If we get here, the system has stopped
echo ""
echo "🛑 Water Management System has stopped"
echo ""
