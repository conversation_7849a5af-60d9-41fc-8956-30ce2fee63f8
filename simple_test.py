"""
Simple test script to verify Gemini API integration.
"""

import os
import asyncio
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables
load_dotenv()

async def test_gemini_basic():
    """Test basic Gemini API functionality."""
    print("🧪 Testing Gemini API Integration...")
    
    try:
        # Get API key from environment
        api_key = os.getenv('GOOGLE_API_KEY')
        
        if not api_key:
            print("❌ Google API key not found in .env file")
            print("Please check your .env file contains: GOOGLE_API_KEY=your_key_here")
            return False
        
        print(f"✅ Google API key found: {api_key[:10]}...")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Test basic generation
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        prompt = """
        You are an AI assistant for a water management decarbonisation system.
        
        Analyze this sample climate data and provide 3 key insights for water treatment optimization:
        
        Climate Data:
        - Temperature: 25°C average (range: 18°C to 32°C)
        - Precipitation: 45mm total over 7 days
        - Humidity: 65% average
        - Location: Urban water treatment facility
        
        Please provide specific, actionable insights.
        """
        
        print("🔄 Generating response with Gemini...")
        response = model.generate_content(prompt)
        
        print("✅ Gemini API test successful!")
        print("\n📊 Sample Climate Analysis Response:")
        print("=" * 60)
        print(response.text)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini API test failed: {str(e)}")
        return False


async def test_water_treatment_optimization():
    """Test water treatment optimization scenario."""
    print("\n🏭 Testing Water Treatment Optimization Scenario...")
    
    try:
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        prompt = """
        You are an expert in water treatment optimization and decarbonisation.
        
        Current System Status:
        - Primary filtration: 92% efficiency, 15.2 kW power consumption
        - Secondary filtration: 88% efficiency, 12.8 kW power consumption
        - UV treatment: Currently standby, 8.5 kW when active
        - Chemical dosing: 87% efficiency, 6.3 kW power consumption
        - Total daily water processed: 50,000 liters
        - Current carbon footprint: 1.2 tons CO₂/month
        
        Climate Conditions:
        - Temperature rising trend (+2°C over baseline)
        - Increased precipitation variability
        - More frequent extreme weather events
        
        Provide:
        1. Three specific optimization recommendations
        2. Expected carbon footprint reduction
        3. Energy efficiency improvements
        4. Climate adaptation strategies
        
        Be specific and quantitative where possible.
        """
        
        print("🔄 Analyzing water treatment optimization...")
        response = model.generate_content(prompt)
        
        print("✅ Water treatment optimization analysis complete!")
        print("\n🎯 Optimization Recommendations:")
        print("=" * 60)
        print(response.text)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Water treatment optimization test failed: {str(e)}")
        return False


async def test_climate_impact_assessment():
    """Test climate impact assessment."""
    print("\n🌡️ Testing Climate Impact Assessment...")
    
    try:
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        prompt = """
        As a climate scientist specializing in water systems, assess the following scenario:
        
        Climate Data Trends (Last 30 days):
        - Average temperature: 24.5°C (2°C above historical average)
        - Temperature range: 18°C to 38°C (extreme heat event detected)
        - Total precipitation: 125mm (30% below average)
        - Heavy precipitation events: 3 (>20mm in 24h)
        - Humidity: 45-85% (high variability)
        - Wind speed: Average 12 km/h, max 65 km/h (storm event)
        
        Water Treatment Facility Context:
        - Capacity: 100,000 L/day
        - Serves urban population of 25,000
        - Current energy mix: 60% grid, 40% solar
        - Located in temperate climate zone
        
        Provide:
        1. Risk assessment (high/medium/low) for each climate factor
        2. Immediate operational adjustments needed
        3. Long-term adaptation strategies
        4. Potential system vulnerabilities
        5. Recommended monitoring parameters
        
        Focus on actionable insights for system operators.
        """
        
        print("🔄 Conducting climate impact assessment...")
        response = model.generate_content(prompt)
        
        print("✅ Climate impact assessment complete!")
        print("\n🌍 Climate Impact Analysis:")
        print("=" * 60)
        print(response.text)
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Climate impact assessment test failed: {str(e)}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Water Management Decarbonisation System - Gemini Integration Test")
    print("=" * 70)
    
    test_results = []
    
    # Test 1: Basic Gemini API
    basic_result = await test_gemini_basic()
    test_results.append(("Basic Gemini API", basic_result))
    
    # Test 2: Water Treatment Optimization (only if basic test passes)
    if basic_result:
        optimization_result = await test_water_treatment_optimization()
        test_results.append(("Water Treatment Optimization", optimization_result))
        
        # Test 3: Climate Impact Assessment
        climate_result = await test_climate_impact_assessment()
        test_results.append(("Climate Impact Assessment", climate_result))
    else:
        test_results.append(("Water Treatment Optimization", False))
        test_results.append(("Climate Impact Assessment", False))
        print("⏭️ Skipping advanced tests due to basic API failure")
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("-" * 70)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! Gemini integration is working perfectly!")
        print("\n🚀 Your system is ready for:")
        print("  ✓ Climate data analysis and insights")
        print("  ✓ Water treatment optimization recommendations")
        print("  ✓ Real-time decision support")
        print("  ✓ Carbon footprint reduction strategies")
        print("  ✓ Climate adaptation planning")
        
        print("\n📋 Next Steps:")
        print("  1. Install additional dependencies: pip install -r requirements.txt")
        print("  2. Set up database: make setup-db")
        print("  3. Start development environment: make dev")
        print("  4. Access Streamlit dashboard: http://localhost:8501")
        
    elif passed > 0:
        print(f"\n⚠️ Partial success ({passed}/{total} tests passed)")
        print("Basic Gemini integration is working, but some advanced features may need attention.")
        
    else:
        print("\n❌ All tests failed. Please check:")
        print("  1. Your .env file contains the correct GOOGLE_API_KEY")
        print("  2. Your internet connection is working")
        print("  3. The API key has proper permissions")
        print("  4. You have sufficient API quota")
    
    print("\n" + "=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
