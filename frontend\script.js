// Chart.js Configuration and Data
const chartConfig = {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [
            {
                label: 'Water Usage (ML)',
                data: [120, 135, 145, 160, 175, 190, 185, 170, 155, 140, 125, 115],
                borderColor: '#4ade80',
                backgroundColor: 'rgba(74, 222, 128, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Energy Consumption (MWh)',
                data: [80, 85, 95, 105, 115, 125, 120, 110, 100, 90, 85, 75],
                borderColor: '#60a5fa',
                backgroundColor: 'rgba(96, 165, 250, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Carbon Emissions (tCO2)',
                data: [45, 48, 52, 58, 62, 68, 65, 60, 55, 50, 47, 42],
                borderColor: '#f87171',
                backgroundColor: 'rgba(248, 113, 113, 0.1)',
                fill: true,
                tension: 0.4
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    color: '#ffffff',
                    usePointStyle: true
                }
            }
        },
        scales: {
            x: {
                ticks: {
                    color: '#ffffff'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            y: {
                ticks: {
                    color: '#ffffff'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        }
    }
};

// Page Management System
class PageManager {
    constructor() {
        this.currentPage = 'overview';
        this.pages = {};
        this.charts = {};
        this.init();
    }

    init() {
        this.setupNavigation();
        this.loadPage('overview');
        this.initializeCharts();
        this.setupEventListeners();
    }

    setupNavigation() {
        // Navigation tabs
        const navTabs = document.querySelectorAll('.nav-tab');
        navTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const page = tab.dataset.page;
                if (page) {
                    this.loadPage(page);
                    this.setActiveNav(tab);
                }
            });
        });


    }

    setActiveNav(activeTab) {
        document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
        activeTab.classList.add('active');
    }



    showPage(pageName) {
        this.loadPage(pageName);
    }

    async loadPage(pageName) {
        console.log(`🔄 loadPage called with: ${pageName}`);

        // Hide all pages first
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
            page.style.display = 'none';
        });

        // Handle overview page (special case)
        if (pageName === 'overview') {
            const overviewPage = document.querySelector('.page:not([id])') || document.querySelector('.main-content');
            if (overviewPage) {
                overviewPage.style.display = 'block';
                this.currentPage = pageName;
                console.log(`✅ Overview page activated`);
                return;
            }
        }

        // Get or create page element
        let pageElement = document.getElementById(`${pageName}-page`);
        if (!pageElement) {
            console.error(`❌ Page element not found: ${pageName}-page`);
            return;
        }

        console.log(`📄 Found page element: ${pageElement.id}`);

        // Always load fresh content
        console.log(`🔄 Loading content for page: ${pageName}`);
        await this.loadPageContent(pageName);

        // Show the page
        pageElement.style.display = 'block';
        pageElement.classList.add('active');
        this.currentPage = pageName;
        console.log(`✅ Page ${pageName} is now active`);

        // Initialize page-specific functionality
        this.initializePage(pageName);
    }

    async loadPageContent(pageName) {
        const pageElement = document.getElementById(`${pageName}-page`);
        if (!pageElement) {
            console.error(`❌ Page element not found: ${pageName}-page`);
            return;
        }

        try {
            let content = '';
            console.log(`🔄 Generating content for page: ${pageName}`);

            // Get content based on page name
            switch(pageName) {
                case 'water-quality':
                    content = await this.getWaterQualityPageContent();
                    break;
                case 'treatment-systems':
                    content = await this.getTreatmentSystemsPageContent();
                    break;
                case 'energy-grid':
                    content = await this.getEnergyGridPageContent();
                    break;
                case 'ai-agents':
                    content = await this.getAIAgentsPageContent();
                    break;
                case 'ml-optimization':
                    content = await this.getMLOptimizationPageContent();
                    break;
                case 'workflow-orchestration':
                    content = await this.getWorkflowOrchestrationPageContent();
                    break;
                case 'knowledge-graphs':
                    content = await this.getKnowledgeGraphsPageContent();
                    break;
                case 'llm-integration':
                    content = await this.getLLMIntegrationPageContent();
                    break;
                case 'climate-impact':
                    content = await this.getClimateImpactPageContent();
                    break;
                case 'sensors':
                    content = await this.getSensorsPageContent();
                    break;
                case 'analytics':
                    content = await this.getAnalyticsPageContent();
                    break;
                case 'reports-dashboard':
                    content = await this.getReportsDashboardPageContent();
                    break;
                case 'system-management':
                    content = await this.getSystemManagementPageContent();
                    break;
                case 'advanced-ai-dashboard':
                    content = await this.getAdvancedAIDashboardPageContent();
                    break;
                case 'digital-twin-dashboard':
                    content = await this.getDigitalTwinDashboardPageContent();
                    break;
                case 'blockchain-dashboard':
                    content = await this.getBlockchainDashboardPageContent();
                    break;
                case 'predictive-maintenance-dashboard':
                    content = await this.getPredictiveMaintenanceDashboardPageContent();
                    break;
                default:
                    console.warn(`No content method found for page: ${pageName}`);
                    content = `<div class="placeholder-content">
                        <div class="placeholder-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3>${pageName.replace('-', ' ').toUpperCase()} Dashboard</h3>
                        <p>This page is under development. Content will be available soon.</p>
                        <div class="placeholder-actions">
                            <button class="btn-primary" onclick="app.loadPage('${pageName}')">
                                <i class="fas fa-refresh"></i> Retry
                            </button>
                            <button class="btn-secondary" onclick="app.loadPage('overview')">
                                <i class="fas fa-home"></i> Go to Overview
                            </button>
                        </div>
                    </div>`;
            }

            // Ensure we have content
            if (!content || content.trim() === '') {
                console.warn(`⚠️ No content generated for ${pageName}, using fallback`);
                content = `
                    <div class="page-header">
                        <h1><i class="fas fa-cog"></i> ${pageName.replace('-', ' ').toUpperCase()}</h1>
                    </div>
                    <div class="placeholder-content">
                        <div class="placeholder-icon">
                            <i class="fas fa-construction"></i>
                        </div>
                        <h3>Page Under Development</h3>
                        <p>This page is being built. Please check back soon!</p>
                    </div>
                `;
            }

            // Set the content
            pageElement.innerHTML = content;
            console.log(`✅ Content loaded for ${pageName}: ${content.length} characters`);

            // Verify content was set
            if (pageElement.innerHTML.trim() === '') {
                console.error(`❌ Content was not set properly for ${pageName}`);
            } else {
                console.log(`✅ Content verified for ${pageName}`);
            }
        } catch (error) {
            console.error(`Error loading page ${pageName}:`, error);
            pageElement.innerHTML = `
                <div class="error-message">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Error Loading Page</h3>
                    <p>Unable to load content for ${pageName}. Please try again.</p>
                    <button class="btn-primary" onclick="app.showPage('${pageName}')">
                        <i class="fas fa-refresh"></i> Retry
                    </button>
                </div>`;
        }
    }

    initializePage(pageName) {
        switch(pageName) {
            case 'overview':
                this.initializeOverviewPage();
                break;
            case 'water-quality':
                this.initializeWaterQualityPage();
                break;
            case 'treatment-systems':
                this.initializeTreatmentSystemsPage();
                break;
            case 'energy-grid':
                this.initializeEnergyGridPage();
                break;
            case 'ai-agents':
                this.initializeAIAgentsPage();
                break;
            case 'ml-optimization':
                this.initializeMLOptimizationPage();
                break;
            case 'workflow-orchestration':
                this.initializeWorkflowOrchestrationPage();
                break;
            case 'knowledge-graphs':
                this.initializeKnowledgeGraphsPage();
                break;
            case 'llm-integration':
                this.initializeLLMIntegrationPage();
                break;
            case 'climate-impact':
                this.initializeClimateImpactPage();
                break;
            case 'sensors':
                this.initializeSensorsPage();
                break;
            case 'analytics':
                this.initializeAnalyticsPage();
                break;
            case 'reports-dashboard':
                this.initializeReportsDashboardPage();
                break;
            case 'system-management':
                this.initializeSystemManagementPage();
                break;
            case 'advanced-ai-dashboard':
                this.initializeAdvancedAIDashboardPage();
                break;
            case 'digital-twin-dashboard':
                this.initializeDigitalTwinDashboardPage();
                break;
            case 'blockchain-dashboard':
                this.initializeBlockchainDashboardPage();
                break;
            case 'predictive-maintenance-dashboard':
                this.initializePredictiveMaintenanceDashboardPage();
                break;
        }
    }

    initializeCharts() {
        // Main overview chart
        const ctx = document.getElementById('mainChart');
        if (ctx) {
            this.charts.mainChart = new Chart(ctx.getContext('2d'), chartConfig);
        }
    }

    initializeOverviewPage() {
        // Chart control buttons
        const chartButtons = document.querySelectorAll('.chart-btn');
        chartButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                chartButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                const buttonText = btn.textContent;
                if (this.charts.mainChart) {
                    updateChartData(this.charts.mainChart, buttonText);
                }
            });
        });
    }

    setupEventListeners() {
        // Climate risk modal
        const climateModal = document.getElementById('climateModal');
        const modalClose = document.querySelector('.modal-close');

        document.addEventListener('click', (e) => {
            if (e.target.closest('.climate-risk-trigger')) {
                climateModal.style.display = 'block';
            }
        });

        if (modalClose) {
            modalClose.addEventListener('click', () => {
                climateModal.style.display = 'none';
            });
        }

        window.addEventListener('click', (e) => {
            if (e.target === climateModal) {
                climateModal.style.display = 'none';
            }
        });

        // Generic button handlers for all pages
        document.addEventListener('click', (e) => {
            // Handle all button clicks with data-action attributes
            if (e.target.matches('button[data-action]') || e.target.closest('button[data-action]')) {
                e.preventDefault();
                const button = e.target.matches('button[data-action]') ? e.target : e.target.closest('button[data-action]');
                const action = button.dataset.action;
                this.handleGenericAction(action, button);
            }
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            e.preventDefault();
            const form = e.target;
            if (form.id === 'chatForm') {
                this.handleChatSubmission(form);
            } else {
                this.handleFormSubmission(form);
            }
        });

        // Range input changes (for hyperparameter tuning, etc.)
        document.addEventListener('input', (e) => {
            if (e.target.type === 'range') {
                this.handleRangeInput(e.target);
            }
        });

        // Dropdown changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('select[data-action]')) {
                const action = e.target.dataset.action;
                this.handleDropdownChange(action, e.target.value, e.target);
            }
        });

        // Real-time data updates
        setInterval(() => this.updateRealTimeData(), 30000);

        // Initialize performance bars animation
        setTimeout(() => this.animatePerformanceBars(), 1000);
    }

    // Page Content Methods
    async getWaterQualityPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-tint"></i> Water Quality Management</h1>
                <div class="page-actions">
                    <button class="btn-primary">
                        <i class="fas fa-plus"></i> Add Sensor
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-cog"></i> Configure
                    </button>
                </div>
            </div>

            <div class="water-quality-dashboard">
                <div class="metrics-grid">
                    <div class="metric-card ph-card">
                        <div class="metric-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">7.2</div>
                            <div class="metric-label">pH Level</div>
                            <div class="metric-status normal">Normal</div>
                        </div>
                        <div class="metric-chart">
                            <canvas id="phChart"></canvas>
                        </div>
                    </div>

                    <div class="metric-card turbidity-card">
                        <div class="metric-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">1.5 NTU</div>
                            <div class="metric-label">Turbidity</div>
                            <div class="metric-status good">Good</div>
                        </div>
                        <div class="metric-chart">
                            <canvas id="turbidityChart"></canvas>
                        </div>
                    </div>

                    <div class="metric-card chlorine-card">
                        <div class="metric-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">1.0 mg/L</div>
                            <div class="metric-label">Chlorine Residual</div>
                            <div class="metric-status normal">Normal</div>
                        </div>
                        <div class="metric-chart">
                            <canvas id="chlorineChart"></canvas>
                        </div>
                    </div>

                    <div class="metric-card bacteria-card">
                        <div class="metric-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">0 CFU</div>
                            <div class="metric-label">E. Coli</div>
                            <div class="metric-status excellent">Excellent</div>
                        </div>
                        <div class="metric-chart">
                            <canvas id="bacteriaChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel trends-panel">
                    <div class="panel-header">
                        <h3>Water Quality Trends</h3>
                        <div class="panel-controls">
                            <select class="time-range-select">
                                <option value="24h">Last 24 Hours</option>
                                <option value="7d">Last 7 Days</option>
                                <option value="30d">Last 30 Days</option>
                                <option value="90d">Last 90 Days</option>
                            </select>
                        </div>
                    </div>
                    <div class="trends-chart">
                        <canvas id="waterQualityTrendsChart"></canvas>
                    </div>
                </div>

                <div class="panel map-panel">
                    <div class="panel-header">
                        <h3>Sensor Network</h3>
                        <div class="panel-controls">
                            <button class="btn-icon active" data-layer="sensors">
                                <i class="fas fa-satellite-dish"></i>
                            </button>
                            <button class="btn-icon" data-layer="quality">
                                <i class="fas fa-tint"></i>
                            </button>
                            <button class="btn-icon" data-layer="alerts">
                                <i class="fas fa-exclamation-triangle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="sensor-map" id="sensorMap"></div>
                </div>
            </div>
        `;
    }

    async getEnergyGridPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-bolt"></i> Energy Grid Management</h1>
                <div class="page-actions">
                    <button class="btn-primary">
                        <i class="fas fa-plus"></i> Add Grid Point
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-sync"></i> Optimize Grid
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-chart-line"></i> Energy Report
                    </button>
                </div>
            </div>

            <div class="energy-dashboard">
                <div class="energy-overview">
                    <div class="energy-card consumption">
                        <div class="energy-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div class="energy-content">
                            <div class="energy-value">2,847 kWh</div>
                            <div class="energy-label">Current Consumption</div>
                            <div class="energy-change positive">-12% from yesterday</div>
                        </div>
                        <div class="energy-gauge">
                            <canvas id="consumptionGauge"></canvas>
                        </div>
                    </div>

                    <div class="energy-card generation">
                        <div class="energy-icon">
                            <i class="fas fa-solar-panel"></i>
                        </div>
                        <div class="energy-content">
                            <div class="energy-value">1,923 kWh</div>
                            <div class="energy-label">Renewable Generation</div>
                            <div class="energy-change positive">+8% from yesterday</div>
                        </div>
                        <div class="energy-gauge">
                            <canvas id="generationGauge"></canvas>
                        </div>
                    </div>

                    <div class="energy-card efficiency">
                        <div class="energy-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="energy-content">
                            <div class="energy-value">87.3%</div>
                            <div class="energy-label">Grid Efficiency</div>
                            <div class="energy-change positive">+2.1% this week</div>
                        </div>
                        <div class="energy-gauge">
                            <canvas id="efficiencyGauge"></canvas>
                        </div>
                    </div>

                    <div class="energy-card cost">
                        <div class="energy-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="energy-content">
                            <div class="energy-value">$342.18</div>
                            <div class="energy-label">Daily Cost</div>
                            <div class="energy-change negative">+$23.45 from yesterday</div>
                        </div>
                        <div class="energy-gauge">
                            <canvas id="costGauge"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel grid-topology-panel">
                    <div class="panel-header">
                        <h3>Grid Topology</h3>
                        <div class="panel-controls">
                            <button class="btn-icon active" data-view="topology">
                                <i class="fas fa-project-diagram"></i>
                            </button>
                            <button class="btn-icon" data-view="load">
                                <i class="fas fa-weight-hanging"></i>
                            </button>
                            <button class="btn-icon" data-view="flow">
                                <i class="fas fa-stream"></i>
                            </button>
                        </div>
                    </div>
                    <div class="grid-visualization">
                        <svg id="gridTopology" width="100%" height="400"></svg>
                    </div>
                </div>

                <div class="panel energy-flow-panel">
                    <div class="panel-header">
                        <h3>Energy Flow Analysis</h3>
                        <div class="panel-controls">
                            <select class="time-range-select">
                                <option value="realtime">Real-time</option>
                                <option value="1h">Last Hour</option>
                                <option value="24h">Last 24 Hours</option>
                                <option value="7d">Last 7 Days</option>
                            </select>
                        </div>
                    </div>
                    <div class="energy-flow-chart">
                        <canvas id="energyFlowChart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    async getAIAgentsPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-robot"></i> AI Agent Management</h1>
                <div class="page-actions">
                    <button class="btn-primary">
                        <i class="fas fa-plus"></i> Deploy Agent
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-brain"></i> Train Model
                    </button>
                    <button class="btn-secondary">
                        <i class="fas fa-cogs"></i> Configure
                    </button>
                </div>
            </div>

            <div class="ai-agents-dashboard">
                <div class="agents-overview">
                    <div class="agent-card climate-agent">
                        <div class="agent-header">
                            <div class="agent-icon">
                                <i class="fas fa-cloud-sun"></i>
                            </div>
                            <div class="agent-info">
                                <h3>Climate Analysis Agent</h3>
                                <div class="agent-status active">Active</div>
                            </div>
                            <div class="agent-actions">
                                <button class="btn-icon" title="Configure">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <button class="btn-icon" title="View Logs">
                                    <i class="fas fa-file-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <span class="metric-label">Tasks Completed</span>
                                <span class="metric-value">1,247</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Accuracy</span>
                                <span class="metric-value">94.7%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Uptime</span>
                                <span class="metric-value">99.2%</span>
                            </div>
                        </div>
                        <div class="agent-activity">
                            <canvas id="climateAgentActivity"></canvas>
                        </div>
                    </div>

                    <div class="agent-card treatment-agent">
                        <div class="agent-header">
                            <div class="agent-icon">
                                <i class="fas fa-filter"></i>
                            </div>
                            <div class="agent-info">
                                <h3>Treatment Optimization Agent</h3>
                                <div class="agent-status active">Active</div>
                            </div>
                            <div class="agent-actions">
                                <button class="btn-icon" title="Configure">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <button class="btn-icon" title="View Logs">
                                    <i class="fas fa-file-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <span class="metric-label">Optimizations</span>
                                <span class="metric-value">892</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Efficiency Gain</span>
                                <span class="metric-value">+12.3%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Energy Saved</span>
                                <span class="metric-value">847 kWh</span>
                            </div>
                        </div>
                        <div class="agent-activity">
                            <canvas id="treatmentAgentActivity"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel communication-panel">
                    <div class="panel-header">
                        <h3>Agent Communication Network</h3>
                        <div class="panel-controls">
                            <button class="btn-icon active" data-view="network">
                                <i class="fas fa-network-wired"></i>
                            </button>
                            <button class="btn-icon" data-view="messages">
                                <i class="fas fa-comments"></i>
                            </button>
                            <button class="btn-icon" data-view="performance">
                                <i class="fas fa-tachometer-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="communication-network">
                        <svg id="agentNetwork" width="100%" height="400"></svg>
                    </div>
                </div>
            </div>
        `;
    }



    // New comprehensive page content methods
    async getTreatmentSystemsPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-filter"></i> Treatment Systems</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="add-system">
                        <i class="fas fa-plus"></i> Add System
                    </button>
                    <button class="btn-secondary" data-action="optimize">
                        <i class="fas fa-cogs"></i> Optimize
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="treatment-dashboard">
                <div class="systems-overview">
                    <div class="system-card primary-treatment">
                        <div class="system-header">
                            <div class="system-icon">
                                <i class="fas fa-filter"></i>
                            </div>
                            <div class="system-info">
                                <h3>Primary Treatment</h3>
                                <div class="system-status operational">Operational</div>
                            </div>
                            <div class="system-efficiency">92%</div>
                        </div>
                        <div class="system-metrics">
                            <div class="metric">
                                <span class="metric-label">Flow Rate</span>
                                <span class="metric-value">2,450 L/min</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Removal Rate</span>
                                <span class="metric-value">87.3%</span>
                            </div>
                        </div>
                        <div class="system-chart">
                            <canvas id="primaryTreatmentChart"></canvas>
                        </div>
                    </div>

                    <div class="system-card secondary-treatment">
                        <div class="system-header">
                            <div class="system-icon">
                                <i class="fas fa-recycle"></i>
                            </div>
                            <div class="system-info">
                                <h3>Secondary Treatment</h3>
                                <div class="system-status operational">Operational</div>
                            </div>
                            <div class="system-efficiency">89%</div>
                        </div>
                        <div class="system-metrics">
                            <div class="metric">
                                <span class="metric-label">BOD Removal</span>
                                <span class="metric-value">94.2%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">TSS Removal</span>
                                <span class="metric-value">91.8%</span>
                            </div>
                        </div>
                        <div class="system-chart">
                            <canvas id="secondaryTreatmentChart"></canvas>
                        </div>
                    </div>

                    <div class="system-card tertiary-treatment">
                        <div class="system-header">
                            <div class="system-icon">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="system-info">
                                <h3>Tertiary Treatment</h3>
                                <div class="system-status maintenance">Maintenance</div>
                            </div>
                            <div class="system-efficiency">76%</div>
                        </div>
                        <div class="system-metrics">
                            <div class="metric">
                                <span class="metric-label">Pathogen Removal</span>
                                <span class="metric-value">99.9%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Nutrient Removal</span>
                                <span class="metric-value">85.4%</span>
                            </div>
                        </div>
                        <div class="system-chart">
                            <canvas id="tertiaryTreatmentChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel process-flow-panel">
                    <div class="panel-header">
                        <h3>Treatment Process Flow</h3>
                        <div class="panel-controls">
                            <button class="btn-icon active" data-view="flow">
                                <i class="fas fa-stream"></i>
                            </button>
                            <button class="btn-icon" data-view="efficiency">
                                <i class="fas fa-chart-line"></i>
                            </button>
                        </div>
                    </div>
                    <div class="process-flow-diagram">
                        <svg id="processFlow" width="100%" height="300"></svg>
                    </div>
                </div>

                <div class="panel optimization-panel">
                    <div class="panel-header">
                        <h3>Process Optimization</h3>
                        <div class="panel-controls">
                            <select class="optimization-select" data-action="filter">
                                <option value="energy">Energy Efficiency</option>
                                <option value="cost">Cost Optimization</option>
                                <option value="quality">Quality Maximization</option>
                            </select>
                        </div>
                    </div>
                    <div class="optimization-results">
                        <div class="optimization-metric">
                            <div class="metric-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">-15.3%</div>
                                <div class="metric-label">Energy Reduction</div>
                            </div>
                        </div>
                        <div class="optimization-metric">
                            <div class="metric-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">$12,450</div>
                                <div class="metric-label">Monthly Savings</div>
                            </div>
                        </div>
                        <div class="optimization-metric">
                            <div class="metric-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">-8.7%</div>
                                <div class="metric-label">Carbon Footprint</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getMLOptimizationPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-brain"></i> ML & Optimization</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="train-model">
                        <i class="fas fa-play"></i> Train Model
                    </button>
                    <button class="btn-secondary" data-action="deploy">
                        <i class="fas fa-rocket"></i> Deploy
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="ml-dashboard">
                <div class="models-overview">
                    <div class="model-card neural-network">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                            <div class="model-info">
                                <h3>Neural Network</h3>
                                <div class="model-status training">Training</div>
                            </div>
                            <div class="model-accuracy">94.7%</div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Epochs</span>
                                <span class="metric-value">847/1000</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Loss</span>
                                <span class="metric-value">0.0234</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="neuralNetworkChart"></canvas>
                        </div>
                    </div>

                    <div class="model-card genetic-algorithm">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-dna"></i>
                            </div>
                            <div class="model-info">
                                <h3>Genetic Algorithm</h3>
                                <div class="model-status optimizing">Optimizing</div>
                            </div>
                            <div class="model-fitness">87.3%</div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Generation</span>
                                <span class="metric-value">156/200</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Best Fitness</span>
                                <span class="metric-value">0.873</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="geneticAlgorithmChart"></canvas>
                        </div>
                    </div>

                    <div class="model-card reinforcement">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="model-info">
                                <h3>Reinforcement Learning</h3>
                                <div class="model-status deployed">Deployed</div>
                            </div>
                            <div class="model-reward">+2,847</div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Episodes</span>
                                <span class="metric-value">12,450</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Avg Reward</span>
                                <span class="metric-value">+234.7</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="reinforcementChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel optimization-results-panel">
                    <div class="panel-header">
                        <h3>Optimization Results</h3>
                        <div class="panel-controls">
                            <select class="algorithm-select" data-action="filter">
                                <option value="all">All Algorithms</option>
                                <option value="neural">Neural Networks</option>
                                <option value="genetic">Genetic Algorithm</option>
                                <option value="rl">Reinforcement Learning</option>
                            </select>
                        </div>
                    </div>
                    <div class="optimization-chart">
                        <canvas id="optimizationResultsChart"></canvas>
                    </div>
                </div>

                <div class="panel hyperparameter-panel">
                    <div class="panel-header">
                        <h3>Hyperparameter Tuning</h3>
                        <div class="panel-controls">
                            <button class="btn-icon" data-action="auto-tune">
                                <i class="fas fa-magic"></i>
                            </button>
                            <button class="btn-icon" data-action="manual-tune">
                                <i class="fas fa-sliders-h"></i>
                            </button>
                        </div>
                    </div>
                    <div class="hyperparameter-grid">
                        <div class="parameter-control">
                            <label>Learning Rate</label>
                            <input type="range" min="0.001" max="0.1" step="0.001" value="0.01">
                            <span class="parameter-value">0.01</span>
                        </div>
                        <div class="parameter-control">
                            <label>Batch Size</label>
                            <input type="range" min="16" max="512" step="16" value="64">
                            <span class="parameter-value">64</span>
                        </div>
                        <div class="parameter-control">
                            <label>Hidden Layers</label>
                            <input type="range" min="1" max="10" step="1" value="3">
                            <span class="parameter-value">3</span>
                        </div>
                        <div class="parameter-control">
                            <label>Dropout Rate</label>
                            <input type="range" min="0" max="0.5" step="0.05" value="0.2">
                            <span class="parameter-value">0.2</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getWorkflowOrchestrationPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-sitemap"></i> Workflow Orchestration</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="create-workflow">
                        <i class="fas fa-plus"></i> Create Workflow
                    </button>
                    <button class="btn-secondary" data-action="import">
                        <i class="fas fa-upload"></i> Import
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="workflow-dashboard">
                <div class="workflow-designer">
                    <div class="designer-toolbar">
                        <div class="tool-group">
                            <button class="tool-btn active" data-tool="select">
                                <i class="fas fa-mouse-pointer"></i>
                            </button>
                            <button class="tool-btn" data-tool="node">
                                <i class="fas fa-circle"></i>
                            </button>
                            <button class="tool-btn" data-tool="connection">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="tool-group">
                            <button class="tool-btn" data-action="undo">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="tool-btn" data-action="redo">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                        <div class="tool-group">
                            <button class="tool-btn" data-action="run">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="tool-btn" data-action="stop">
                                <i class="fas fa-stop"></i>
                            </button>
                        </div>
                    </div>
                    <div class="designer-canvas">
                        <svg id="workflowCanvas" width="100%" height="500"></svg>
                    </div>
                </div>

                <div class="workflow-sidebar">
                    <div class="sidebar-section">
                        <h3>Components</h3>
                        <div class="component-list">
                            <div class="component-item" draggable="true" data-type="data-source">
                                <i class="fas fa-database"></i>
                                <span>Data Source</span>
                            </div>
                            <div class="component-item" draggable="true" data-type="processor">
                                <i class="fas fa-cogs"></i>
                                <span>Processor</span>
                            </div>
                            <div class="component-item" draggable="true" data-type="filter">
                                <i class="fas fa-filter"></i>
                                <span>Filter</span>
                            </div>
                            <div class="component-item" draggable="true" data-type="aggregator">
                                <i class="fas fa-compress"></i>
                                <span>Aggregator</span>
                            </div>
                            <div class="component-item" draggable="true" data-type="output">
                                <i class="fas fa-external-link-alt"></i>
                                <span>Output</span>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-section">
                        <h3>Properties</h3>
                        <div class="properties-panel">
                            <div class="property-group">
                                <label>Name</label>
                                <input type="text" placeholder="Component name">
                            </div>
                            <div class="property-group">
                                <label>Type</label>
                                <select>
                                    <option>Select type...</option>
                                    <option>Data Source</option>
                                    <option>Processor</option>
                                    <option>Filter</option>
                                </select>
                            </div>
                            <div class="property-group">
                                <label>Configuration</label>
                                <textarea placeholder="JSON configuration"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel execution-panel">
                    <div class="panel-header">
                        <h3>Execution Status</h3>
                        <div class="panel-controls">
                            <div class="status-indicator running">
                                <i class="fas fa-circle"></i>
                                <span>Running</span>
                            </div>
                        </div>
                    </div>
                    <div class="execution-log">
                        <div class="log-entry success">
                            <span class="timestamp">14:23:45</span>
                            <span class="message">Workflow started successfully</span>
                        </div>
                        <div class="log-entry info">
                            <span class="timestamp">14:23:46</span>
                            <span class="message">Processing data source: WaterQuality_DB</span>
                        </div>
                        <div class="log-entry info">
                            <span class="timestamp">14:23:47</span>
                            <span class="message">Applied filter: pH > 6.5</span>
                        </div>
                        <div class="log-entry success">
                            <span class="timestamp">14:23:48</span>
                            <span class="message">Output generated: 2,847 records</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getKnowledgeGraphsPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-project-diagram"></i> Knowledge Graphs</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="create-graph">
                        <i class="fas fa-plus"></i> Create Graph
                    </button>
                    <button class="btn-secondary" data-action="query">
                        <i class="fas fa-search"></i> SPARQL Query
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="knowledge-dashboard">
                <div class="graph-visualization">
                    <div class="graph-controls">
                        <div class="control-group">
                            <label>Layout</label>
                            <select data-action="layout">
                                <option value="force">Force-directed</option>
                                <option value="hierarchical">Hierarchical</option>
                                <option value="circular">Circular</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>Filter</label>
                            <select data-action="filter">
                                <option value="all">All Entities</option>
                                <option value="water">Water Systems</option>
                                <option value="energy">Energy Grid</option>
                                <option value="climate">Climate Data</option>
                            </select>
                        </div>
                    </div>
                    <div class="graph-canvas">
                        <svg id="knowledgeGraph" width="100%" height="600"></svg>
                    </div>
                </div>

                <div class="panel sparql-panel">
                    <div class="panel-header">
                        <h3>SPARQL Query Interface</h3>
                        <div class="panel-controls">
                            <button class="btn-primary" data-action="execute-query">
                                <i class="fas fa-play"></i> Execute
                            </button>
                            <button class="btn-secondary" data-action="clear-query">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                        </div>
                    </div>
                    <div class="sparql-editor">
                        <textarea placeholder="Enter SPARQL query...">
SELECT ?subject ?predicate ?object
WHERE {
  ?subject ?predicate ?object .
  FILTER(CONTAINS(STR(?subject), "water"))
}
LIMIT 100</textarea>
                    </div>
                    <div class="query-results">
                        <table>
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Predicate</th>
                                    <th>Object</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>water:TreatmentPlant_01</td>
                                    <td>rdf:type</td>
                                    <td>water:TreatmentFacility</td>
                                </tr>
                                <tr>
                                    <td>water:TreatmentPlant_01</td>
                                    <td>water:hasCapacity</td>
                                    <td>50000</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    async getLLMIntegrationPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-comments"></i> LLM Integration</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="new-chat">
                        <i class="fas fa-plus"></i> New Chat
                    </button>
                    <button class="btn-secondary" data-action="settings">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="llm-dashboard">
                <div class="chat-interface">
                    <div class="chat-header">
                        <h3>AI Assistant - Gemini Integration</h3>
                        <div class="chat-status online">
                            <i class="fas fa-circle"></i>
                            <span>Online</span>
                        </div>
                    </div>
                    <div class="chat-messages" id="chatMessages">
                        <div class="message assistant">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    Hello! I'm your AI assistant for water management and decarbonization. How can I help you today?
                                </div>
                                <div class="message-time">14:20</div>
                            </div>
                        </div>
                        <div class="message user">
                            <div class="message-content">
                                <div class="message-text">
                                    What's the current status of our water treatment systems?
                                </div>
                                <div class="message-time">14:21</div>
                            </div>
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="message assistant">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    Based on the latest data, your treatment systems are performing well:
                                    <br>• Primary Treatment: 92% efficiency
                                    <br>• Secondary Treatment: 89% efficiency
                                    <br>• Tertiary Treatment: Currently in maintenance mode
                                    <br><br>Would you like me to provide more detailed analysis or recommendations?
                                </div>
                                <div class="message-time">14:21</div>
                            </div>
                        </div>
                    </div>
                    <div class="chat-input">
                        <form id="chatForm">
                            <input type="text" id="chatInput" placeholder="Ask me anything about your water management system...">
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <div class="panel llm-analytics-panel">
                    <div class="panel-header">
                        <h3>LLM Usage Analytics</h3>
                        <div class="panel-controls">
                            <select data-action="filter">
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                            </select>
                        </div>
                    </div>
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <div class="card-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-value">247</div>
                                <div class="card-label">Total Queries</div>
                            </div>
                        </div>
                        <div class="analytics-card">
                            <div class="card-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-value">1.2s</div>
                                <div class="card-label">Avg Response Time</div>
                            </div>
                        </div>
                        <div class="analytics-card">
                            <div class="card-icon">
                                <i class="fas fa-thumbs-up"></i>
                            </div>
                            <div class="card-content">
                                <div class="card-value">94.7%</div>
                                <div class="card-label">Satisfaction Rate</div>
                            </div>
                        </div>
                    </div>
                    <div class="usage-chart">
                        <canvas id="llmUsageChart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    async getClimateImpactPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-globe-americas"></i> Climate Impact Analysis</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="refresh">
                        <i class="fas fa-sync"></i> Refresh Data
                    </button>
                    <button class="btn-secondary" data-action="forecast">
                        <i class="fas fa-chart-line"></i> Generate Forecast
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>

            <div class="climate-dashboard">
                <div class="climate-overview">
                    <div class="climate-card temperature">
                        <div class="climate-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="climate-content">
                            <div class="climate-value climate-temp">24.3°C</div>
                            <div class="climate-label">Current Temperature</div>
                            <div class="climate-change positive">+1.2°C from avg</div>
                        </div>
                        <div class="climate-chart">
                            <canvas id="temperatureChart"></canvas>
                        </div>
                    </div>

                    <div class="climate-card humidity">
                        <div class="climate-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="climate-content">
                            <div class="climate-value climate-humidity">67%</div>
                            <div class="climate-label">Humidity</div>
                            <div class="climate-change negative">-3% from avg</div>
                        </div>
                        <div class="climate-chart">
                            <canvas id="humidityChart"></canvas>
                        </div>
                    </div>

                    <div class="climate-card precipitation">
                        <div class="climate-icon">
                            <i class="fas fa-cloud-rain"></i>
                        </div>
                        <div class="climate-content">
                            <div class="climate-value">12.4mm</div>
                            <div class="climate-label">Precipitation</div>
                            <div class="climate-change positive">+5.2mm today</div>
                        </div>
                        <div class="climate-chart">
                            <canvas id="precipitationChart"></canvas>
                        </div>
                    </div>

                    <div class="climate-card carbon">
                        <div class="climate-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="climate-content">
                            <div class="climate-value">-847kg</div>
                            <div class="climate-label">CO₂ Reduction</div>
                            <div class="climate-change positive">-12% this month</div>
                        </div>
                        <div class="climate-chart">
                            <canvas id="carbonChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel climate-map-panel">
                    <div class="panel-header">
                        <h3>Global Climate Data</h3>
                        <div class="panel-controls">
                            <select data-action="layer">
                                <option value="temperature">Temperature</option>
                                <option value="precipitation">Precipitation</option>
                                <option value="humidity">Humidity</option>
                                <option value="wind">Wind Patterns</option>
                            </select>
                        </div>
                    </div>
                    <div class="climate-map" id="climateMap"></div>
                </div>
            </div>
        `;
    }

    async getSensorsPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-satellite-dish"></i> Sensor Network Management</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="add-sensor">
                        <i class="fas fa-plus"></i> Add Sensor
                    </button>
                    <button class="btn-secondary" data-action="calibrate">
                        <i class="fas fa-tools"></i> Calibrate
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                </div>
            </div>

            <div class="sensors-dashboard">
                <div class="sensors-overview">
                    <div class="sensor-stats">
                        <div class="stat-card total">
                            <div class="stat-icon">
                                <i class="fas fa-satellite-dish"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value sensors-total">247</div>
                                <div class="stat-label">Total Sensors</div>
                            </div>
                        </div>
                        <div class="stat-card online">
                            <div class="stat-icon">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value sensors-online">234</div>
                                <div class="stat-label">Online</div>
                            </div>
                        </div>
                        <div class="stat-card offline">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value">13</div>
                                <div class="stat-label">Offline</div>
                            </div>
                        </div>
                        <div class="stat-card alerts">
                            <div class="stat-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-value">5</div>
                                <div class="stat-label">Alerts</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel sensor-map-panel">
                    <div class="panel-header">
                        <h3>Sensor Network Map</h3>
                        <div class="panel-controls">
                            <button class="btn-icon active" data-layer="all">
                                <i class="fas fa-globe"></i>
                            </button>
                            <button class="btn-icon" data-layer="water">
                                <i class="fas fa-tint"></i>
                            </button>
                            <button class="btn-icon" data-layer="air">
                                <i class="fas fa-wind"></i>
                            </button>
                            <button class="btn-icon" data-layer="soil">
                                <i class="fas fa-seedling"></i>
                            </button>
                        </div>
                    </div>
                    <div class="sensor-network-map" id="sensorNetworkMap"></div>
                </div>

                <div class="panel sensor-list-panel">
                    <div class="panel-header">
                        <h3>Sensor List</h3>
                        <div class="panel-controls">
                            <input type="search" placeholder="Search sensors...">
                            <select data-action="filter">
                                <option value="all">All Types</option>
                                <option value="water">Water Quality</option>
                                <option value="air">Air Quality</option>
                                <option value="weather">Weather</option>
                                <option value="soil">Soil</option>
                            </select>
                        </div>
                    </div>
                    <div class="sensor-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Last Reading</th>
                                    <th>Battery</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>WQ-001</td>
                                    <td>Water Quality</td>
                                    <td>Treatment Plant A</td>
                                    <td><span class="status online">Online</span></td>
                                    <td>2 min ago</td>
                                    <td>87%</td>
                                    <td>
                                        <button class="btn-icon" data-action="view">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon" data-action="configure">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>AQ-012</td>
                                    <td>Air Quality</td>
                                    <td>Industrial Zone</td>
                                    <td><span class="status online">Online</span></td>
                                    <td>1 min ago</td>
                                    <td>92%</td>
                                    <td>
                                        <button class="btn-icon" data-action="view">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon" data-action="configure">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>WS-045</td>
                                    <td>Weather Station</td>
                                    <td>Central Park</td>
                                    <td><span class="status offline">Offline</span></td>
                                    <td>2 hours ago</td>
                                    <td>23%</td>
                                    <td>
                                        <button class="btn-icon" data-action="view">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon" data-action="configure">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    async getAnalyticsPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-chart-bar"></i> Advanced Analytics</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="create-report">
                        <i class="fas fa-plus"></i> Create Report
                    </button>
                    <button class="btn-secondary" data-action="schedule">
                        <i class="fas fa-calendar"></i> Schedule
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="analytics-dashboard">
                <div class="kpi-grid">
                    <div class="kpi-card efficiency">
                        <div class="kpi-header">
                            <h3>System Efficiency</h3>
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="kpi-value">89.7%</div>
                        <div class="kpi-change positive">+2.3% vs last month</div>
                        <div class="kpi-chart">
                            <canvas id="efficiencyKpiChart"></canvas>
                        </div>
                    </div>

                    <div class="kpi-card cost">
                        <div class="kpi-header">
                            <h3>Operational Cost</h3>
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="kpi-value">$24,567</div>
                        <div class="kpi-change negative">+$1,234 vs last month</div>
                        <div class="kpi-chart">
                            <canvas id="costKpiChart"></canvas>
                        </div>
                    </div>

                    <div class="kpi-card carbon">
                        <div class="kpi-header">
                            <h3>Carbon Footprint</h3>
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="kpi-value">-15.2%</div>
                        <div class="kpi-change positive">-3.4% vs last month</div>
                        <div class="kpi-chart">
                            <canvas id="carbonKpiChart"></canvas>
                        </div>
                    </div>

                    <div class="kpi-card quality">
                        <div class="kpi-header">
                            <h3>Water Quality Score</h3>
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="kpi-value">94.8</div>
                        <div class="kpi-change positive">+1.2 vs last month</div>
                        <div class="kpi-chart">
                            <canvas id="qualityKpiChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel trends-analysis-panel">
                    <div class="panel-header">
                        <h3>Trend Analysis</h3>
                        <div class="panel-controls">
                            <select data-action="metric">
                                <option value="efficiency">Efficiency</option>
                                <option value="cost">Cost</option>
                                <option value="quality">Quality</option>
                                <option value="carbon">Carbon Footprint</option>
                            </select>
                            <select data-action="period">
                                <option value="7d">Last 7 Days</option>
                                <option value="30d">Last 30 Days</option>
                                <option value="90d">Last 90 Days</option>
                                <option value="1y">Last Year</option>
                            </select>
                        </div>
                    </div>
                    <div class="trends-chart">
                        <canvas id="trendsAnalysisChart"></canvas>
                    </div>
                </div>

                <div class="panel predictive-panel">
                    <div class="panel-header">
                        <h3>Predictive Analytics</h3>
                        <div class="panel-controls">
                            <button class="btn-primary" data-action="run-prediction">
                                <i class="fas fa-crystal-ball"></i> Run Prediction
                            </button>
                        </div>
                    </div>
                    <div class="prediction-results">
                        <div class="prediction-item">
                            <div class="prediction-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="prediction-content">
                                <h4>Maintenance Alert</h4>
                                <p>Pump #3 predicted to require maintenance in 7-10 days</p>
                                <div class="prediction-confidence">Confidence: 87%</div>
                            </div>
                        </div>
                        <div class="prediction-item">
                            <div class="prediction-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="prediction-content">
                                <h4>Demand Forecast</h4>
                                <p>Water demand expected to increase by 12% next week</p>
                                <div class="prediction-confidence">Confidence: 92%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getReportsDashboardPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-file-alt"></i> Reports Dashboard</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="create-report">
                        <i class="fas fa-plus"></i> Create Report
                    </button>
                    <button class="btn-secondary" data-action="schedule">
                        <i class="fas fa-calendar"></i> Schedule
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export All
                    </button>
                </div>
            </div>

            <div class="reports-dashboard">
                <div class="report-categories">
                    <div class="category-card operational">
                        <div class="category-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="category-content">
                            <h3>Operational Reports</h3>
                            <div class="category-count">12 reports</div>
                        </div>
                        <div class="category-actions">
                            <button class="btn-secondary" data-action="view-category" data-category="operational">
                                View All
                            </button>
                        </div>
                    </div>

                    <div class="category-card compliance">
                        <div class="category-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="category-content">
                            <h3>Compliance Reports</h3>
                            <div class="category-count">8 reports</div>
                        </div>
                        <div class="category-actions">
                            <button class="btn-secondary" data-action="view-category" data-category="compliance">
                                View All
                            </button>
                        </div>
                    </div>

                    <div class="category-card financial">
                        <div class="category-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="category-content">
                            <h3>Financial Reports</h3>
                            <div class="category-count">6 reports</div>
                        </div>
                        <div class="category-actions">
                            <button class="btn-secondary" data-action="view-category" data-category="financial">
                                View All
                            </button>
                        </div>
                    </div>

                    <div class="category-card environmental">
                        <div class="category-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="category-content">
                            <h3>Environmental Reports</h3>
                            <div class="category-count">15 reports</div>
                        </div>
                        <div class="category-actions">
                            <button class="btn-secondary" data-action="view-category" data-category="environmental">
                                View All
                            </button>
                        </div>
                    </div>
                </div>

                <div class="panel recent-reports-panel">
                    <div class="panel-header">
                        <h3>Recent Reports</h3>
                        <div class="panel-controls">
                            <select data-action="filter">
                                <option value="all">All Reports</option>
                                <option value="operational">Operational</option>
                                <option value="compliance">Compliance</option>
                                <option value="financial">Financial</option>
                                <option value="environmental">Environmental</option>
                            </select>
                        </div>
                    </div>
                    <div class="reports-list">
                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="report-content">
                                <h4>Water Quality Monthly Report</h4>
                                <p>Comprehensive analysis of water quality metrics for November 2024</p>
                                <div class="report-meta">
                                    <span class="report-date">Generated: Nov 30, 2024</span>
                                    <span class="report-type">Operational</span>
                                </div>
                            </div>
                            <div class="report-actions">
                                <button class="btn-icon" data-action="view-report">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" data-action="download-report">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn-icon" data-action="share-report">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </div>

                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="report-content">
                                <h4>Carbon Footprint Analysis</h4>
                                <p>Quarterly assessment of carbon emissions and reduction strategies</p>
                                <div class="report-meta">
                                    <span class="report-date">Generated: Nov 28, 2024</span>
                                    <span class="report-type">Environmental</span>
                                </div>
                            </div>
                            <div class="report-actions">
                                <button class="btn-icon" data-action="view-report">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" data-action="download-report">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn-icon" data-action="share-report">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </div>

                        <div class="report-item">
                            <div class="report-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="report-content">
                                <h4>Regulatory Compliance Report</h4>
                                <p>Status of compliance with environmental regulations and standards</p>
                                <div class="report-meta">
                                    <span class="report-date">Generated: Nov 25, 2024</span>
                                    <span class="report-type">Compliance</span>
                                </div>
                            </div>
                            <div class="report-actions">
                                <button class="btn-icon" data-action="view-report">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-icon" data-action="download-report">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn-icon" data-action="share-report">
                                    <i class="fas fa-share"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel report-builder-panel">
                    <div class="panel-header">
                        <h3>Report Builder</h3>
                        <div class="panel-controls">
                            <button class="btn-primary" data-action="build-report">
                                <i class="fas fa-magic"></i> Build Report
                            </button>
                        </div>
                    </div>
                    <div class="report-builder">
                        <div class="builder-section">
                            <label>Report Type</label>
                            <select data-action="report-type">
                                <option value="">Select type...</option>
                                <option value="operational">Operational</option>
                                <option value="compliance">Compliance</option>
                                <option value="financial">Financial</option>
                                <option value="environmental">Environmental</option>
                            </select>
                        </div>
                        <div class="builder-section">
                            <label>Date Range</label>
                            <div class="date-range">
                                <input type="date" placeholder="Start date">
                                <input type="date" placeholder="End date">
                            </div>
                        </div>
                        <div class="builder-section">
                            <label>Include Sections</label>
                            <div class="section-checkboxes">
                                <label><input type="checkbox" checked> Executive Summary</label>
                                <label><input type="checkbox" checked> Data Analysis</label>
                                <label><input type="checkbox"> Charts & Graphs</label>
                                <label><input type="checkbox"> Recommendations</label>
                                <label><input type="checkbox"> Appendices</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getSystemManagementPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-server"></i> System Management</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="system-scan">
                        <i class="fas fa-search"></i> System Scan
                    </button>
                    <button class="btn-secondary" data-action="backup">
                        <i class="fas fa-save"></i> Backup
                    </button>
                    <button class="btn-secondary" data-action="settings">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                </div>
            </div>

            <div class="system-dashboard">
                <div class="system-overview">
                    <div class="system-card cpu">
                        <div class="system-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="system-content">
                            <div class="system-value">23.4%</div>
                            <div class="system-label">CPU Usage</div>
                            <div class="system-status normal">Normal</div>
                        </div>
                        <div class="system-chart">
                            <canvas id="cpuChart"></canvas>
                        </div>
                    </div>

                    <div class="system-card memory">
                        <div class="system-icon">
                            <i class="fas fa-memory"></i>
                        </div>
                        <div class="system-content">
                            <div class="system-value">67.8%</div>
                            <div class="system-label">Memory Usage</div>
                            <div class="system-status warning">High</div>
                        </div>
                        <div class="system-chart">
                            <canvas id="memoryChart"></canvas>
                        </div>
                    </div>

                    <div class="system-card storage">
                        <div class="system-icon">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="system-content">
                            <div class="system-value">45.2%</div>
                            <div class="system-label">Storage Usage</div>
                            <div class="system-status normal">Normal</div>
                        </div>
                        <div class="system-chart">
                            <canvas id="storageChart"></canvas>
                        </div>
                    </div>

                    <div class="system-card network">
                        <div class="system-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="system-content">
                            <div class="system-value">12.3 MB/s</div>
                            <div class="system-label">Network I/O</div>
                            <div class="system-status normal">Normal</div>
                        </div>
                        <div class="system-chart">
                            <canvas id="networkChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel services-panel">
                    <div class="panel-header">
                        <h3>System Services</h3>
                        <div class="panel-controls">
                            <button class="btn-secondary" data-action="refresh-services">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="services-list">
                        <div class="service-item">
                            <div class="service-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="service-content">
                                <h4>Database Service</h4>
                                <p>PostgreSQL database server</p>
                            </div>
                            <div class="service-status running">Running</div>
                            <div class="service-actions">
                                <button class="btn-icon" data-action="restart-service">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="btn-icon" data-action="stop-service">
                                    <i class="fas fa-stop"></i>
                                </button>
                            </div>
                        </div>

                        <div class="service-item">
                            <div class="service-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="service-content">
                                <h4>Web Server</h4>
                                <p>FastAPI application server</p>
                            </div>
                            <div class="service-status running">Running</div>
                            <div class="service-actions">
                                <button class="btn-icon" data-action="restart-service">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="btn-icon" data-action="stop-service">
                                    <i class="fas fa-stop"></i>
                                </button>
                            </div>
                        </div>

                        <div class="service-item">
                            <div class="service-icon">
                                <i class="fas fa-satellite-dish"></i>
                            </div>
                            <div class="service-content">
                                <h4>Sensor Gateway</h4>
                                <p>IoT sensor data collection service</p>
                            </div>
                            <div class="service-status running">Running</div>
                            <div class="service-actions">
                                <button class="btn-icon" data-action="restart-service">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="btn-icon" data-action="stop-service">
                                    <i class="fas fa-stop"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel logs-panel">
                    <div class="panel-header">
                        <h3>System Logs</h3>
                        <div class="panel-controls">
                            <select data-action="log-level">
                                <option value="all">All Levels</option>
                                <option value="error">Error</option>
                                <option value="warning">Warning</option>
                                <option value="info">Info</option>
                            </select>
                            <button class="btn-secondary" data-action="clear-logs">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                        </div>
                    </div>
                    <div class="logs-container">
                        <div class="log-entry info">
                            <span class="log-timestamp">2024-12-01 14:23:45</span>
                            <span class="log-level info">INFO</span>
                            <span class="log-message">System startup completed successfully</span>
                        </div>
                        <div class="log-entry warning">
                            <span class="log-timestamp">2024-12-01 14:22:12</span>
                            <span class="log-level warning">WARN</span>
                            <span class="log-message">Memory usage approaching 70% threshold</span>
                        </div>
                        <div class="log-entry info">
                            <span class="log-timestamp">2024-12-01 14:21:33</span>
                            <span class="log-level info">INFO</span>
                            <span class="log-message">Database connection established</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getAdvancedAIDashboardPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-brain"></i> Advanced AI Dashboard</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="deploy-model">
                        <i class="fas fa-rocket"></i> Deploy Model
                    </button>
                    <button class="btn-secondary" data-action="federated-learning">
                        <i class="fas fa-network-wired"></i> Federated Learning
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="advanced-ai-dashboard">
                <div class="ai-models-grid">
                    <div class="ai-model-card federated">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="model-info">
                                <h3>Federated Learning</h3>
                                <div class="model-status training">Training</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Nodes</span>
                                <span class="metric-value">12/15</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Rounds</span>
                                <span class="metric-value">47/100</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Accuracy</span>
                                <span class="metric-value">91.3%</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="federatedLearningChart"></canvas>
                        </div>
                    </div>

                    <div class="ai-model-card reinforcement">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="model-info">
                                <h3>Reinforcement Learning</h3>
                                <div class="model-status deployed">Deployed</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Episodes</span>
                                <span class="metric-value">15,247</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Reward</span>
                                <span class="metric-value">+2,847</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Success Rate</span>
                                <span class="metric-value">94.7%</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="reinforcementLearningChart"></canvas>
                        </div>
                    </div>

                    <div class="ai-model-card transfer">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="model-info">
                                <h3>Transfer Learning</h3>
                                <div class="model-status optimizing">Optimizing</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Source Models</span>
                                <span class="metric-value">3</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Transfer Rate</span>
                                <span class="metric-value">87.2%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Fine-tuning</span>
                                <span class="metric-value">67%</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="transferLearningChart"></canvas>
                        </div>
                    </div>

                    <div class="ai-model-card interpretability">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="model-info">
                                <h3>Model Interpretability</h3>
                                <div class="model-status analyzing">Analyzing</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">SHAP Values</span>
                                <span class="metric-value">Generated</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">LIME Score</span>
                                <span class="metric-value">0.847</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Feature Importance</span>
                                <span class="metric-value">Updated</span>
                            </div>
                        </div>
                        <div class="model-chart">
                            <canvas id="interpretabilityChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="panel model-comparison-panel">
                    <div class="panel-header">
                        <h3>Model Performance Comparison</h3>
                        <div class="panel-controls">
                            <select data-action="metric">
                                <option value="accuracy">Accuracy</option>
                                <option value="precision">Precision</option>
                                <option value="recall">Recall</option>
                                <option value="f1">F1 Score</option>
                            </select>
                        </div>
                    </div>
                    <div class="comparison-chart">
                        <canvas id="modelComparisonChart"></canvas>
                    </div>
                </div>

                <div class="panel explainability-panel">
                    <div class="panel-header">
                        <h3>Model Explainability</h3>
                        <div class="panel-controls">
                            <select data-action="explanation-type">
                                <option value="shap">SHAP Values</option>
                                <option value="lime">LIME Explanations</option>
                                <option value="feature-importance">Feature Importance</option>
                            </select>
                        </div>
                    </div>
                    <div class="explainability-visualization">
                        <canvas id="explainabilityChart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    async getDigitalTwinDashboardPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-cube"></i> Digital Twin Dashboard</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="run-simulation">
                        <i class="fas fa-play"></i> Run Simulation
                    </button>
                    <button class="btn-secondary" data-action="sync-data">
                        <i class="fas fa-sync"></i> Sync Data
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="digital-twin-dashboard">
                <div class="twin-visualization">
                    <div class="visualization-controls">
                        <div class="control-group">
                            <label>View Mode</label>
                            <select data-action="view-mode">
                                <option value="3d">3D Model</option>
                                <option value="schematic">Schematic</option>
                                <option value="data-flow">Data Flow</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label>System</label>
                            <select data-action="system">
                                <option value="treatment-plant">Treatment Plant</option>
                                <option value="distribution">Distribution Network</option>
                                <option value="energy-grid">Energy Grid</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <button class="btn-icon" data-action="reset-view">
                                <i class="fas fa-home"></i>
                            </button>
                            <button class="btn-icon" data-action="fullscreen">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="twin-3d-container">
                        <div id="digitalTwin3D" class="twin-3d-canvas"></div>
                    </div>
                </div>

                <div class="panel simulation-panel">
                    <div class="panel-header">
                        <h3>Simulation Controls</h3>
                        <div class="panel-controls">
                            <div class="simulation-status running">
                                <i class="fas fa-circle"></i>
                                <span>Running</span>
                            </div>
                        </div>
                    </div>
                    <div class="simulation-controls">
                        <div class="control-section">
                            <label>Simulation Speed</label>
                            <input type="range" min="0.1" max="10" step="0.1" value="1" data-control="speed">
                            <span class="control-value">1x</span>
                        </div>
                        <div class="control-section">
                            <label>Time Range</label>
                            <select data-control="time-range">
                                <option value="1h">1 Hour</option>
                                <option value="24h">24 Hours</option>
                                <option value="7d">7 Days</option>
                                <option value="30d">30 Days</option>
                            </select>
                        </div>
                        <div class="control-section">
                            <label>Scenario</label>
                            <select data-control="scenario">
                                <option value="normal">Normal Operation</option>
                                <option value="peak-demand">Peak Demand</option>
                                <option value="maintenance">Maintenance Mode</option>
                                <option value="emergency">Emergency Response</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="panel real-time-sync-panel">
                    <div class="panel-header">
                        <h3>Real-time Synchronization</h3>
                        <div class="panel-controls">
                            <div class="sync-indicator active">
                                <i class="fas fa-sync fa-spin"></i>
                                <span>Syncing</span>
                            </div>
                        </div>
                    </div>
                    <div class="sync-metrics">
                        <div class="sync-metric">
                            <div class="metric-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">2.3s</div>
                                <div class="metric-label">Data Latency</div>
                            </div>
                        </div>
                        <div class="sync-metric">
                            <div class="metric-icon">
                                <i class="fas fa-satellite-dish"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">247/250</div>
                                <div class="metric-label">Sensors Online</div>
                            </div>
                        </div>
                        <div class="sync-metric">
                            <div class="metric-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">99.2%</div>
                                <div class="metric-label">Sync Accuracy</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel predictive-analytics-panel">
                    <div class="panel-header">
                        <h3>Predictive Analytics</h3>
                        <div class="panel-controls">
                            <button class="btn-primary" data-action="run-prediction">
                                <i class="fas fa-crystal-ball"></i> Run Prediction
                            </button>
                        </div>
                    </div>
                    <div class="prediction-results">
                        <div class="prediction-chart">
                            <canvas id="predictionChart"></canvas>
                        </div>
                        <div class="prediction-insights">
                            <div class="insight-item">
                                <div class="insight-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="insight-content">
                                    <h4>Maintenance Required</h4>
                                    <p>Pump #3 predicted to fail in 5-7 days</p>
                                    <div class="insight-confidence">Confidence: 89%</div>
                                </div>
                            </div>
                            <div class="insight-item">
                                <div class="insight-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="insight-content">
                                    <h4>Demand Spike</h4>
                                    <p>Water demand will increase by 15% next week</p>
                                    <div class="insight-confidence">Confidence: 94%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getBlockchainDashboardPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-link"></i> Blockchain Dashboard</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="create-transaction">
                        <i class="fas fa-plus"></i> New Transaction
                    </button>
                    <button class="btn-secondary" data-action="deploy-contract">
                        <i class="fas fa-file-contract"></i> Deploy Contract
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>

            <div class="blockchain-dashboard">
                <div class="blockchain-overview">
                    <div class="blockchain-card blocks">
                        <div class="blockchain-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="blockchain-content">
                            <div class="blockchain-value">12,847</div>
                            <div class="blockchain-label">Total Blocks</div>
                            <div class="blockchain-change positive">+23 today</div>
                        </div>
                    </div>

                    <div class="blockchain-card transactions">
                        <div class="blockchain-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="blockchain-content">
                            <div class="blockchain-value">247,891</div>
                            <div class="blockchain-label">Transactions</div>
                            <div class="blockchain-change positive">+1,234 today</div>
                        </div>
                    </div>

                    <div class="blockchain-card nodes">
                        <div class="blockchain-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div class="blockchain-content">
                            <div class="blockchain-value">47</div>
                            <div class="blockchain-label">Active Nodes</div>
                            <div class="blockchain-change neutral">No change</div>
                        </div>
                    </div>

                    <div class="blockchain-card hash-rate">
                        <div class="blockchain-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="blockchain-content">
                            <div class="blockchain-value">2.3 TH/s</div>
                            <div class="blockchain-label">Hash Rate</div>
                            <div class="blockchain-change positive">+5.2% today</div>
                        </div>
                    </div>
                </div>

                <div class="panel network-visualization-panel">
                    <div class="panel-header">
                        <h3>Network Visualization</h3>
                        <div class="panel-controls">
                            <select data-action="view-type">
                                <option value="network">Network Topology</option>
                                <option value="transactions">Transaction Flow</option>
                                <option value="blocks">Block Chain</option>
                            </select>
                        </div>
                    </div>
                    <div class="network-visualization">
                        <svg id="blockchainNetwork" width="100%" height="400"></svg>
                    </div>
                </div>

                <div class="panel transaction-pool-panel">
                    <div class="panel-header">
                        <h3>Transaction Pool</h3>
                        <div class="panel-controls">
                            <div class="pool-status">
                                <span class="status-indicator active"></span>
                                <span>247 pending</span>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-list">
                        <div class="transaction-item">
                            <div class="transaction-hash">0x1a2b3c4d5e6f...</div>
                            <div class="transaction-details">
                                <span class="transaction-type">Water Quality Data</span>
                                <span class="transaction-value">0.001 ETH</span>
                                <span class="transaction-time">2 min ago</span>
                            </div>
                            <div class="transaction-status pending">Pending</div>
                        </div>
                        <div class="transaction-item">
                            <div class="transaction-hash">0x2b3c4d5e6f7a...</div>
                            <div class="transaction-details">
                                <span class="transaction-type">Energy Certificate</span>
                                <span class="transaction-value">0.002 ETH</span>
                                <span class="transaction-time">5 min ago</span>
                            </div>
                            <div class="transaction-status confirmed">Confirmed</div>
                        </div>
                        <div class="transaction-item">
                            <div class="transaction-hash">0x3c4d5e6f7a8b...</div>
                            <div class="transaction-details">
                                <span class="transaction-type">Carbon Credit</span>
                                <span class="transaction-value">0.005 ETH</span>
                                <span class="transaction-time">8 min ago</span>
                            </div>
                            <div class="transaction-status confirmed">Confirmed</div>
                        </div>
                    </div>
                </div>

                <div class="panel smart-contracts-panel">
                    <div class="panel-header">
                        <h3>Smart Contracts</h3>
                        <div class="panel-controls">
                            <button class="btn-primary" data-action="deploy-contract">
                                <i class="fas fa-plus"></i> Deploy
                            </button>
                        </div>
                    </div>
                    <div class="contracts-list">
                        <div class="contract-item">
                            <div class="contract-icon">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <div class="contract-content">
                                <h4>Water Quality Verification</h4>
                                <p>Automated verification of water quality data</p>
                                <div class="contract-address">0x4d5e6f7a8b9c...</div>
                            </div>
                            <div class="contract-status active">Active</div>
                        </div>
                        <div class="contract-item">
                            <div class="contract-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="contract-content">
                                <h4>Carbon Credit Trading</h4>
                                <p>Automated carbon credit trading and verification</p>
                                <div class="contract-address">0x5e6f7a8b9c0d...</div>
                            </div>
                            <div class="contract-status active">Active</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getPredictiveMaintenanceDashboardPageContent() {
        return `
            <div class="page-header">
                <h1><i class="fas fa-tools"></i> Predictive Maintenance</h1>
                <div class="page-actions">
                    <button class="btn-primary" data-action="schedule-maintenance">
                        <i class="fas fa-calendar-plus"></i> Schedule Maintenance
                    </button>
                    <button class="btn-secondary" data-action="run-analysis">
                        <i class="fas fa-chart-line"></i> Run Analysis
                    </button>
                    <button class="btn-secondary" data-action="export">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>

            <div class="maintenance-dashboard">
                <div class="equipment-overview">
                    <div class="equipment-card pumps">
                        <div class="equipment-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="equipment-content">
                            <div class="equipment-count">12</div>
                            <div class="equipment-label">Water Pumps</div>
                            <div class="equipment-health">
                                <div class="health-bar">
                                    <div class="health-fill" style="width: 87%"></div>
                                </div>
                                <span>87% Health</span>
                            </div>
                        </div>
                        <div class="equipment-alerts">
                            <span class="alert-count">2</span>
                        </div>
                    </div>

                    <div class="equipment-card filters">
                        <div class="equipment-icon">
                            <i class="fas fa-filter"></i>
                        </div>
                        <div class="equipment-content">
                            <div class="equipment-count">8</div>
                            <div class="equipment-label">Filtration Systems</div>
                            <div class="equipment-health">
                                <div class="health-bar">
                                    <div class="health-fill" style="width: 92%"></div>
                                </div>
                                <span>92% Health</span>
                            </div>
                        </div>
                        <div class="equipment-alerts">
                            <span class="alert-count">0</span>
                        </div>
                    </div>

                    <div class="equipment-card sensors">
                        <div class="equipment-icon">
                            <i class="fas fa-satellite-dish"></i>
                        </div>
                        <div class="equipment-content">
                            <div class="equipment-count">247</div>
                            <div class="equipment-label">Sensors</div>
                            <div class="equipment-health">
                                <div class="health-bar">
                                    <div class="health-fill" style="width: 94%"></div>
                                </div>
                                <span>94% Health</span>
                            </div>
                        </div>
                        <div class="equipment-alerts">
                            <span class="alert-count">5</span>
                        </div>
                    </div>

                    <div class="equipment-card valves">
                        <div class="equipment-icon">
                            <i class="fas fa-valve"></i>
                        </div>
                        <div class="equipment-content">
                            <div class="equipment-count">34</div>
                            <div class="equipment-label">Control Valves</div>
                            <div class="equipment-health">
                                <div class="health-bar">
                                    <div class="health-fill" style="width: 89%"></div>
                                </div>
                                <span>89% Health</span>
                            </div>
                        </div>
                        <div class="equipment-alerts">
                            <span class="alert-count">1</span>
                        </div>
                    </div>
                </div>

                <div class="panel maintenance-schedule-panel">
                    <div class="panel-header">
                        <h3>Maintenance Schedule</h3>
                        <div class="panel-controls">
                            <select data-action="time-range">
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="quarter">This Quarter</option>
                            </select>
                        </div>
                    </div>
                    <div class="maintenance-calendar">
                        <div class="calendar-header">
                            <div class="calendar-nav">
                                <button class="btn-icon" data-action="prev-month">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <h4>December 2024</h4>
                                <button class="btn-icon" data-action="next-month">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="calendar-grid">
                            <div class="calendar-day">
                                <div class="day-number">1</div>
                            </div>
                            <div class="calendar-day">
                                <div class="day-number">2</div>
                            </div>
                            <div class="calendar-day has-maintenance">
                                <div class="day-number">3</div>
                                <div class="maintenance-item">Pump #3 Service</div>
                            </div>
                            <div class="calendar-day">
                                <div class="day-number">4</div>
                            </div>
                            <div class="calendar-day">
                                <div class="day-number">5</div>
                            </div>
                            <div class="calendar-day has-maintenance">
                                <div class="day-number">6</div>
                                <div class="maintenance-item">Filter Replacement</div>
                            </div>
                            <div class="calendar-day">
                                <div class="day-number">7</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel failure-prediction-panel">
                    <div class="panel-header">
                        <h3>Failure Predictions</h3>
                        <div class="panel-controls">
                            <button class="btn-primary" data-action="run-prediction">
                                <i class="fas fa-crystal-ball"></i> Update Predictions
                            </button>
                        </div>
                    </div>
                    <div class="prediction-list">
                        <div class="prediction-item high-risk">
                            <div class="prediction-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="prediction-content">
                                <h4>Pump #3 - High Risk</h4>
                                <p>Predicted failure in 5-7 days</p>
                                <div class="prediction-details">
                                    <span class="confidence">Confidence: 89%</span>
                                    <span class="impact">Impact: High</span>
                                </div>
                            </div>
                            <div class="prediction-actions">
                                <button class="btn-primary" data-action="schedule-immediate">
                                    Schedule Now
                                </button>
                            </div>
                        </div>

                        <div class="prediction-item medium-risk">
                            <div class="prediction-icon">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="prediction-content">
                                <h4>Filter Bank A - Medium Risk</h4>
                                <p>Predicted maintenance needed in 2-3 weeks</p>
                                <div class="prediction-details">
                                    <span class="confidence">Confidence: 76%</span>
                                    <span class="impact">Impact: Medium</span>
                                </div>
                            </div>
                            <div class="prediction-actions">
                                <button class="btn-secondary" data-action="schedule-maintenance">
                                    Schedule
                                </button>
                            </div>
                        </div>

                        <div class="prediction-item low-risk">
                            <div class="prediction-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="prediction-content">
                                <h4>Sensor Array B - Low Risk</h4>
                                <p>Routine calibration recommended in 1 month</p>
                                <div class="prediction-details">
                                    <span class="confidence">Confidence: 92%</span>
                                    <span class="impact">Impact: Low</span>
                                </div>
                            </div>
                            <div class="prediction-actions">
                                <button class="btn-secondary" data-action="add-to-schedule">
                                    Add to Schedule
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Page initialization methods
    initializeWaterQualityPage() {
        // Initialize water quality specific charts and interactions
        this.createWaterQualityCharts();
    }

    initializeEnergyGridPage() {
        // Initialize energy grid specific visualizations
        this.createEnergyGridVisualizations();
    }

    initializeAIAgentsPage() {
        // Initialize AI agents specific functionality
        this.createAgentNetworkVisualization();
    }

    initializeClimateImpactPage() {
        // Initialize climate impact specific charts
        this.createClimateCharts();
    }

    initializeSensorsPage() {
        // Initialize sensor network specific functionality
        this.createSensorNetworkMap();
    }

    initializeAnalyticsPage() {
        // Initialize analytics specific charts
        this.createAnalyticsCharts();
    }

    // New page initialization methods
    initializeTreatmentSystemsPage() {
        // Initialize treatment systems specific functionality
        this.createTreatmentSystemCharts();
        this.setupTreatmentSystemInteractions();
    }

    initializeMLOptimizationPage() {
        // Initialize ML optimization specific functionality
        this.createMLModelCharts();
        this.setupGeneticAlgorithmVisualization();
    }

    initializeWorkflowOrchestrationPage() {
        // Initialize workflow orchestration specific functionality
        this.setupWorkflowDesigner();
        this.createWorkflowVisualization();
    }

    initializeSystemManagementPage() {
        // Initialize system management specific functionality
        this.createSystemHealthCharts();
        this.setupSystemMonitoring();
    }

    initializeKnowledgeGraphsPage() {
        // Initialize knowledge graphs specific functionality
        this.createKnowledgeGraphVisualization();
        this.setupSPARQLInterface();
    }

    initializeLLMIntegrationPage() {
        // Initialize LLM integration specific functionality
        this.setupChatInterface();
        this.createLLMUsageCharts();
    }

    initializeReportsDashboardPage() {
        // Initialize reports dashboard specific functionality
        this.createReportAnalyticsCharts();
        this.setupReportBuilder();
    }

    initializeAdvancedAIDashboardPage() {
        // Initialize advanced AI dashboard specific functionality
        this.setupFederatedLearning();
        this.createReinforcementLearningCharts();
        this.setupTransferLearning();
        this.createModelInterpretabilityCharts();
    }

    initializeDigitalTwinDashboardPage() {
        // Initialize digital twin dashboard specific functionality
        this.setup3DVisualization();
        this.createSimulationControls();
        this.setupRealTimeSync();
        this.createPredictiveAnalytics();
    }

    initializeBlockchainDashboardPage() {
        // Initialize blockchain dashboard specific functionality
        this.createBlockchainVisualization();
        this.setupTransactionPool();
        this.createNetworkMap();
        this.setupSmartContracts();
    }

    initializePredictiveMaintenanceDashboardPage() {
        // Initialize predictive maintenance dashboard specific functionality
        this.createEquipmentHealthCharts();
        this.setupMaintenanceSchedule();
        this.createPredictionModels();
        this.setupMaintenanceAnalytics();
    }

    // Chart creation methods
    createWaterQualityCharts() {
        // Create pH, turbidity, chlorine charts
        const phCtx = document.getElementById('phChart');
        if (phCtx) {
            new Chart(phCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['6h', '5h', '4h', '3h', '2h', '1h', 'Now'],
                    datasets: [{
                        data: [7.1, 7.0, 7.2, 7.3, 7.1, 7.2, 7.2],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });
        }
    }

    createEnergyGridVisualizations() {
        // Create energy consumption gauges and grid topology
        const consumptionCtx = document.getElementById('consumptionGauge');
        if (consumptionCtx) {
            new Chart(consumptionCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [75, 25],
                        backgroundColor: ['#ef4444', 'rgba(255, 255, 255, 0.1)'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } }
                }
            });
        }
    }

    createAgentNetworkVisualization() {
        // Create agent communication network visualization
        const svg = document.getElementById('agentNetwork');
        if (svg) {
            // Simple network visualization placeholder
            svg.innerHTML = `
                <circle cx="200" cy="200" r="30" fill="#4ade80" opacity="0.8"/>
                <circle cx="400" cy="150" r="25" fill="#3b82f6" opacity="0.8"/>
                <circle cx="350" cy="250" r="25" fill="#f59e0b" opacity="0.8"/>
                <circle cx="150" cy="300" r="25" fill="#8b5cf6" opacity="0.8"/>
                <line x1="200" y1="200" x2="400" y2="150" stroke="#4ade80" stroke-width="2"/>
                <line x1="200" y1="200" x2="350" y2="250" stroke="#4ade80" stroke-width="2"/>
                <line x1="200" y1="200" x2="150" y2="300" stroke="#4ade80" stroke-width="2"/>
            `;
        }
    }

    createClimateCharts() {
        // Create climate impact charts
    }

    createSensorNetworkMap() {
        // Create sensor network map
    }

    createAnalyticsCharts() {
        // Create analytics charts
    }

    // New chart creation methods
    createTreatmentSystemCharts() {
        // Create treatment system specific charts
    }

    setupTreatmentSystemInteractions() {
        // Setup treatment system interactions
    }

    createMLModelCharts() {
        // Create ML model performance charts
    }

    setupGeneticAlgorithmVisualization() {
        // Setup genetic algorithm visualization
    }

    setupWorkflowDesigner() {
        // Setup workflow designer interface
    }

    createWorkflowVisualization() {
        // Create workflow visualization
    }

    createSystemHealthCharts() {
        // Create system health monitoring charts
    }

    setupSystemMonitoring() {
        // Setup system monitoring functionality
    }

    createKnowledgeGraphVisualization() {
        // Create knowledge graph visualization
    }

    setupSPARQLInterface() {
        // Setup SPARQL query interface
    }

    setupChatInterface() {
        // Setup LLM chat interface
    }

    createLLMUsageCharts() {
        // Create LLM usage analytics charts
    }

    createReportAnalyticsCharts() {
        // Create report analytics charts
    }

    setupReportBuilder() {
        // Setup report builder interface
    }

    // New advanced dashboard methods
    setupFederatedLearning() {
        // Setup federated learning interface
    }

    createReinforcementLearningCharts() {
        // Create RL training charts
    }

    setupTransferLearning() {
        // Setup transfer learning interface
    }

    createModelInterpretabilityCharts() {
        // Create SHAP and LIME charts
    }

    setup3DVisualization() {
        // Setup 3D digital twin visualization
    }

    createSimulationControls() {
        // Create simulation control interface
    }

    setupRealTimeSync() {
        // Setup real-time synchronization
    }

    createPredictiveAnalytics() {
        // Create predictive analytics charts
    }

    createBlockchainVisualization() {
        // Create blockchain network visualization
    }

    setupTransactionPool() {
        // Setup transaction pool interface
    }

    createNetworkMap() {
        // Create blockchain network map
    }

    setupSmartContracts() {
        // Setup smart contracts interface
    }

    createEquipmentHealthCharts() {
        // Create equipment health monitoring charts
    }

    setupMaintenanceSchedule() {
        // Setup maintenance scheduling interface
    }

    createPredictionModels() {
        // Create failure prediction model charts
    }

    setupMaintenanceAnalytics() {
        // Setup maintenance analytics interface
    }

    // Utility methods
    updateRealTimeData() {
        // Update real-time data across all pages using integrated API
        this.fetchRealTimeData();
    }

    async fetchRealTimeData() {
        try {
            // Fetch data from integrated API endpoints
            const [climate, waterQuality, energy, aiAgents, sensors] = await Promise.all([
                fetch('/api/climate/current').then(r => r.json()),
                fetch('/api/water-quality').then(r => r.json()),
                fetch('/api/energy/grid').then(r => r.json()),
                fetch('/api/ai/agents').then(r => r.json()),
                fetch('/api/sensors/network').then(r => r.json())
            ]);

            // Update UI with real data
            this.updateClimateData(climate.data);
            this.updateWaterQualityData(waterQuality.data);
            this.updateEnergyData(energy.data);
            this.updateAIAgentsData(aiAgents.data);
            this.updateSensorData(sensors.data);

        } catch (error) {
            console.error('Error fetching real-time data:', error);
        }
    }

    updateClimateData(data) {
        if (data) {
            // Update climate indicators
            const tempElement = document.querySelector('.climate-temp');
            if (tempElement) tempElement.textContent = `${data.temperature}°C`;

            const humidityElement = document.querySelector('.climate-humidity');
            if (humidityElement) humidityElement.textContent = `${data.humidity}%`;
        }
    }

    updateWaterQualityData(data) {
        if (data) {
            // Update water quality metrics
            const phElement = document.querySelector('.water-ph');
            if (phElement) phElement.textContent = data.ph_level;

            const turbidityElement = document.querySelector('.water-turbidity');
            if (turbidityElement) turbidityElement.textContent = `${data.turbidity} NTU`;
        }
    }

    updateEnergyData(data) {
        if (data) {
            // Update energy metrics
            const consumptionElement = document.querySelector('.energy-consumption');
            if (consumptionElement) consumptionElement.textContent = `${data.total_consumption} kWh`;

            const efficiencyElement = document.querySelector('.energy-efficiency');
            if (efficiencyElement) efficiencyElement.textContent = `${data.grid_efficiency}%`;
        }
    }

    updateAIAgentsData(data) {
        if (data) {
            // Update AI agent status
            Object.keys(data).forEach(agentName => {
                const statusElement = document.querySelector(`.agent-${agentName}-status`);
                if (statusElement) {
                    statusElement.textContent = data[agentName].status;
                    statusElement.className = `agent-status ${data[agentName].status}`;
                }
            });
        }
    }

    updateSensorData(data) {
        if (data) {
            // Update sensor network metrics
            const totalElement = document.querySelector('.sensors-total');
            if (totalElement) totalElement.textContent = data.total_sensors;

            const onlineElement = document.querySelector('.sensors-online');
            if (onlineElement) onlineElement.textContent = data.online_sensors;
        }
    }

    animatePerformanceBars() {
        animatePerformanceBars();
    }

    // Generic action handler for all button clicks
    handleGenericAction(action, button) {
        console.log(`🎯 Handling action: ${action}`);

        // Show visual feedback
        this.showActionFeedback(button, action);

        switch(action) {
            // Data actions
            case 'refresh':
                this.updateRealTimeData();
                break;
            case 'export':
                this.exportData(action);
                break;
            case 'import':
                this.importData();
                break;

            // System actions
            case 'settings':
                this.showSettings();
                break;
            case 'help':
                this.showHelp();
                break;
            case 'backup':
                this.performBackup();
                break;
            case 'system-scan':
                this.performSystemScan();
                break;

            // UI actions
            case 'fullscreen':
                this.toggleFullscreen();
                break;
            case 'minimize':
                this.minimizePanel(button);
                break;
            case 'maximize':
                this.maximizePanel(button);
                break;
            case 'close':
                this.closePanel(button);
                break;

            // Water Quality actions
            case 'add-sensor':
                this.addSensor();
                break;
            case 'calibrate':
                this.calibrateSensors();
                break;
            case 'configure':
                this.showConfiguration();
                break;

            // Treatment Systems actions
            case 'add-system':
                this.addTreatmentSystem();
                break;
            case 'optimize':
                this.optimizeTreatmentSystems();
                break;

            // ML & AI actions
            case 'train-model':
                this.trainModel();
                break;
            case 'deploy':
            case 'deploy-model':
                this.deployModel();
                break;
            case 'auto-tune':
                this.autoTuneHyperparameters();
                break;
            case 'manual-tune':
                this.showManualTuning();
                break;

            // Workflow actions
            case 'create-workflow':
                this.createWorkflow();
                break;
            case 'run':
                this.runWorkflow();
                break;
            case 'stop':
                this.stopWorkflow();
                break;
            case 'undo':
                this.undoWorkflowAction();
                break;
            case 'redo':
                this.redoWorkflowAction();
                break;

            // Knowledge Graph actions
            case 'create-graph':
                this.createKnowledgeGraph();
                break;
            case 'query':
                this.showSPARQLQuery();
                break;
            case 'execute-query':
                this.executeSPARQLQuery();
                break;
            case 'clear-query':
                this.clearSPARQLQuery();
                break;

            // LLM actions
            case 'new-chat':
                this.startNewChat();
                break;

            // Climate actions
            case 'forecast':
                this.generateClimateForcast();
                break;

            // Analytics actions
            case 'create-report':
                this.createReport();
                break;
            case 'schedule':
                this.scheduleReport();
                break;
            case 'run-prediction':
                this.runPredictiveAnalysis();
                break;
            case 'run-analysis':
                this.runAnalysis();
                break;

            // Report actions
            case 'view-report':
                this.viewReport(button);
                break;
            case 'download-report':
                this.downloadReport(button);
                break;
            case 'share-report':
                this.shareReport(button);
                break;
            case 'build-report':
                this.buildCustomReport();
                break;

            // Service management
            case 'restart-service':
                this.restartService(button);
                break;
            case 'stop-service':
                this.stopService(button);
                break;
            case 'refresh-services':
                this.refreshServices();
                break;
            case 'clear-logs':
                this.clearSystemLogs();
                break;

            // Digital Twin actions
            case 'run-simulation':
                this.runSimulation();
                break;
            case 'sync-data':
                this.syncDigitalTwinData();
                break;
            case 'reset-view':
                this.resetDigitalTwinView();
                break;

            // Blockchain actions
            case 'create-transaction':
                this.createBlockchainTransaction();
                break;
            case 'deploy-contract':
                this.deploySmartContract();
                break;

            // Maintenance actions
            case 'schedule-maintenance':
                this.scheduleMaintenanceTask();
                break;
            case 'schedule-immediate':
                this.scheduleImmediateMaintenance(button);
                break;
            case 'add-to-schedule':
                this.addToMaintenanceSchedule(button);
                break;
            case 'prev-month':
                this.navigateCalendar(-1);
                break;
            case 'next-month':
                this.navigateCalendar(1);
                break;

            // View actions
            case 'view':
                this.viewDetails(button);
                break;
            case 'view-category':
                this.viewReportCategory(button);
                break;

            // Federated Learning
            case 'federated-learning':
                this.manageFederatedLearning();
                break;

            default:
                console.log(`⚠️ Unknown action: ${action}`);
                this.showNotification(`Action "${action}" is not implemented yet`, 'info');
        }
    }

    // Form submission handler
    handleFormSubmission(form) {
        console.log('📝 Form submitted:', form.id || 'unnamed form');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        console.log('Form data:', data);
        this.showNotification('Form submitted successfully', 'success');
    }

    // Chat submission handler
    handleChatSubmission(form) {
        const input = form.querySelector('#chatInput');
        const message = input.value.trim();

        if (!message) return;

        console.log('💬 Chat message:', message);

        // Add user message to chat
        this.addChatMessage(message, 'user');

        // Clear input
        input.value = '';

        // Simulate AI response
        setTimeout(() => {
            const responses = [
                "I'm analyzing your water management system data. Based on current metrics, everything looks optimal.",
                "Your treatment systems are performing well. I recommend checking the pH levels in sector 3.",
                "Energy efficiency is up 12% this month. The new optimization algorithms are working effectively.",
                "I've detected a minor anomaly in sensor WQ-045. It may need calibration soon.",
                "Carbon footprint has decreased by 8.7% compared to last month. Great progress!",
                "Water quality metrics are within normal ranges. All systems operational."
            ];
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            this.addChatMessage(randomResponse, 'assistant');
        }, 1500);
    }

    // Add message to chat
    addChatMessage(message, sender) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        if (sender === 'assistant') {
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="message-text">${message}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">${message}</div>
                    <div class="message-time">${time}</div>
                </div>
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Range input handler
    handleRangeInput(input) {
        const value = input.value;
        const valueSpan = input.parentElement.querySelector('.parameter-value, .control-value');

        if (valueSpan) {
            valueSpan.textContent = value + (input.dataset.unit || '');
        }

        console.log(`🎛️ Range input changed: ${input.name || 'unnamed'} = ${value}`);

        // Show notification for important parameter changes
        if (input.name && ['learning-rate', 'batch-size', 'speed'].includes(input.name)) {
            this.showNotification(`${input.name} updated to ${value}`, 'info');
        }
    }

    // Dropdown change handler
    handleDropdownChange(action, value, dropdown) {
        console.log(`📋 Dropdown changed: ${action} = ${value}`);

        // Show visual feedback
        this.showDropdownFeedback(dropdown, value);

        switch(action) {
            // Filtering actions
            case 'filter':
                this.applyFilter(value, dropdown);
                break;
            case 'metric':
                this.changeMetric(value, dropdown);
                break;
            case 'period':
                this.changePeriod(value, dropdown);
                break;
            case 'time-range':
            case 'timerange':
                this.changeTimeRange(value, dropdown);
                break;
            case 'layer':
                this.changeMapLayer(value, dropdown);
                break;
            case 'layout':
                this.changeGraphLayout(value, dropdown);
                break;
            case 'view-mode':
                this.changeViewMode(value, dropdown);
                break;
            case 'system':
                this.changeSystem(value, dropdown);
                break;
            case 'view-type':
                this.changeVisualizationType(value, dropdown);
                break;
            case 'log-level':
                this.changeLogLevel(value, dropdown);
                break;
            case 'report-type':
                this.changeReportType(value, dropdown);
                break;
            case 'explanation-type':
                this.changeExplanationType(value, dropdown);
                break;

            // Sorting actions
            case 'sort':
                this.applySorting(value, dropdown);
                break;

            // View actions
            case 'view':
                this.changeView(value, dropdown);
                break;

            default:
                console.log(`⚠️ Unknown dropdown action: ${action}`);
                this.showNotification(`Dropdown action "${action}" is not implemented yet`, 'info');
        }
    }

    // Utility methods for actions
    exportData(type = 'general') {
        console.log(`📤 Exporting ${type} data...`);
        this.showNotification(`Exporting ${type} data...`, 'info');

        // Simulate export process
        setTimeout(() => {
            this.showNotification(`${type} data exported successfully!`, 'success');
        }, 2000);
    }

    importData() {
        console.log('📥 Importing data...');
        this.showNotification('Import functionality coming soon!', 'info');
    }

    performBackup() {
        console.log('💾 Performing system backup...');
        this.showNotification('Starting system backup...', 'info');

        setTimeout(() => {
            this.showNotification('System backup completed successfully!', 'success');
        }, 3000);
    }

    performSystemScan() {
        console.log('🔍 Performing system scan...');
        this.showNotification('Running system diagnostics...', 'info');

        setTimeout(() => {
            this.showNotification('System scan completed - All systems operational', 'success');
        }, 4000);
    }

    // Sensor management
    addSensor() {
        console.log('📡 Adding new sensor...');
        this.showNotification('Sensor configuration wizard opened', 'info');
    }

    calibrateSensors() {
        console.log('🔧 Calibrating sensors...');
        this.showNotification('Starting sensor calibration...', 'info');

        setTimeout(() => {
            this.showNotification('Sensor calibration completed', 'success');
        }, 5000);
    }

    showConfiguration() {
        console.log('⚙️ Showing configuration...');
        this.showNotification('Configuration panel opened', 'info');
    }

    // Treatment systems
    addTreatmentSystem() {
        console.log('🏭 Adding treatment system...');
        this.showNotification('Treatment system wizard opened', 'info');
    }

    optimizeTreatmentSystems() {
        console.log('⚡ Optimizing treatment systems...');
        this.showNotification('Running optimization algorithms...', 'info');

        setTimeout(() => {
            this.showNotification('Treatment systems optimized - 15% efficiency gain!', 'success');
        }, 6000);
    }

    // ML & AI
    trainModel() {
        console.log('🧠 Training ML model...');
        this.showNotification('Starting model training...', 'info');

        setTimeout(() => {
            this.showNotification('Model training completed - 94.7% accuracy achieved!', 'success');
        }, 8000);
    }

    deployModel() {
        console.log('🚀 Deploying model...');
        this.showNotification('Deploying model to production...', 'info');

        setTimeout(() => {
            this.showNotification('Model deployed successfully!', 'success');
        }, 3000);
    }

    autoTuneHyperparameters() {
        console.log('🎛️ Auto-tuning hyperparameters...');
        this.showNotification('Running automated hyperparameter optimization...', 'info');

        setTimeout(() => {
            this.showNotification('Hyperparameters optimized - Performance improved by 12%!', 'success');
        }, 7000);
    }

    showManualTuning() {
        console.log('🎚️ Showing manual tuning interface...');
        this.showNotification('Manual tuning interface activated', 'info');
    }

    // Workflow management
    createWorkflow() {
        console.log('🔄 Creating new workflow...');
        this.showNotification('Workflow designer opened', 'info');
    }

    runWorkflow() {
        console.log('▶️ Running workflow...');
        this.showNotification('Workflow execution started', 'info');

        setTimeout(() => {
            this.showNotification('Workflow completed successfully!', 'success');
        }, 4000);
    }

    stopWorkflow() {
        console.log('⏹️ Stopping workflow...');
        this.showNotification('Workflow execution stopped', 'warning');
    }

    undoWorkflowAction() {
        console.log('↶ Undoing workflow action...');
        this.showNotification('Last action undone', 'info');
    }

    redoWorkflowAction() {
        console.log('↷ Redoing workflow action...');
        this.showNotification('Action redone', 'info');
    }

    // Knowledge Graph management
    createKnowledgeGraph() {
        console.log('🕸️ Creating knowledge graph...');
        this.showNotification('Knowledge graph builder opened', 'info');
    }

    showSPARQLQuery() {
        console.log('🔍 Opening SPARQL query interface...');
        this.showNotification('SPARQL query interface activated', 'info');
    }

    executeSPARQLQuery() {
        console.log('⚡ Executing SPARQL query...');
        this.showNotification('Executing SPARQL query...', 'info');

        setTimeout(() => {
            this.showNotification('Query executed - 247 results found', 'success');
        }, 2000);
    }

    clearSPARQLQuery() {
        console.log('🗑️ Clearing SPARQL query...');
        const queryTextarea = document.querySelector('.sparql-editor textarea');
        if (queryTextarea) {
            queryTextarea.value = '';
        }
        this.showNotification('Query cleared', 'info');
    }

    // LLM Integration
    startNewChat() {
        console.log('💬 Starting new chat...');
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.innerHTML = `
                <div class="message assistant">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            Hello! I'm your AI assistant. How can I help you today?
                        </div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                </div>
            `;
        }
        this.showNotification('New chat session started', 'info');
    }

    // Climate analysis
    generateClimateForcast() {
        console.log('🌡️ Generating climate forecast...');
        this.showNotification('Generating climate forecast...', 'info');

        setTimeout(() => {
            this.showNotification('Climate forecast generated - Temperature rise of 1.2°C predicted', 'warning');
        }, 3000);
    }

    // Analytics and reporting
    createReport() {
        console.log('📊 Creating new report...');
        this.showNotification('Report builder opened', 'info');
    }

    scheduleReport() {
        console.log('📅 Scheduling report...');
        this.showNotification('Report scheduler opened', 'info');
    }

    runPredictiveAnalysis() {
        console.log('🔮 Running predictive analysis...');
        this.showNotification('Running predictive models...', 'info');

        setTimeout(() => {
            this.showNotification('Predictive analysis completed - 3 critical alerts generated', 'warning');
        }, 5000);
    }

    runAnalysis() {
        console.log('📈 Running analysis...');
        this.showNotification('Running data analysis...', 'info');

        setTimeout(() => {
            this.showNotification('Analysis completed successfully', 'success');
        }, 3000);
    }

    // Report actions
    viewReport(button) {
        console.log('👁️ Viewing report...');
        this.showNotification('Opening report viewer...', 'info');
    }

    downloadReport(button) {
        console.log('⬇️ Downloading report...');
        this.showNotification('Downloading report...', 'info');

        setTimeout(() => {
            this.showNotification('Report downloaded successfully', 'success');
        }, 2000);
    }

    shareReport(button) {
        console.log('📤 Sharing report...');
        this.showNotification('Share options opened', 'info');
    }

    buildCustomReport() {
        console.log('🔨 Building custom report...');
        this.showNotification('Building custom report...', 'info');

        setTimeout(() => {
            this.showNotification('Custom report generated successfully', 'success');
        }, 4000);
    }

    // Service management
    restartService(button) {
        const serviceName = button.closest('.service-item').querySelector('h4').textContent;
        console.log(`🔄 Restarting service: ${serviceName}`);
        this.showNotification(`Restarting ${serviceName}...`, 'info');

        setTimeout(() => {
            this.showNotification(`${serviceName} restarted successfully`, 'success');
        }, 3000);
    }

    stopService(button) {
        const serviceName = button.closest('.service-item').querySelector('h4').textContent;
        console.log(`⏹️ Stopping service: ${serviceName}`);
        this.showNotification(`Stopping ${serviceName}...`, 'warning');

        setTimeout(() => {
            this.showNotification(`${serviceName} stopped`, 'info');
        }, 2000);
    }

    refreshServices() {
        console.log('🔄 Refreshing services...');
        this.showNotification('Refreshing service status...', 'info');

        setTimeout(() => {
            this.showNotification('Service status updated', 'success');
        }, 1500);
    }

    clearSystemLogs() {
        console.log('🗑️ Clearing system logs...');
        const logsContainer = document.querySelector('.logs-container');
        if (logsContainer) {
            logsContainer.innerHTML = '<div class="log-entry info"><span class="log-timestamp">' +
                new Date().toLocaleString() + '</span><span class="log-level info">INFO</span>' +
                '<span class="log-message">System logs cleared</span></div>';
        }
        this.showNotification('System logs cleared', 'info');
    }

    // Digital Twin actions
    runSimulation() {
        console.log('🎮 Running digital twin simulation...');
        this.showNotification('Starting digital twin simulation...', 'info');

        setTimeout(() => {
            this.showNotification('Simulation completed - Results available in analytics', 'success');
        }, 6000);
    }

    syncDigitalTwinData() {
        console.log('🔄 Syncing digital twin data...');
        this.showNotification('Synchronizing with real-time data...', 'info');

        setTimeout(() => {
            this.showNotification('Digital twin synchronized successfully', 'success');
        }, 3000);
    }

    resetDigitalTwinView() {
        console.log('🏠 Resetting digital twin view...');
        this.showNotification('View reset to default position', 'info');
    }

    // Blockchain actions
    createBlockchainTransaction() {
        console.log('⛓️ Creating blockchain transaction...');
        this.showNotification('Transaction builder opened', 'info');
    }

    deploySmartContract() {
        console.log('📜 Deploying smart contract...');
        this.showNotification('Deploying smart contract...', 'info');

        setTimeout(() => {
            this.showNotification('Smart contract deployed successfully', 'success');
        }, 4000);
    }

    // Maintenance actions
    scheduleMaintenanceTask() {
        console.log('📅 Scheduling maintenance task...');
        this.showNotification('Maintenance scheduler opened', 'info');
    }

    scheduleImmediateMaintenance(button) {
        const equipmentName = button.closest('.prediction-item').querySelector('h4').textContent;
        console.log(`⚡ Scheduling immediate maintenance for: ${equipmentName}`);
        this.showNotification(`Immediate maintenance scheduled for ${equipmentName}`, 'warning');
    }

    addToMaintenanceSchedule(button) {
        const equipmentName = button.closest('.prediction-item').querySelector('h4').textContent;
        console.log(`📋 Adding to maintenance schedule: ${equipmentName}`);
        this.showNotification(`${equipmentName} added to maintenance schedule`, 'info');
    }

    navigateCalendar(direction) {
        console.log(`📅 Navigating calendar: ${direction > 0 ? 'next' : 'previous'} month`);
        this.showNotification(`Calendar ${direction > 0 ? 'advanced' : 'moved back'} one month`, 'info');
    }

    // View actions
    viewDetails(button) {
        console.log('👁️ Viewing details...');
        this.showNotification('Detail view opened', 'info');
    }

    viewReportCategory(button) {
        const category = button.dataset.category;
        console.log(`📂 Viewing report category: ${category}`);
        this.showNotification(`Showing ${category} reports`, 'info');
    }

    // Federated Learning
    manageFederatedLearning() {
        console.log('🌐 Managing federated learning...');
        this.showNotification('Federated learning dashboard opened', 'info');
    }

    // Dropdown change handlers
    applyFilter(value, dropdown) {
        console.log(`🔍 Applying filter: ${value}`);
        this.showNotification(`Filter applied: ${value}`, 'info');
    }

    changeMetric(value, dropdown) {
        console.log(`📊 Changing metric to: ${value}`);
        this.showNotification(`Metric changed to ${value}`, 'info');
    }

    changePeriod(value, dropdown) {
        console.log(`📅 Changing period to: ${value}`);
        this.showNotification(`Time period changed to ${value}`, 'info');
        this.updateRealTimeData();
    }

    changeMapLayer(value, dropdown) {
        console.log(`🗺️ Changing map layer to: ${value}`);
        this.showNotification(`Map layer changed to ${value}`, 'info');
    }

    changeGraphLayout(value, dropdown) {
        console.log(`🕸️ Changing graph layout to: ${value}`);
        this.showNotification(`Graph layout changed to ${value}`, 'info');
    }

    changeViewMode(value, dropdown) {
        console.log(`👁️ Changing view mode to: ${value}`);
        this.showNotification(`View mode changed to ${value}`, 'info');
    }

    changeSystem(value, dropdown) {
        console.log(`🏭 Changing system to: ${value}`);
        this.showNotification(`System changed to ${value}`, 'info');
    }

    changeVisualizationType(value, dropdown) {
        console.log(`📈 Changing visualization to: ${value}`);
        this.showNotification(`Visualization changed to ${value}`, 'info');
    }

    changeLogLevel(value, dropdown) {
        console.log(`📝 Changing log level to: ${value}`);
        this.showNotification(`Log level changed to ${value}`, 'info');
    }

    changeReportType(value, dropdown) {
        console.log(`📋 Changing report type to: ${value}`);
        this.showNotification(`Report type changed to ${value}`, 'info');
    }

    changeExplanationType(value, dropdown) {
        console.log(`🧠 Changing explanation type to: ${value}`);
        this.showNotification(`Explanation type changed to ${value}`, 'info');
    }

    applySorting(value, dropdown) {
        console.log(`🔄 Applying sorting: ${value}`);
        this.showNotification(`Sorting applied: ${value}`, 'info');
    }

    changeView(value, dropdown) {
        console.log(`👁️ Changing view to: ${value}`);
        this.showNotification(`View changed to ${value}`, 'info');
    }

    // Feedback methods
    showActionFeedback(button, action) {
        // Add visual feedback to button
        button.style.transform = 'scale(0.95)';
        button.style.opacity = '0.8';

        setTimeout(() => {
            button.style.transform = 'scale(1)';
            button.style.opacity = '1';
        }, 150);
    }

    showDropdownFeedback(dropdown, value) {
        // Add visual feedback to dropdown
        dropdown.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';

        setTimeout(() => {
            dropdown.style.backgroundColor = '';
        }, 300);
    }

    showNotification(message, type = 'info') {
        console.log(`🔔 ${type.toUpperCase()}: ${message}`);

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    getNotificationIcon(type) {
        switch(type) {
            case 'success': return 'check-circle';
            case 'warning': return 'exclamation-triangle';
            case 'error': return 'times-circle';
            default: return 'info-circle';
        }
    }

    getNotificationColor(type) {
        switch(type) {
            case 'success': return '#10b981';
            case 'warning': return '#f59e0b';
            case 'error': return '#ef4444';
            default: return '#3b82f6';
        }
    }

    showSettings() {
        console.log('Showing settings...');
        // Add settings modal logic
    }

    showHelp() {
        console.log('Showing help...');
        // Add help modal logic
    }

    toggleFullscreen() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.documentElement.requestFullscreen();
        }
    }

    minimizePanel(button) {
        const panel = button.closest('.panel');
        if (panel) {
            panel.classList.add('minimized');
        }
    }

    maximizePanel(button) {
        const panel = button.closest('.panel');
        if (panel) {
            panel.classList.remove('minimized');
            panel.classList.add('maximized');
        }
    }

    closePanel(button) {
        const panel = button.closest('.panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    applyFilter(value) {
        console.log(`Applying filter: ${value}`);
        // Add filtering logic
    }

    applySorting(value) {
        console.log(`Applying sorting: ${value}`);
        // Add sorting logic
    }

    changeTimeRange(value) {
        console.log(`Changing time range: ${value}`);
        // Add time range logic
        this.updateRealTimeData();
    }

    // AI Chat functionality
    async sendChatMessage(message) {
        try {
            const response = await fetch('/api/ai/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });

            const data = await response.json();
            return data.response;
        } catch (error) {
            console.error('Error sending chat message:', error);
            return 'Sorry, I encountered an error processing your request.';
        }
    }

    // Load analytics data
    async loadAnalyticsData() {
        try {
            const [kpis, optimization, maintenance, blockchain] = await Promise.all([
                fetch('/api/analytics/kpis').then(r => r.json()),
                fetch('/api/ml/optimization').then(r => r.json()),
                fetch('/api/maintenance/predictions').then(r => r.json()),
                fetch('/api/blockchain/status').then(r => r.json())
            ]);

            this.updateAnalyticsDisplay(kpis.data, optimization.data, maintenance.data, blockchain.data);
        } catch (error) {
            console.error('Error loading analytics data:', error);
        }
    }

    updateAnalyticsDisplay(kpis, optimization, maintenance, blockchain) {
        // Update KPI displays
        if (kpis) {
            const efficiencyElement = document.querySelector('.kpi-efficiency');
            if (efficiencyElement) efficiencyElement.textContent = `${kpis.water_efficiency}%`;

            const savingsElement = document.querySelector('.kpi-savings');
            if (savingsElement) savingsElement.textContent = `$${kpis.cost_savings.toLocaleString()}`;
        }

        // Update optimization results
        if (optimization) {
            const fitnessElement = document.querySelector('.optimization-fitness');
            if (fitnessElement) fitnessElement.textContent = optimization.best_fitness;
        }

        // Update maintenance predictions
        if (maintenance) {
            const alertsElement = document.querySelector('.maintenance-alerts');
            if (alertsElement) alertsElement.textContent = maintenance.critical_alerts;
        }

        // Update blockchain status
        if (blockchain) {
            const blocksElement = document.querySelector('.blockchain-blocks');
            if (blocksElement) blocksElement.textContent = blockchain.total_blocks.toLocaleString();
        }
    }
}

// Initialize the page manager
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Water Management System...');
    window.pageManager = new PageManager();
    window.app = window.pageManager; // Alias for compatibility
    console.log('✅ PageManager initialized');

    // Test if page content methods exist
    const testMethods = [
        'getWaterQualityPageContent',
        'getTreatmentSystemsPageContent',
        'getEnergyGridPageContent',
        'getAIAgentsPageContent'
    ];

    testMethods.forEach(method => {
        if (typeof window.pageManager[method] === 'function') {
            console.log(`✅ ${method} method exists`);
        } else {
            console.error(`❌ ${method} method missing`);
        }
    });

    console.log('🎯 Application ready for navigation');

    // Add test functions to window for debugging
    window.testPageLoading = async function(pageName) {
        console.log(`🧪 Testing page loading for: ${pageName}`);
        try {
            await window.pageManager.loadPage(pageName);
            console.log(`✅ Test successful for page: ${pageName}`);
        } catch (error) {
            console.error(`❌ Test failed for page: ${pageName}`, error);
        }
    };

    window.testContentMethod = async function(pageName) {
        console.log(`🧪 Testing content method for: ${pageName}`);
        try {
            const methodName = `get${pageName.split('-').map(word =>
                word.charAt(0).toUpperCase() + word.slice(1)
            ).join('')}PageContent`;
            console.log(`🔍 Looking for method: ${methodName}`);

            if (typeof window.pageManager[methodName] === 'function') {
                const content = await window.pageManager[methodName]();
                console.log(`✅ Method ${methodName} returned ${content.length} characters`);
                return content;
            } else {
                console.error(`❌ Method ${methodName} not found`);
                return null;
            }
        } catch (error) {
            console.error(`❌ Content method test failed:`, error);
            return null;
        }
    };

    // Force load water quality page to test immediately
    setTimeout(() => {
        console.log('🧪 Force loading water quality page...');
        const waterQualityTab = document.querySelector('[data-page="water-quality"]');
        if (waterQualityTab) {
            console.log('🖱️ Simulating click on water quality...');
            waterQualityTab.click();
        } else {
            console.error('❌ Water quality navigation tab not found');
        }
    }, 1000);

    // Also test content methods directly
    setTimeout(async () => {
        console.log('🧪 Testing content methods directly...');
        await window.testContentMethod('water-quality');
        await window.testContentMethod('treatment-systems');
        await window.testContentMethod('energy-grid');
    }, 500);
});



// Update chart data based on selected view
function updateChartData(chart, view) {
    let newData;
    
    switch(view) {
        case 'Emissions':
            newData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'CO2 Emissions (tCO2)',
                    data: [45, 48, 52, 58, 62, 68, 65, 60, 55, 50, 47, 42],
                    borderColor: '#f87171',
                    backgroundColor: 'rgba(248, 113, 113, 0.2)',
                    fill: true,
                    tension: 0.4
                }]
            };
            break;
        case 'Temperature':
            newData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Temperature (°C)',
                    data: [18, 19, 21, 23, 26, 28, 30, 29, 27, 24, 21, 19],
                    borderColor: '#fbbf24',
                    backgroundColor: 'rgba(251, 191, 36, 0.2)',
                    fill: true,
                    tension: 0.4
                }]
            };
            break;
        case 'Climate Risks':
            newData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Risk Level',
                    data: [3, 3, 4, 4, 5, 6, 7, 6, 5, 4, 3, 3],
                    borderColor: '#dc2626',
                    backgroundColor: 'rgba(220, 38, 38, 0.2)',
                    fill: true,
                    tension: 0.4
                }]
            };
            break;
        default:
            newData = chartConfig.data;
    }
    
    chart.data = newData;
    chart.update();
}

// Simulate real-time data updates
function updateRealTimeData() {
    // Update stat cards with random variations
    const statValues = document.querySelectorAll('.stat-value');
    statValues.forEach(stat => {
        const currentValue = parseFloat(stat.textContent);
        if (!isNaN(currentValue)) {
            const variation = (Math.random() - 0.5) * 2; // ±1% variation
            const newValue = currentValue + variation;
            stat.textContent = newValue.toFixed(1) + (stat.textContent.includes('%') ? '%' : stat.textContent.includes('°C') ? '°C' : '');
        }
    });

    // Update performance bars
    const perfBars = document.querySelectorAll('.perf-fill');
    perfBars.forEach(bar => {
        const currentWidth = parseInt(bar.style.width);
        const variation = Math.random() * 4 - 2; // ±2% variation
        const newWidth = Math.max(0, Math.min(100, currentWidth + variation));
        bar.style.width = newWidth + '%';
        
        // Update corresponding value
        const valueSpan = bar.closest('.performance-item').querySelector('.perf-value');
        if (valueSpan) {
            valueSpan.textContent = Math.round(newWidth) + '%';
        }
    });

    // Update last updated time
    const lastUpdate = document.querySelector('.last-update');
    if (lastUpdate) {
        const now = new Date();
        lastUpdate.textContent = `Last Updated: ${now.toLocaleTimeString()}`;
    }
}

// Animate performance bars on load
function animatePerformanceBars() {
    const perfBars = document.querySelectorAll('.perf-fill');
    perfBars.forEach((bar, index) => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-out';
            bar.style.width = targetWidth;
        }, index * 200);
    });
}

// Add click handlers for interactive elements
document.addEventListener('click', function(e) {
    // Handle alert dismissal
    if (e.target.closest('.alert-item')) {
        const alertItem = e.target.closest('.alert-item');
        alertItem.style.opacity = '0.5';
        setTimeout(() => {
            alertItem.style.opacity = '1';
        }, 2000);
    }

    // Handle stat card interactions
    if (e.target.closest('.stat-card')) {
        const card = e.target.closest('.stat-card');
        card.style.transform = 'scale(0.98)';
        setTimeout(() => {
            card.style.transform = 'scale(1)';
        }, 150);
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case '1':
                e.preventDefault();
                document.querySelector('.nav-tab').click();
                break;
            case 'r':
                e.preventDefault();
                updateRealTimeData();
                break;
        }
    }
});

// Add smooth scrolling for any anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});

// Initialize tooltips for performance indicators
function initializeTooltips() {
    const perfItems = document.querySelectorAll('.performance-item');
    perfItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const label = this.querySelector('.perf-label').textContent;
            const value = this.querySelector('.perf-value').textContent;
            
            // Create tooltip (you can enhance this with a proper tooltip library)
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = `${label}: ${value}`;
            tooltip.style.cssText = `
                position: absolute;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 0.5rem;
                border-radius: 0.25rem;
                font-size: 0.8rem;
                z-index: 1000;
                pointer-events: none;
            `;
            
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + 'px';
            tooltip.style.top = (rect.top - 40) + 'px';
        });
        
        item.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
}

// Call initialization functions
setTimeout(initializeTooltips, 1000);
