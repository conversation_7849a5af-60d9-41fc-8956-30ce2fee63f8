#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test suite for Copernicus Marine Service API integration
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import numpy as np

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.marine_conservation.apis.copernicus_marine_api import (
    CopernicusMarineAPI,
    OceanographicData,
    MarineEcosystemData,
    SeaIceData,
    get_comprehensive_ocean_data
)


class TestCopernicusMarineAPI:
    """Test cases for Copernicus Marine Service API integration"""
    
    @pytest.fixture
    def api_client(self):
        """Create test API client"""
        return CopernicusMarineAPI(
            username="test_user",
            password="test_password"
        )
    
    @pytest.fixture
    def test_coordinates(self):
        """Test coordinates (North Atlantic)"""
        return 45.0, -30.0
    
    @pytest.fixture
    def test_time_range(self):
        """Test time range"""
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=24)
        return start_date, end_date
    
    @pytest.fixture
    def mock_netcdf_data(self):
        """Mock NetCDF data response"""
        return b"mock_netcdf_data_content"
    
    def test_oceanographic_data_creation(self):
        """Test oceanographic data object creation"""
        data = OceanographicData(
            latitude=45.0,
            longitude=-30.0,
            timestamp=datetime.now(),
            sea_surface_temperature=15.5,
            sea_surface_height=0.5,
            salinity=35.2,
            current_velocity_u=0.1,
            current_velocity_v=0.05,
            current_speed=0.112,
            current_direction=26.6,
            wave_height=2.5,
            wave_period=8.0,
            wave_direction=270.0,
            data_source="Copernicus Marine Service"
        )
        
        assert data.latitude == 45.0
        assert data.longitude == -30.0
        assert data.sea_surface_temperature == 15.5
        assert data.salinity == 35.2
        assert data.current_speed == 0.112
        assert data.wave_height == 2.5
        assert data.data_source == "Copernicus Marine Service"
    
    def test_marine_ecosystem_data_creation(self):
        """Test marine ecosystem data object creation"""
        data = MarineEcosystemData(
            latitude=45.0,
            longitude=-30.0,
            timestamp=datetime.now(),
            primary_productivity=55.0,
            dissolved_oxygen=250.0,
            ph_level=8.1,
            nitrate_concentration=5.2,
            phosphate_concentration=0.6,
            chlorophyll_concentration=2.1,
            turbidity=1.5,
            data_source="Copernicus Marine BGC"
        )
        
        assert data.latitude == 45.0
        assert data.longitude == -30.0
        assert data.primary_productivity == 55.0
        assert data.ph_level == 8.1
        assert data.chlorophyll_concentration == 2.1
        assert data.data_source == "Copernicus Marine BGC"
    
    def test_sea_ice_data_creation(self):
        """Test sea ice data object creation"""
        data = SeaIceData(
            latitude=75.0,
            longitude=-150.0,
            timestamp=datetime.now(),
            ice_concentration=0.8,
            ice_thickness=1.5,
            ice_drift_u=0.01,
            ice_drift_v=0.005,
            ice_temperature=-5.0,
            data_source="Copernicus Marine Ice"
        )
        
        assert data.latitude == 75.0
        assert data.longitude == -150.0
        assert data.ice_concentration == 0.8
        assert data.ice_thickness == 1.5
        assert data.ice_temperature == -5.0
        assert data.data_source == "Copernicus Marine Ice"
    
    def test_datasets_configuration(self, api_client):
        """Test datasets configuration"""
        assert "global_analysis" in api_client.datasets
        assert "global_reanalysis" in api_client.datasets
        assert "global_biogeochemistry" in api_client.datasets
        assert "mediterranean" in api_client.datasets
        assert "baltic" in api_client.datasets
        assert "arctic_analysis" in api_client.datasets
        
        # Check dataset IDs
        assert api_client.datasets["global_analysis"] == "GLOBAL_ANALYSISFORECAST_PHY_001_024"
        assert api_client.datasets["mediterranean"] == "MEDSEA_ANALYSISFORECAST_PHY_006_013"
    
    def test_variables_mapping(self, api_client):
        """Test variables mapping"""
        assert api_client.variables["temperature"] == "thetao"
        assert api_client.variables["salinity"] == "so"
        assert api_client.variables["current_u"] == "uo"
        assert api_client.variables["current_v"] == "vo"
        assert api_client.variables["chlorophyll"] == "chl"
        assert api_client.variables["ice_concentration"] == "siconc"
    
    @pytest.mark.asyncio
    async def test_authentication_success(self, api_client):
        """Test successful authentication"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = "Login successful"
        mock_response.url.path = "/dashboard"
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.authenticate()
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_authentication_failure(self, api_client):
        """Test failed authentication"""
        mock_response = AsyncMock()
        mock_response.status = 401
        mock_response.text.return_value = "Authentication failed"
        mock_response.url.path = "/cas/login"
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.post.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.authenticate()
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_get_oceanographic_data_success(self, api_client, test_coordinates, test_time_range, mock_netcdf_data):
        """Test successful oceanographic data retrieval"""
        lat, lon = test_coordinates
        start_date, end_date = test_time_range
        
        # Mock successful MOTU response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = mock_netcdf_data
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            # Mock the NetCDF processing
            with patch.object(api_client, '_process_oceanographic_netcdf') as mock_process:
                mock_data = [
                    OceanographicData(
                        latitude=lat,
                        longitude=lon,
                        timestamp=datetime.now(),
                        sea_surface_temperature=15.5,
                        salinity=35.2,
                        current_speed=0.1,
                        data_source="Copernicus Marine Service"
                    )
                ]
                mock_process.return_value = mock_data
                
                result = await api_client.get_oceanographic_data(lat, lon, start_date, end_date)
                
                assert len(result) == 1
                assert result[0].sea_surface_temperature == 15.5
                assert result[0].salinity == 35.2
                assert result[0].data_source == "Copernicus Marine Service"
    
    @pytest.mark.asyncio
    async def test_get_oceanographic_data_failure(self, api_client, test_coordinates, test_time_range):
        """Test failed oceanographic data retrieval"""
        lat, lon = test_coordinates
        start_date, end_date = test_time_range
        
        # Mock failed MOTU response
        mock_response = AsyncMock()
        mock_response.status = 404
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            result = await api_client.get_oceanographic_data(lat, lon, start_date, end_date)
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_get_marine_ecosystem_data_success(self, api_client, test_coordinates, test_time_range, mock_netcdf_data):
        """Test successful marine ecosystem data retrieval"""
        lat, lon = test_coordinates
        start_date, end_date = test_time_range
        
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = mock_netcdf_data
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            # Mock the NetCDF processing
            with patch.object(api_client, '_process_ecosystem_netcdf') as mock_process:
                mock_data = [
                    MarineEcosystemData(
                        latitude=lat,
                        longitude=lon,
                        timestamp=datetime.now(),
                        chlorophyll_concentration=2.1,
                        ph_level=8.1,
                        dissolved_oxygen=250.0,
                        data_source="Copernicus Marine BGC"
                    )
                ]
                mock_process.return_value = mock_data
                
                result = await api_client.get_marine_ecosystem_data(lat, lon, start_date, end_date)
                
                assert len(result) == 1
                assert result[0].chlorophyll_concentration == 2.1
                assert result[0].ph_level == 8.1
                assert result[0].data_source == "Copernicus Marine BGC"
    
    @pytest.mark.asyncio
    async def test_get_sea_ice_data_success(self, api_client, mock_netcdf_data):
        """Test successful sea ice data retrieval"""
        # Use Arctic coordinates
        lat, lon = 75.0, -150.0
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=24)
        
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = mock_netcdf_data
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            # Mock the NetCDF processing
            with patch.object(api_client, '_process_ice_netcdf') as mock_process:
                mock_data = [
                    SeaIceData(
                        latitude=lat,
                        longitude=lon,
                        timestamp=datetime.now(),
                        ice_concentration=0.8,
                        ice_thickness=1.5,
                        ice_temperature=-5.0,
                        data_source="Copernicus Marine Ice"
                    )
                ]
                mock_process.return_value = mock_data
                
                result = await api_client.get_sea_ice_data(lat, lon, start_date, end_date)
                
                assert len(result) == 1
                assert result[0].ice_concentration == 0.8
                assert result[0].ice_thickness == 1.5
                assert result[0].data_source == "Copernicus Marine Ice"
    
    @pytest.mark.asyncio
    async def test_get_regional_data_mediterranean(self, api_client, mock_netcdf_data):
        """Test regional data retrieval for Mediterranean"""
        # Mediterranean coordinates
        lat, lon = 40.0, 15.0
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=24)
        
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.read.return_value = mock_netcdf_data
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            # Mock the NetCDF processing
            with patch.object(api_client, '_process_oceanographic_netcdf') as mock_process:
                mock_data = [
                    OceanographicData(
                        latitude=lat,
                        longitude=lon,
                        timestamp=datetime.now(),
                        sea_surface_temperature=18.5,
                        salinity=38.5,
                        data_source="Copernicus Marine Service"
                    )
                ]
                mock_process.return_value = mock_data
                
                result = await api_client.get_regional_data("mediterranean", lat, lon, start_date, end_date)
                
                assert len(result) == 1
                assert result[0].sea_surface_temperature == 18.5
                assert result[0].salinity == 38.5  # Higher salinity typical for Mediterranean
    
    @pytest.mark.asyncio
    async def test_get_regional_data_unknown_region(self, api_client):
        """Test regional data retrieval for unknown region"""
        lat, lon = 40.0, 15.0
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=24)
        
        result = await api_client.get_regional_data("unknown_region", lat, lon, start_date, end_date)
        
        assert result == []
    
    @pytest.mark.asyncio
    async def test_process_oceanographic_netcdf(self, api_client):
        """Test NetCDF oceanographic data processing"""
        mock_netcdf_data = b"mock_netcdf_content"
        lat, lon = 45.0, -30.0
        
        result = await api_client._process_oceanographic_netcdf(mock_netcdf_data, lat, lon)
        
        # Should return 24 hourly data points
        assert len(result) == 24
        assert all(isinstance(point, OceanographicData) for point in result)
        assert all(point.latitude == lat for point in result)
        assert all(point.longitude == lon for point in result)
        assert all(point.data_source == "Copernicus Marine Service" for point in result)
    
    @pytest.mark.asyncio
    async def test_process_ecosystem_netcdf(self, api_client):
        """Test NetCDF ecosystem data processing"""
        mock_netcdf_data = b"mock_netcdf_content"
        lat, lon = 45.0, -30.0
        
        result = await api_client._process_ecosystem_netcdf(mock_netcdf_data, lat, lon)
        
        # Should return 24 hourly data points
        assert len(result) == 24
        assert all(isinstance(point, MarineEcosystemData) for point in result)
        assert all(point.latitude == lat for point in result)
        assert all(point.longitude == lon for point in result)
        assert all(point.data_source == "Copernicus Marine BGC" for point in result)
    
    @pytest.mark.asyncio
    async def test_process_ice_netcdf_arctic(self, api_client):
        """Test NetCDF ice data processing for Arctic region"""
        mock_netcdf_data = b"mock_netcdf_content"
        lat, lon = 75.0, -150.0  # Arctic coordinates
        
        result = await api_client._process_ice_netcdf(mock_netcdf_data, lat, lon)
        
        # Should return 24 hourly data points for Arctic region
        assert len(result) == 24
        assert all(isinstance(point, SeaIceData) for point in result)
        assert all(point.latitude == lat for point in result)
        assert all(point.longitude == lon for point in result)
        assert all(point.data_source == "Copernicus Marine Ice" for point in result)
    
    @pytest.mark.asyncio
    async def test_process_ice_netcdf_non_polar(self, api_client):
        """Test NetCDF ice data processing for non-polar region"""
        mock_netcdf_data = b"mock_netcdf_content"
        lat, lon = 45.0, -30.0  # Non-polar coordinates
        
        result = await api_client._process_ice_netcdf(mock_netcdf_data, lat, lon)
        
        # Should return empty list for non-polar regions
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_convenience_function(self, test_coordinates):
        """Test convenience function for comprehensive ocean data"""
        lat, lon = test_coordinates
        
        # Mock the entire API class
        with patch('src.marine_conservation.apis.copernicus_marine_api.CopernicusMarineAPI') as mock_api_class:
            mock_api_instance = AsyncMock()
            
            # Mock return values
            mock_api_instance.get_oceanographic_data.return_value = [
                OceanographicData(lat, lon, datetime.now(), sea_surface_temperature=15.5)
            ]
            mock_api_instance.get_marine_ecosystem_data.return_value = [
                MarineEcosystemData(lat, lon, datetime.now(), chlorophyll_concentration=2.1)
            ]
            mock_api_instance.get_sea_ice_data.return_value = []
            
            mock_api_class.return_value.__aenter__.return_value = mock_api_instance
            
            result = await get_comprehensive_ocean_data(lat, lon, hours_back=6)
            
            assert 'location' in result
            assert 'oceanographic' in result
            assert 'ecosystem' in result
            assert 'sea_ice' in result
            assert 'timestamp' in result
            assert 'data_source' in result
            
            assert result['location']['latitude'] == lat
            assert result['location']['longitude'] == lon
            assert len(result['oceanographic']) == 1
            assert len(result['ecosystem']) == 1
            assert len(result['sea_ice']) == 0
            assert result['data_source'] == 'Copernicus Marine Service'


class TestDataValidation:
    """Test cases for data validation and edge cases"""
    
    def test_oceanographic_data_with_none_values(self):
        """Test oceanographic data creation with None values"""
        data = OceanographicData(
            latitude=45.0,
            longitude=-30.0,
            timestamp=datetime.now()
            # All other fields default to None
        )
        
        assert data.latitude == 45.0
        assert data.longitude == -30.0
        assert data.sea_surface_temperature is None
        assert data.salinity is None
        assert data.current_speed is None
    
    def test_realistic_oceanographic_values(self):
        """Test oceanographic data with realistic values"""
        data = OceanographicData(
            latitude=45.0,
            longitude=-30.0,
            timestamp=datetime.now(),
            sea_surface_temperature=15.5,  # Realistic North Atlantic SST
            salinity=35.2,  # Realistic ocean salinity
            current_speed=0.1,  # Realistic current speed (10 cm/s)
            wave_height=2.5,  # Realistic wave height
            wind_speed=8.0  # Realistic wind speed
        )
        
        # Validate realistic ranges
        assert -2 <= data.sea_surface_temperature <= 35  # Ocean temperature range
        assert 30 <= data.salinity <= 40  # Ocean salinity range
        assert 0 <= data.current_speed <= 5  # Reasonable current speed
        assert 0 <= data.wave_height <= 20  # Reasonable wave height
        assert 0 <= data.wind_speed <= 50  # Reasonable wind speed
    
    def test_ecosystem_data_realistic_values(self):
        """Test ecosystem data with realistic values"""
        data = MarineEcosystemData(
            latitude=45.0,
            longitude=-30.0,
            timestamp=datetime.now(),
            chlorophyll_concentration=2.1,  # mg/m³
            ph_level=8.1,  # Ocean pH
            dissolved_oxygen=250.0,  # mmol/m³
            nitrate_concentration=5.2,  # mmol/m³
            phosphate_concentration=0.6  # mmol/m³
        )
        
        # Validate realistic ranges
        assert 0 <= data.chlorophyll_concentration <= 50  # Chlorophyll range
        assert 7.5 <= data.ph_level <= 8.5  # Ocean pH range
        assert 0 <= data.dissolved_oxygen <= 500  # Oxygen range
        assert 0 <= data.nitrate_concentration <= 50  # Nitrate range
        assert 0 <= data.phosphate_concentration <= 5  # Phosphate range


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
