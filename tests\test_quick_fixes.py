#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Test for Fixed Components
Tests only the components that were fixed
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

print("🔧 QUICK FIX VERIFICATION TESTS")
print("=" * 50)

# Test configuration
TEST_AREA = (119.0, 23.0, 121.0, 25.0)  # Taiwan Strait
TEST_TIMEOUT = 20  # seconds per test

async def test_fixed_components():
    """Test only the components that were fixed"""
    results = {}
    
    print("\n🧪 Testing Fixed Components")
    print("-" * 30)
    
    # Test 1: Water Treatment Agent (was timing out)
    print("1. Testing Water Treatment Agent...")
    try:
        from marine_conservation.agents.water_treatment_marine_agent import WaterTreatmentMarineAgent
        agent = WaterTreatmentMarineAgent()
        
        start_time = time.time()
        result = await asyncio.wait_for(
            agent.optimize_water_treatment(TEST_AREA), 
            timeout=TEST_TIMEOUT
        )
        end_time = time.time()
        
        success = result is not None and hasattr(result, 'optimization_id')
        results['Water Treatment Agent'] = {
            'success': success,
            'time': f"{end_time - start_time:.1f}s",
            'result': f"ID: {getattr(result, 'optimization_id', 'N/A')}"
        }
        print(f"   ✅ PASS - {results['Water Treatment Agent']['time']} - {results['Water Treatment Agent']['result']}")
        
    except Exception as e:
        results['Water Treatment Agent'] = {'success': False, 'error': str(e)}
        print(f"   ❌ FAIL - {e}")
    
    # Test 2: Sustainability Agent (was timing out)
    print("2. Testing Sustainability Agent...")
    try:
        from marine_conservation.agents.sustainability_marine_agent import SustainabilityMarineAgent
        agent = SustainabilityMarineAgent()
        
        start_time = time.time()
        result = await asyncio.wait_for(
            agent.assess_marine_ecosystem(TEST_AREA), 
            timeout=TEST_TIMEOUT
        )
        end_time = time.time()
        
        success = result is not None and hasattr(result, 'assessment_id')
        results['Sustainability Agent'] = {
            'success': success,
            'time': f"{end_time - start_time:.1f}s",
            'result': f"ID: {getattr(result, 'assessment_id', 'N/A')}"
        }
        print(f"   ✅ PASS - {results['Sustainability Agent']['time']} - {results['Sustainability Agent']['result']}")
        
    except Exception as e:
        results['Sustainability Agent'] = {'success': False, 'error': str(e)}
        print(f"   ❌ FAIL - {e}")
    
    # Test 3: AI Recycling Optimizer (missing method)
    print("3. Testing AI Recycling Optimizer...")
    try:
        from marine_conservation.recycling.ai_recycling_optimizer import AIRecyclingOptimizer
        optimizer = AIRecyclingOptimizer()
        
        start_time = time.time()
        result = await asyncio.wait_for(
            optimizer.optimize_recycling_pathways([]), 
            timeout=TEST_TIMEOUT
        )
        end_time = time.time()
        
        success = result is not None
        results['AI Recycling Optimizer'] = {
            'success': success,
            'time': f"{end_time - start_time:.1f}s",
            'result': f"Pathways: {len(result.optimal_pathways) if hasattr(result, 'optimal_pathways') else 'N/A'}"
        }
        print(f"   ✅ PASS - {results['AI Recycling Optimizer']['time']} - {results['AI Recycling Optimizer']['result']}")
        
    except Exception as e:
        results['AI Recycling Optimizer'] = {'success': False, 'error': str(e)}
        print(f"   ❌ FAIL - {e}")
    
    # Test 4: AIS Stream API (VesselData constructor)
    print("4. Testing AIS Stream API...")
    try:
        from marine_conservation.apis.aisstream_api import AISStreamAPI

        api = AISStreamAPI()
        start_time = time.time()
        result = await asyncio.wait_for(
            api.get_vessels_in_area(TEST_AREA),
            timeout=TEST_TIMEOUT
        )
        end_time = time.time()
        
        success = isinstance(result, list) and len(result) > 0
        results['AIS Stream API'] = {
            'success': success,
            'time': f"{end_time - start_time:.1f}s",
            'result': f"Vessels: {len(result) if result else 0}"
        }
        print(f"   ✅ PASS - {results['AIS Stream API']['time']} - {results['AIS Stream API']['result']}")
        
    except Exception as e:
        results['AIS Stream API'] = {'success': False, 'error': str(e)}
        print(f"   ❌ FAIL - {e}")
    
    # Test 5: Unified Platform (was dependent on above fixes)
    print("5. Testing Unified Platform...")
    try:
        from marine_conservation.integrated_platform.simplified_unified_platform import SimplifiedUnifiedPlatform
        
        platform = SimplifiedUnifiedPlatform()
        start_time = time.time()
        result = await asyncio.wait_for(
            platform.execute_integrated_operation(
                area_bbox=TEST_AREA,
                operation_type="quick_test"
            ), 
            timeout=TEST_TIMEOUT * 2  # Give more time for integration
        )
        end_time = time.time()
        
        success = hasattr(result, 'operation_id')
        results['Unified Platform'] = {
            'success': success,
            'time': f"{end_time - start_time:.1f}s",
            'result': f"ID: {getattr(result, 'operation_id', 'N/A')}, Health: {getattr(result, 'overall_health_score', 'N/A'):.2f}"
        }
        print(f"   ✅ PASS - {results['Unified Platform']['time']} - {results['Unified Platform']['result']}")
        
    except Exception as e:
        results['Unified Platform'] = {'success': False, 'error': str(e)}
        print(f"   ❌ FAIL - {e}")
    
    return results

async def main():
    """Run quick fix verification tests"""
    print(f"🕒 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Test Area: Taiwan Strait {TEST_AREA}")
    print(f"⏱️ Timeout per test: {TEST_TIMEOUT} seconds")
    
    start_time = time.time()
    results = await test_fixed_components()
    total_time = time.time() - start_time
    
    # Print summary
    print("\n" + "=" * 50)
    print("🏆 QUICK FIX VERIFICATION RESULTS")
    print("=" * 50)
    
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"📊 Results Summary:")
    print(f"   Tests Passed: {passed}/{total}")
    print(f"   Success Rate: {success_rate:.1f}%")
    print(f"   Total Time: {total_time:.1f} seconds")
    
    print(f"\n📋 Detailed Results:")
    for component, result in results.items():
        if result['success']:
            print(f"   ✅ {component}: {result['time']} - {result['result']}")
        else:
            print(f"   ❌ {component}: {result.get('error', 'Unknown error')}")
    
    # Final status
    if success_rate >= 90:
        status = "🚀 ALL FIXES SUCCESSFUL - READY FOR DEPLOYMENT"
    elif success_rate >= 80:
        status = "✅ MOSTLY FIXED - Minor issues remain"
    else:
        status = "⚠️ FIXES INCOMPLETE - More work needed"
    
    print(f"\n🎯 Fix Status: {status}")
    print("=" * 50)
    
    return results

if __name__ == "__main__":
    # Run quick fix tests
    results = asyncio.run(main())
    
    # Exit with appropriate code
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    success_rate = (passed / total) if total > 0 else 0
    
    if success_rate >= 0.8:
        print("\n✅ Quick fix verification completed successfully!")
        exit(0)
    else:
        print(f"\n⚠️ Some fixes still need work ({success_rate:.1%} success rate)")
        exit(1)
