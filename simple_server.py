#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Water Management System Server
Minimal version focusing on core functionality
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# Fix Windows console encoding for emojis
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
    os.environ['PYTHONIOENCODING'] = 'utf-8'

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse, Response
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Set environment variables
os.environ['GEMINI_API_KEY'] = 'AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk'
os.environ['OPENWEATHER_API_KEY'] = '********************************'

print("🚀 Starting Simple Water Management System...")

# Initialize FastAPI app
app = FastAPI(
    title="Water Management Decarbonisation System",
    description="Simple frontend and backend for water management",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files (frontend)
frontend_path = Path("frontend")
if frontend_path.exists():
    app.mount("/static", StaticFiles(directory="frontend"), name="static")
    print("✅ Frontend static files mounted")

# API Routes
@app.get("/", response_class=HTMLResponse)
async def serve_frontend():
    """Serve the main frontend application"""
    try:
        with open("frontend/index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <head><title>Water Management System</title></head>
            <body>
                <h1>Water Management System</h1>
                <p>Frontend files not found. Please ensure frontend directory exists.</p>
            </body>
        </html>
        """)

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "frontend": True,
            "backend": True
        }
    }

@app.get("/api/climate/current")
async def get_current_climate():
    """Get current climate data"""
    return {
        "status": "success",
        "data": {
            "temperature": 23.5,
            "humidity": 65.2,
            "precipitation": 0.0,
            "wind_speed": 12.3,
            "co2_level": 415.2,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/water-quality")
async def get_water_quality():
    """Get water quality metrics"""
    return {
        "status": "success",
        "data": {
            "ph_level": 7.2,
            "turbidity": 1.5,
            "chlorine": 0.8,
            "bacteria_count": 12,
            "temperature": 18.5,
            "dissolved_oxygen": 8.2,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/energy/grid")
async def get_energy_grid():
    """Get energy grid status"""
    return {
        "status": "success",
        "data": {
            "total_consumption": 2847.5,
            "renewable_generation": 1923.2,
            "grid_efficiency": 94.7,
            "carbon_intensity": 0.23,
            "peak_demand": 3200.0,
            "current_load": 2847.5,
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/ai/agents")
async def get_ai_agents():
    """Get AI agents status"""
    return {
        "status": "success",
        "data": {
            "climate_agent": {"status": "active", "accuracy": 94.7, "last_update": datetime.now().isoformat()},
            "treatment_agent": {"status": "active", "accuracy": 92.3, "last_update": datetime.now().isoformat()},
            "energy_agent": {"status": "active", "accuracy": 96.1, "last_update": datetime.now().isoformat()},
            "optimization_agent": {"status": "active", "accuracy": 89.5, "last_update": datetime.now().isoformat()},
            "risk_agent": {"status": "active", "accuracy": 91.8, "last_update": datetime.now().isoformat()}
        }
    }

@app.post("/api/ai/chat")
async def chat_with_ai(request: Request):
    """Chat with AI using mock responses"""
    try:
        body = await request.json()
        message = body.get("message", "")
        
        return {
            "status": "success", 
            "response": f"AI Response to: '{message}' - The water management system is operating optimally with 94.7% efficiency. Current recommendations include optimizing pump speeds and adjusting chemical dosing based on real-time water quality data."
        }
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Serve static files for frontend assets
@app.get("/styles.css")
async def serve_css():
    try:
        with open("frontend/styles.css", "r", encoding="utf-8") as f:
            css_content = f.read()
        return Response(content=css_content, media_type="text/css")
    except FileNotFoundError:
        return Response(content="/* CSS file not found */", media_type="text/css")

@app.get("/script.js")
async def serve_js():
    try:
        with open("frontend/script.js", "r", encoding="utf-8") as f:
            js_content = f.read()
        return Response(content=js_content, media_type="application/javascript")
    except FileNotFoundError:
        return Response(content="// JS file not found", media_type="application/javascript")

@app.get("/demo-data.js")
async def serve_demo_data():
    try:
        with open("frontend/demo-data.js", "r", encoding="utf-8") as f:
            js_content = f.read()
        return Response(content=js_content, media_type="application/javascript")
    except FileNotFoundError:
        return Response(content="// Demo data file not found", media_type="application/javascript")

if __name__ == "__main__":
    print("🚀 Starting Simple Water Management System...")
    print("📊 Frontend and Backend running on: http://localhost:8001")
    print("🔗 API Documentation: http://localhost:8001/docs")
    print("✅ Simple server ready!")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8001,
        reload=False,
        log_level="info"
    )
