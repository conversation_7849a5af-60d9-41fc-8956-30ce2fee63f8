<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success {
            background: #22c55e;
            color: white;
        }
        .error {
            background: #ef4444;
            color: white;
        }
        .warning {
            background: #f59e0b;
            color: white;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <h1>🧪 Water Management Frontend Functionality Test</h1>
    
    <div class="test-section">
        <h2>📋 Test Results</h2>
        <div id="testResults"></div>
        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div class="test-section">
        <h2>🔗 Navigation Tests</h2>
        <button onclick="testNavigation()">Test Navigation</button>
        <button onclick="testSidebar()">Test Sidebar</button>
        <button onclick="testPageLoading()">Test Page Loading</button>
    </div>

    <div class="test-section">
        <h2>📊 Chart Tests</h2>
        <button onclick="testCharts()">Test Charts</button>
        <button onclick="testChartControls()">Test Chart Controls</button>
    </div>

    <div class="test-section">
        <h2>🎛️ Interactive Elements Tests</h2>
        <button onclick="testButtons()">Test Buttons</button>
        <button onclick="testModals()">Test Modals</button>
        <button onclick="testDropdowns()">Test Dropdowns</button>
    </div>

    <script>
        let testResults = [];

        function addResult(test, status, message) {
            const result = {
                test: test,
                status: status,
                message: message,
                timestamp: new Date().toLocaleTimeString()
            };
            testResults.push(result);
            displayResults();
        }

        function displayResults() {
            const container = document.getElementById('testResults');
            container.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.status}">
                    <strong>[${result.timestamp}] ${result.test}:</strong> ${result.message}
                </div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            displayResults();
        }

        async function runAllTests() {
            clearResults();
            addResult('Test Suite', 'warning', 'Starting comprehensive frontend tests...');
            
            await testNavigation();
            await testSidebar();
            await testPageLoading();
            await testCharts();
            await testChartControls();
            await testButtons();
            await testModals();
            await testDropdowns();
            
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            addResult('Test Suite Complete', 
                errorCount === 0 ? 'success' : 'warning', 
                `✅ ${successCount} passed, ❌ ${errorCount} failed`);
        }

        async function testNavigation() {
            try {
                // Test if main window exists
                if (window.opener) {
                    const mainWindow = window.opener;
                    
                    // Test navigation tabs
                    const navTabs = mainWindow.document.querySelectorAll('.nav-tab');
                    if (navTabs.length > 0) {
                        addResult('Navigation Tabs', 'success', `Found ${navTabs.length} navigation tabs`);
                        
                        // Test clicking each tab
                        let clickableCount = 0;
                        navTabs.forEach((tab, index) => {
                            if (tab.dataset.page) {
                                clickableCount++;
                                // Simulate click
                                tab.click();
                            }
                        });
                        addResult('Navigation Clicks', 'success', `${clickableCount} tabs are clickable`);
                    } else {
                        addResult('Navigation Tabs', 'error', 'No navigation tabs found');
                    }
                } else {
                    addResult('Navigation Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Navigation Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testSidebar() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const sidebarItems = mainWindow.document.querySelectorAll('.sidebar-item');
                    
                    if (sidebarItems.length > 0) {
                        addResult('Sidebar Items', 'success', `Found ${sidebarItems.length} sidebar items`);
                        
                        let clickableCount = 0;
                        sidebarItems.forEach(item => {
                            if (item.dataset.page) {
                                clickableCount++;
                                item.click();
                            }
                        });
                        addResult('Sidebar Clicks', 'success', `${clickableCount} sidebar items are clickable`);
                    } else {
                        addResult('Sidebar Items', 'error', 'No sidebar items found');
                    }
                } else {
                    addResult('Sidebar Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Sidebar Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testPageLoading() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const pages = [
                        'overview', 'water-quality', 'treatment-systems', 'energy-grid', 
                        'ai-agents', 'ml-optimization', 'workflow-orchestration', 
                        'knowledge-graphs', 'llm-integration', 'climate-impact', 
                        'sensors', 'analytics', 'reports-dashboard', 'system-management',
                        'advanced-ai-dashboard', 'digital-twin-dashboard', 
                        'blockchain-dashboard', 'predictive-maintenance-dashboard'
                    ];
                    
                    let loadedPages = 0;
                    for (const pageName of pages) {
                        const pageElement = mainWindow.document.getElementById(`${pageName}-page`);
                        if (pageElement) {
                            loadedPages++;
                        }
                    }
                    
                    addResult('Page Loading', 'success', `${loadedPages}/${pages.length} pages are available`);
                } else {
                    addResult('Page Loading Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Page Loading Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testCharts() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const chartElements = mainWindow.document.querySelectorAll('canvas');
                    
                    if (chartElements.length > 0) {
                        addResult('Charts', 'success', `Found ${chartElements.length} chart canvases`);
                        
                        // Test if Chart.js is loaded
                        if (mainWindow.Chart) {
                            addResult('Chart.js Library', 'success', 'Chart.js is loaded and available');
                        } else {
                            addResult('Chart.js Library', 'error', 'Chart.js library not found');
                        }
                    } else {
                        addResult('Charts', 'warning', 'No chart canvases found');
                    }
                } else {
                    addResult('Charts Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Charts Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testChartControls() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const chartButtons = mainWindow.document.querySelectorAll('.chart-btn');
                    
                    if (chartButtons.length > 0) {
                        addResult('Chart Controls', 'success', `Found ${chartButtons.length} chart control buttons`);
                        
                        // Test clicking chart buttons
                        chartButtons.forEach(btn => {
                            btn.click();
                        });
                        addResult('Chart Button Clicks', 'success', 'Chart buttons are clickable');
                    } else {
                        addResult('Chart Controls', 'warning', 'No chart control buttons found');
                    }
                } else {
                    addResult('Chart Controls Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Chart Controls Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testButtons() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const buttons = mainWindow.document.querySelectorAll('button');
                    
                    if (buttons.length > 0) {
                        addResult('Buttons', 'success', `Found ${buttons.length} buttons in the application`);
                        
                        let clickableButtons = 0;
                        buttons.forEach(btn => {
                            if (!btn.disabled) {
                                clickableButtons++;
                            }
                        });
                        addResult('Button Accessibility', 'success', `${clickableButtons} buttons are enabled and clickable`);
                    } else {
                        addResult('Buttons', 'error', 'No buttons found');
                    }
                } else {
                    addResult('Buttons Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Buttons Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testModals() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const modals = mainWindow.document.querySelectorAll('.modal, #climateModal');
                    
                    if (modals.length > 0) {
                        addResult('Modals', 'success', `Found ${modals.length} modal dialogs`);
                        
                        // Test modal triggers
                        const modalTriggers = mainWindow.document.querySelectorAll('.climate-risk-trigger');
                        if (modalTriggers.length > 0) {
                            addResult('Modal Triggers', 'success', `Found ${modalTriggers.length} modal triggers`);
                        }
                    } else {
                        addResult('Modals', 'warning', 'No modal dialogs found');
                    }
                } else {
                    addResult('Modals Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Modals Test', 'error', `Error: ${error.message}`);
            }
        }

        async function testDropdowns() {
            try {
                if (window.opener) {
                    const mainWindow = window.opener;
                    const selects = mainWindow.document.querySelectorAll('select');
                    
                    if (selects.length > 0) {
                        addResult('Dropdowns', 'success', `Found ${selects.length} dropdown selects`);
                        
                        let functionalDropdowns = 0;
                        selects.forEach(select => {
                            if (select.options.length > 1) {
                                functionalDropdowns++;
                            }
                        });
                        addResult('Dropdown Options', 'success', `${functionalDropdowns} dropdowns have multiple options`);
                    } else {
                        addResult('Dropdowns', 'warning', 'No dropdown selects found');
                    }
                } else {
                    addResult('Dropdowns Test', 'warning', 'Please open this test from the main application');
                }
            } catch (error) {
                addResult('Dropdowns Test', 'error', `Error: ${error.message}`);
            }
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            addResult('Test Page', 'success', 'Frontend functionality test page loaded successfully');
        });
    </script>
</body>
</html>
