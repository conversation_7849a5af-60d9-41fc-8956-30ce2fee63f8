#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real-Time Multi-Source Debris Intelligence System
Fusion of satellite, vessel, oceanographic, and social data for comprehensive marine debris intelligence
"""

import asyncio
import json
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import logging

# Import all data sources
from ..apis.sentinel_hub_api import detect_marine_debris_area, BoundingBox
from ..apis.noaa_ocean_api import get_marine_conditions
from ..apis.copernicus_marine_api import get_comprehensive_ocean_data
from ..apis.planet_labs_api import get_planet_marine_imagery
from ..apis.nasa_open_api import get_nasa_marine_data
from ..apis.aisstream_api import get_maritime_traffic_data
from ..apis.openstreetmap_api import get_coastal_infrastructure_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class IntelligenceReport:
    """Comprehensive marine debris intelligence report"""
    report_id: str
    area_bbox: Tuple[float, float, float, float]
    timestamp: datetime
    confidence_score: float
    debris_detections: List[Dict[str, Any]]
    vessel_correlations: List[Dict[str, Any]]
    environmental_factors: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    recommendations: List[str]
    data_sources: List[str]
    processing_time_seconds: float


@dataclass
class DebrisHotspot:
    """Marine debris hotspot identification"""
    hotspot_id: str
    center_location: Tuple[float, float]
    radius_km: float
    debris_density: float
    confidence: float
    contributing_factors: List[str]
    vessel_activity: Dict[str, Any]
    environmental_conditions: Dict[str, Any]
    timestamp: datetime


class MultiSourceIntelligenceSystem:
    """Real-time multi-source marine debris intelligence system"""
    
    def __init__(self):
        self.data_sources = {
            'satellite_imagery': True,
            'vessel_tracking': True,
            'oceanographic': True,
            'coastal_infrastructure': True,
            'social_media': False  # Placeholder for future implementation
        }
        self.fusion_weights = {
            'satellite_confidence': 0.4,
            'vessel_correlation': 0.3,
            'environmental_factors': 0.2,
            'infrastructure_proximity': 0.1
        }
        self.hotspot_threshold = 0.7
    
    async def generate_intelligence_report(
        self,
        area_bbox: Tuple[float, float, float, float],
        time_window_hours: int = 24
    ) -> IntelligenceReport:
        """Generate comprehensive intelligence report for specified area"""
        start_time = datetime.now()
        report_id = f"intel_{start_time.strftime('%Y%m%d_%H%M%S')}"
        
        try:
            logger.info(f"🔍 Generating intelligence report for area {area_bbox}")
            
            # Collect data from all sources concurrently
            data_tasks = await self._collect_multi_source_data(area_bbox, time_window_hours)
            
            # Fuse and analyze data
            debris_detections = await self._analyze_debris_detections(data_tasks)
            vessel_correlations = await self._analyze_vessel_correlations(data_tasks)
            environmental_factors = await self._analyze_environmental_factors(data_tasks)
            risk_assessment = await self._assess_debris_risk(
                debris_detections, vessel_correlations, environmental_factors
            )
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                debris_detections, vessel_correlations, environmental_factors, risk_assessment
            )
            
            # Calculate overall confidence
            confidence_score = self._calculate_confidence_score(
                debris_detections, vessel_correlations, environmental_factors
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            report = IntelligenceReport(
                report_id=report_id,
                area_bbox=area_bbox,
                timestamp=start_time,
                confidence_score=confidence_score,
                debris_detections=debris_detections,
                vessel_correlations=vessel_correlations,
                environmental_factors=environmental_factors,
                risk_assessment=risk_assessment,
                recommendations=recommendations,
                data_sources=list(self.data_sources.keys()),
                processing_time_seconds=processing_time
            )
            
            logger.info(f"✅ Intelligence report generated: {confidence_score:.2f} confidence")
            return report
            
        except Exception as e:
            logger.error(f"❌ Error generating intelligence report: {e}")
            return IntelligenceReport(
                report_id=report_id,
                area_bbox=area_bbox,
                timestamp=start_time,
                confidence_score=0.0,
                debris_detections=[],
                vessel_correlations=[],
                environmental_factors={},
                risk_assessment={'error': str(e)},
                recommendations=['Data collection failed - retry analysis'],
                data_sources=[],
                processing_time_seconds=(datetime.now() - start_time).total_seconds()
            )
    
    async def _collect_multi_source_data(
        self,
        area_bbox: Tuple[float, float, float, float],
        time_window_hours: int
    ) -> Dict[str, Any]:
        """Collect data from all available sources"""
        # Convert bbox to required formats
        bbox_obj = BoundingBox(area_bbox[0], area_bbox[1], area_bbox[2], area_bbox[3])
        center_lat = (area_bbox[1] + area_bbox[3]) / 2
        center_lon = (area_bbox[0] + area_bbox[2]) / 2
        
        # Create data collection tasks
        tasks = {}
        
        if self.data_sources['satellite_imagery']:
            tasks['sentinel_debris'] = detect_marine_debris_area(bbox_obj, days_back=7)
            tasks['planet_imagery'] = get_planet_marine_imagery(area_bbox, days_back=7)
            tasks['nasa_data'] = get_nasa_marine_data(center_lat, center_lon, days_back=7)
        
        if self.data_sources['vessel_tracking']:
            tasks['vessel_traffic'] = get_maritime_traffic_data(area_bbox)
        
        if self.data_sources['oceanographic']:
            tasks['noaa_conditions'] = get_marine_conditions(center_lat, center_lon, time_window_hours)
            tasks['copernicus_ocean'] = get_comprehensive_ocean_data(center_lat, center_lon, time_window_hours)
        
        if self.data_sources['coastal_infrastructure']:
            tasks['coastal_infra'] = get_coastal_infrastructure_data(area_bbox)
        
        # Execute all tasks concurrently
        results = {}
        for source, task in tasks.items():
            try:
                results[source] = await task
            except Exception as e:
                logger.error(f"❌ Failed to collect {source} data: {e}")
                results[source] = None
        
        return results
    
    async def _analyze_debris_detections(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze and fuse debris detection data from multiple sources"""
        detections = []
        
        # Process Sentinel Hub detections
        if data.get('sentinel_debris'):
            for detection in data['sentinel_debris']:
                detections.append({
                    'source': 'sentinel_hub',
                    'location': detection.location,
                    'confidence': detection.confidence,
                    'size_estimate': detection.size_estimate,
                    'debris_type': detection.debris_type,
                    'timestamp': detection.timestamp.isoformat()
                })
        
        # Process Planet Labs imagery
        if data.get('planet_imagery'):
            for imagery in data['planet_imagery']:
                # Simulate debris detection from high-res imagery
                if imagery.resolution <= 5.0 and imagery.cloud_coverage <= 0.1:
                    detections.append({
                        'source': 'planet_labs',
                        'location': (imagery.latitude, imagery.longitude),
                        'confidence': 0.8 - imagery.cloud_coverage,
                        'size_estimate': 15.0,  # Estimated from high-res imagery
                        'debris_type': 'unknown',
                        'timestamp': imagery.timestamp.isoformat()
                    })
        
        # Process NASA data for anomalies
        if data.get('nasa_data') and data['nasa_data'].get('modis_data'):
            for modis_point in data['nasa_data']['modis_data']:
                if modis_point.parameter == 'ocean_color_anomaly' and modis_point.value > 0.3:
                    detections.append({
                        'source': 'nasa_modis',
                        'location': (modis_point.latitude, modis_point.longitude),
                        'confidence': modis_point.value,
                        'size_estimate': 50.0,  # Large-scale anomaly
                        'debris_type': 'anomaly',
                        'timestamp': modis_point.timestamp.isoformat()
                    })
        
        return detections
    
    async def _analyze_vessel_correlations(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze correlations between vessel movements and debris"""
        correlations = []
        
        if data.get('vessel_traffic') and data['vessel_traffic'].get('vessels'):
            vessels = data['vessel_traffic']['vessels']
            
            # Analyze vessel types and activities
            for vessel in vessels:
                correlation_score = 0.0
                risk_factors = []
                
                # High-risk vessel types
                if vessel.vessel_type in ['fishing', 'cargo', 'tanker']:
                    correlation_score += 0.3
                    risk_factors.append(f'{vessel.vessel_type}_vessel')
                
                # Slow or stationary vessels (potential dumping)
                if vessel.speed < 2.0:
                    correlation_score += 0.4
                    risk_factors.append('low_speed')
                
                # Large vessels (higher debris potential)
                if vessel.length and vessel.length > 100:
                    correlation_score += 0.2
                    risk_factors.append('large_vessel')
                
                # Near coastal areas (higher debris impact)
                if self._is_near_coast(vessel.latitude, vessel.longitude, data):
                    correlation_score += 0.1
                    risk_factors.append('coastal_proximity')
                
                if correlation_score > 0.3:
                    correlations.append({
                        'vessel_mmsi': vessel.mmsi,
                        'vessel_name': vessel.vessel_name,
                        'vessel_type': vessel.vessel_type,
                        'location': (vessel.latitude, vessel.longitude),
                        'correlation_score': correlation_score,
                        'risk_factors': risk_factors,
                        'timestamp': vessel.timestamp.isoformat()
                    })
        
        return correlations
    
    async def _analyze_environmental_factors(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze environmental factors affecting debris distribution"""
        factors = {
            'ocean_currents': {},
            'weather_conditions': {},
            'tidal_effects': {},
            'seasonal_patterns': {}
        }
        
        # Analyze NOAA conditions
        if data.get('noaa_conditions'):
            noaa_data = data['noaa_conditions']
            
            if noaa_data.get('currents'):
                currents = noaa_data['currents']
                if currents:
                    avg_speed = sum(c.speed for c in currents) / len(currents)
                    avg_direction = sum(c.direction for c in currents) / len(currents)
                    factors['ocean_currents'] = {
                        'average_speed': avg_speed,
                        'average_direction': avg_direction,
                        'debris_transport_potential': 'high' if avg_speed > 0.3 else 'low'
                    }
            
            if noaa_data.get('weather'):
                weather = noaa_data['weather']
                factors['weather_conditions'] = {
                    'wind_speed': weather.wind_speed,
                    'wind_direction': weather.wind_direction,
                    'air_temperature': weather.air_temperature,
                    'debris_dispersion_factor': 'high' if weather.wind_speed > 10 else 'low'
                }
        
        # Analyze Copernicus oceanographic data
        if data.get('copernicus_ocean'):
            copernicus_data = data['copernicus_ocean']
            
            if copernicus_data.get('oceanographic'):
                ocean_data = copernicus_data['oceanographic']
                if ocean_data:
                    latest = ocean_data[0]
                    factors['ocean_currents'].update({
                        'sea_surface_temperature': latest.sea_surface_temperature,
                        'salinity': latest.salinity,
                        'current_velocity_u': latest.current_velocity_u,
                        'current_velocity_v': latest.current_velocity_v
                    })
        
        return factors
    
    async def _assess_debris_risk(
        self,
        debris_detections: List[Dict[str, Any]],
        vessel_correlations: List[Dict[str, Any]],
        environmental_factors: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Assess overall debris risk for the area"""
        risk_score = 0.0
        risk_factors = []
        
        # Detection-based risk
        if len(debris_detections) > 5:
            risk_score += 0.4
            risk_factors.append('high_detection_count')
        elif len(debris_detections) > 2:
            risk_score += 0.2
            risk_factors.append('moderate_detection_count')
        
        # Vessel correlation risk
        high_risk_vessels = [v for v in vessel_correlations if v['correlation_score'] > 0.5]
        if len(high_risk_vessels) > 3:
            risk_score += 0.3
            risk_factors.append('multiple_high_risk_vessels')
        elif len(high_risk_vessels) > 0:
            risk_score += 0.1
            risk_factors.append('some_high_risk_vessels')
        
        # Environmental risk
        currents = environmental_factors.get('ocean_currents', {})
        if currents.get('debris_transport_potential') == 'high':
            risk_score += 0.2
            risk_factors.append('high_current_transport')
        
        weather = environmental_factors.get('weather_conditions', {})
        if weather.get('debris_dispersion_factor') == 'high':
            risk_score += 0.1
            risk_factors.append('high_wind_dispersion')
        
        # Determine risk level
        if risk_score >= 0.7:
            risk_level = 'critical'
        elif risk_score >= 0.5:
            risk_level = 'high'
        elif risk_score >= 0.3:
            risk_level = 'moderate'
        else:
            risk_level = 'low'
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'debris_count': len(debris_detections),
            'vessel_correlations': len(vessel_correlations),
            'environmental_impact': 'significant' if risk_score > 0.5 else 'minimal'
        }
    
    async def _generate_recommendations(
        self,
        debris_detections: List[Dict[str, Any]],
        vessel_correlations: List[Dict[str, Any]],
        environmental_factors: Dict[str, Any],
        risk_assessment: Dict[str, Any]
    ) -> List[str]:
        """Generate actionable recommendations based on analysis"""
        recommendations = []
        
        # Detection-based recommendations
        if len(debris_detections) > 5:
            recommendations.append("Deploy cleanup vessels to high-detection areas immediately")
            recommendations.append("Increase satellite monitoring frequency for this area")
        
        # Vessel-based recommendations
        high_risk_vessels = [v for v in vessel_correlations if v['correlation_score'] > 0.5]
        if high_risk_vessels:
            recommendations.append(f"Monitor {len(high_risk_vessels)} high-risk vessels for compliance")
            recommendations.append("Implement enhanced waste management protocols for fishing vessels")
        
        # Environmental recommendations
        currents = environmental_factors.get('ocean_currents', {})
        if currents.get('debris_transport_potential') == 'high':
            recommendations.append("Deploy drift prediction models to forecast debris movement")
            recommendations.append("Position cleanup resources downstream of current flow")
        
        # Risk-based recommendations
        if risk_assessment['risk_level'] == 'critical':
            recommendations.append("Activate emergency response protocols")
            recommendations.append("Coordinate with Taiwan government marine agencies")
        elif risk_assessment['risk_level'] == 'high':
            recommendations.append("Increase patrol frequency in affected areas")
            recommendations.append("Alert nearby coastal communities")
        
        # General recommendations
        recommendations.append("Continue multi-source monitoring for trend analysis")
        recommendations.append("Share intelligence with marine conservation partners")
        
        return recommendations
    
    def _calculate_confidence_score(
        self,
        debris_detections: List[Dict[str, Any]],
        vessel_correlations: List[Dict[str, Any]],
        environmental_factors: Dict[str, Any]
    ) -> float:
        """Calculate overall confidence score for the intelligence report"""
        confidence = 0.0
        
        # Detection confidence
        if debris_detections:
            avg_detection_confidence = sum(d['confidence'] for d in debris_detections) / len(debris_detections)
            confidence += avg_detection_confidence * self.fusion_weights['satellite_confidence']
        
        # Vessel correlation confidence
        if vessel_correlations:
            avg_vessel_confidence = sum(v['correlation_score'] for v in vessel_correlations) / len(vessel_correlations)
            confidence += avg_vessel_confidence * self.fusion_weights['vessel_correlation']
        
        # Environmental data availability
        env_score = len(environmental_factors) / 4.0  # Normalize by expected number of factors
        confidence += env_score * self.fusion_weights['environmental_factors']
        
        # Infrastructure data (if available)
        confidence += 0.8 * self.fusion_weights['infrastructure_proximity']  # Assume good infrastructure data
        
        return min(1.0, confidence)
    
    def _is_near_coast(self, lat: float, lon: float, data: Dict[str, Any]) -> bool:
        """Check if location is near coastal infrastructure"""
        if data.get('coastal_infra') and data['coastal_infra'].get('infrastructure'):
            infra = data['coastal_infra']['infrastructure']
            for category, items in infra.items():
                for item in items:
                    # Simple distance check (would use proper geospatial calculation in production)
                    if abs(item.latitude - lat) < 0.1 and abs(item.longitude - lon) < 0.1:
                        return True
        return False
    
    async def identify_debris_hotspots(
        self,
        area_bbox: Tuple[float, float, float, float],
        grid_size: float = 0.1
    ) -> List[DebrisHotspot]:
        """Identify marine debris hotspots using grid-based analysis"""
        hotspots = []
        
        # Generate intelligence report for the area
        report = await self.generate_intelligence_report(area_bbox)
        
        if report.confidence_score < 0.5:
            return hotspots
        
        # Create grid for hotspot analysis
        min_lon, min_lat, max_lon, max_lat = area_bbox
        
        for lat in np.arange(min_lat, max_lat, grid_size):
            for lon in np.arange(min_lon, max_lon, grid_size):
                grid_bbox = (lon, lat, lon + grid_size, lat + grid_size)
                
                # Count detections in grid cell
                detections_in_cell = [
                    d for d in report.debris_detections
                    if (grid_bbox[0] <= d['location'][1] <= grid_bbox[2] and
                        grid_bbox[1] <= d['location'][0] <= grid_bbox[3])
                ]
                
                if len(detections_in_cell) >= 3:  # Minimum for hotspot
                    debris_density = len(detections_in_cell) / (grid_size ** 2)
                    avg_confidence = sum(d['confidence'] for d in detections_in_cell) / len(detections_in_cell)
                    
                    if avg_confidence >= self.hotspot_threshold:
                        hotspot = DebrisHotspot(
                            hotspot_id=f"hotspot_{lat:.2f}_{lon:.2f}",
                            center_location=(lat + grid_size/2, lon + grid_size/2),
                            radius_km=grid_size * 111,  # Approximate km conversion
                            debris_density=debris_density,
                            confidence=avg_confidence,
                            contributing_factors=['satellite_detection', 'vessel_activity'],
                            vessel_activity={'nearby_vessels': len(report.vessel_correlations)},
                            environmental_conditions=report.environmental_factors,
                            timestamp=datetime.now()
                        )
                        hotspots.append(hotspot)
        
        logger.info(f"✅ Identified {len(hotspots)} debris hotspots")
        return hotspots


# Convenience functions
async def generate_marine_intelligence(
    area_bbox: Tuple[float, float, float, float],
    time_window_hours: int = 24
) -> IntelligenceReport:
    """Generate comprehensive marine debris intelligence report"""
    system = MultiSourceIntelligenceSystem()
    return await system.generate_intelligence_report(area_bbox, time_window_hours)


if __name__ == "__main__":
    async def test_intelligence_system():
        print("🧠 Testing Multi-Source Intelligence System")
        
        # Test area: Mediterranean Sea
        test_bbox = (2.0, 41.0, 3.0, 42.0)
        
        try:
            report = await generate_marine_intelligence(test_bbox, time_window_hours=12)
            
            print(f"✅ Intelligence report generated")
            print(f"   Report ID: {report.report_id}")
            print(f"   Confidence: {report.confidence_score:.2f}")
            print(f"   Debris detections: {len(report.debris_detections)}")
            print(f"   Vessel correlations: {len(report.vessel_correlations)}")
            print(f"   Risk level: {report.risk_assessment.get('risk_level', 'unknown')}")
            print(f"   Processing time: {report.processing_time_seconds:.1f}s")
            
            print("\n📋 Recommendations:")
            for i, rec in enumerate(report.recommendations[:3], 1):
                print(f"   {i}. {rec}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    asyncio.run(test_intelligence_system())
