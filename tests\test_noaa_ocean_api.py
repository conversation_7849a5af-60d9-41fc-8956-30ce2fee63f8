#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test suite for NOAA Ocean Service API integration
"""

import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.marine_conservation.apis.noaa_ocean_api import (
    NOAAOceanAPI,
    OceanCurrentData,
    WaterTemperatureData,
    TidalData,
    WeatherData,
    get_marine_conditions
)


class TestNOAAOceanAPI:
    """Test cases for NOAA Ocean Service API integration"""
    
    @pytest.fixture
    def api_client(self):
        """Create test API client"""
        return NOAAOceanAPI(user_agent="TestAgent/1.0")
    
    @pytest.fixture
    def test_coordinates(self):
        """Test coordinates (Boston Harbor)"""
        return 42.3581, -71.0636
    
    @pytest.fixture
    def mock_current_response(self):
        """Mock NOAA current data response"""
        return {
            "metadata": {"id": "8447930", "name": "Boston, MA"},
            "data": [
                {"t": "2024-01-15 12:00", "s": "0.5", "d": "45", "bin": "1", "f": "0,0,0,0"},
                {"t": "2024-01-15 13:00", "s": "0.7", "d": "50", "bin": "1", "f": "0,0,0,0"},
                {"t": "2024-01-15 14:00", "s": "0.3", "d": "40", "bin": "1", "f": "0,0,0,0"}
            ]
        }
    
    @pytest.fixture
    def mock_temperature_response(self):
        """Mock NOAA temperature data response"""
        return {
            "metadata": {"id": "8447930", "name": "Boston, MA"},
            "data": [
                {"t": "2024-01-15 12:00", "v": "8.5", "f": "0,0,0,0"},
                {"t": "2024-01-15 13:00", "v": "8.7", "f": "0,0,0,0"},
                {"t": "2024-01-15 14:00", "v": "8.3", "f": "0,0,0,0"}
            ]
        }
    
    @pytest.fixture
    def mock_tidal_response(self):
        """Mock NOAA tidal data response"""
        return {
            "metadata": {"id": "8447930", "name": "Boston, MA"},
            "data": [
                {"t": "2024-01-15 12:00", "v": "2.1", "f": "0,0,0,0"},
                {"t": "2024-01-15 13:00", "v": "2.3", "f": "0,0,0,0"},
                {"t": "2024-01-15 14:00", "v": "2.0", "f": "0,0,0,0"}
            ]
        }
    
    @pytest.fixture
    def mock_weather_response(self):
        """Mock NOAA weather data response"""
        return {
            "properties": {
                "periods": [
                    {
                        "temperature": 45,
                        "windSpeed": "10 mph",
                        "windDirection": "NW",
                        "shortForecast": "Partly Cloudy"
                    }
                ]
            }
        }
    
    def test_ocean_current_data_creation(self):
        """Test ocean current data object creation"""
        current = OceanCurrentData(
            latitude=42.3581,
            longitude=-71.0636,
            u_velocity=0.35,
            v_velocity=0.35,
            speed=0.5,
            direction=45.0,
            timestamp=datetime.now(),
            depth=1.0,
            quality_flag="0,0,0,0"
        )
        
        assert current.latitude == 42.3581
        assert current.longitude == -71.0636
        assert current.speed == 0.5
        assert current.direction == 45.0
        assert current.depth == 1.0
    
    def test_water_temperature_data_creation(self):
        """Test water temperature data object creation"""
        temp = WaterTemperatureData(
            latitude=42.3581,
            longitude=-71.0636,
            temperature=8.5,
            depth=0.0,
            timestamp=datetime.now(),
            station_id="8447930"
        )
        
        assert temp.latitude == 42.3581
        assert temp.longitude == -71.0636
        assert temp.temperature == 8.5
        assert temp.station_id == "8447930"
    
    def test_tidal_data_creation(self):
        """Test tidal data object creation"""
        tidal = TidalData(
            station_id="8447930",
            station_name="Boston, MA",
            latitude=42.3581,
            longitude=-71.0636,
            water_level=2.1,
            prediction=2.0,
            timestamp=datetime.now()
        )
        
        assert tidal.station_id == "8447930"
        assert tidal.station_name == "Boston, MA"
        assert tidal.water_level == 2.1
        assert tidal.prediction == 2.0
    
    def test_weather_data_creation(self):
        """Test weather data object creation"""
        weather = WeatherData(
            latitude=42.3581,
            longitude=-71.0636,
            air_temperature=7.2,
            wind_speed=4.5,
            wind_direction=315.0,
            wave_height=1.2,
            atmospheric_pressure=1013.2
        )
        
        assert weather.latitude == 42.3581
        assert weather.longitude == -71.0636
        assert weather.air_temperature == 7.2
        assert weather.wind_speed == 4.5
        assert weather.wind_direction == 315.0
    
    @pytest.mark.asyncio
    async def test_find_nearest_current_station(self, api_client, test_coordinates):
        """Test finding nearest current monitoring station"""
        lat, lon = test_coordinates
        station_id = await api_client._find_nearest_current_station(lat, lon)
        
        # Should find Boston station for Boston coordinates
        assert station_id == "8447930"
    
    @pytest.mark.asyncio
    async def test_find_nearest_station_too_far(self, api_client):
        """Test finding station when coordinates are too far from any station"""
        # Use coordinates in the middle of the Pacific Ocean
        station_id = await api_client._find_nearest_current_station(30.0, -150.0)
        
        # Should return None for locations too far from stations
        assert station_id is None
    
    @pytest.mark.asyncio
    async def test_get_ocean_currents_success(self, api_client, test_coordinates, mock_current_response):
        """Test successful ocean current data retrieval"""
        lat, lon = test_coordinates
        
        # Mock the API response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_current_response
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            currents = await api_client.get_ocean_currents(lat, lon, hours_back=6)
            
            assert len(currents) == 3
            assert currents[0].speed == 0.5
            assert currents[0].direction == 45.0
            assert currents[1].speed == 0.7
            assert currents[1].direction == 50.0
    
    @pytest.mark.asyncio
    async def test_get_ocean_currents_no_station(self, api_client):
        """Test ocean current retrieval when no station is found"""
        # Use coordinates far from any station
        currents = await api_client.get_ocean_currents(30.0, -150.0, hours_back=6)
        
        assert currents == []
    
    @pytest.mark.asyncio
    async def test_get_water_temperature_success(self, api_client, test_coordinates, mock_temperature_response):
        """Test successful water temperature data retrieval"""
        lat, lon = test_coordinates
        
        # Mock the API response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_temperature_response
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            temperatures = await api_client.get_water_temperature(lat, lon, hours_back=6)
            
            assert len(temperatures) == 3
            assert temperatures[0].temperature == 8.5
            assert temperatures[1].temperature == 8.7
            assert temperatures[2].temperature == 8.3
    
    @pytest.mark.asyncio
    async def test_get_tidal_data_success(self, api_client, test_coordinates, mock_tidal_response):
        """Test successful tidal data retrieval"""
        lat, lon = test_coordinates
        
        # Mock the API response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_tidal_response
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            tides = await api_client.get_tidal_data(lat, lon, hours_back=6)
            
            assert len(tides) == 3
            assert tides[0].water_level == 2.1
            assert tides[1].water_level == 2.3
            assert tides[2].water_level == 2.0
            assert tides[0].station_id == "8447930"
    
    @pytest.mark.asyncio
    async def test_get_marine_weather_success(self, api_client, test_coordinates, mock_weather_response):
        """Test successful marine weather data retrieval"""
        lat, lon = test_coordinates
        
        # Mock the point data response
        mock_point_response = AsyncMock()
        mock_point_response.status = 200
        mock_point_response.json.return_value = {
            "properties": {"forecast": "https://api.weather.gov/gridpoints/BOX/71,90/forecast"}
        }
        
        # Mock the forecast response
        mock_forecast_response = AsyncMock()
        mock_forecast_response.status = 200
        mock_forecast_response.json.return_value = mock_weather_response
        
        with patch.object(api_client, 'session') as mock_session:
            # Set up the mock to return different responses for different calls
            mock_session.get.return_value.__aenter__.side_effect = [
                mock_point_response,
                mock_forecast_response
            ]
            
            weather = await api_client.get_marine_weather(lat, lon)
            
            assert weather is not None
            assert weather.latitude == lat
            assert weather.longitude == lon
            assert weather.air_temperature == 7.222222222222222  # 45°F to Celsius
            assert weather.wind_speed == pytest.approx(4.47, rel=1e-2)  # 10 mph to m/s
            assert weather.wind_direction == 315.0  # NW
    
    def test_parse_wind_speed(self, api_client):
        """Test wind speed parsing from string format"""
        assert api_client._parse_wind_speed("10 mph") == pytest.approx(4.47, rel=1e-2)
        assert api_client._parse_wind_speed("5 to 10 mph") == pytest.approx(2.24, rel=1e-2)
        assert api_client._parse_wind_speed("15 mph") == pytest.approx(6.71, rel=1e-2)
        assert api_client._parse_wind_speed("invalid") == 0.0
    
    def test_parse_wind_direction(self, api_client):
        """Test wind direction parsing from string format"""
        assert api_client._parse_wind_direction("N") == 0.0
        assert api_client._parse_wind_direction("NE") == 45.0
        assert api_client._parse_wind_direction("E") == 90.0
        assert api_client._parse_wind_direction("SE") == 135.0
        assert api_client._parse_wind_direction("S") == 180.0
        assert api_client._parse_wind_direction("SW") == 225.0
        assert api_client._parse_wind_direction("W") == 270.0
        assert api_client._parse_wind_direction("NW") == 315.0
        assert api_client._parse_wind_direction("invalid") == 0.0
    
    def test_fahrenheit_to_celsius(self, api_client):
        """Test temperature conversion from Fahrenheit to Celsius"""
        assert api_client._fahrenheit_to_celsius(32) == 0.0
        assert api_client._fahrenheit_to_celsius(212) == 100.0
        assert api_client._fahrenheit_to_celsius(68) == 20.0
        assert api_client._fahrenheit_to_celsius(45) == pytest.approx(7.22, rel=1e-2)
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, api_client, test_coordinates):
        """Test API error handling"""
        lat, lon = test_coordinates
        
        # Mock failed API response
        mock_response = AsyncMock()
        mock_response.status = 404
        
        with patch.object(api_client, 'session') as mock_session:
            mock_session.get.return_value.__aenter__.return_value = mock_response
            
            currents = await api_client.get_ocean_currents(lat, lon, hours_back=6)
            temperatures = await api_client.get_water_temperature(lat, lon, hours_back=6)
            tides = await api_client.get_tidal_data(lat, lon, hours_back=6)
            
            assert currents == []
            assert temperatures == []
            assert tides == []
    
    @pytest.mark.asyncio
    async def test_convenience_function(self, test_coordinates):
        """Test convenience function for getting marine conditions"""
        lat, lon = test_coordinates
        
        # Mock all the API calls
        with patch('src.marine_conservation.apis.noaa_ocean_api.NOAAOceanAPI') as mock_api_class:
            mock_api_instance = AsyncMock()
            
            # Mock return values
            mock_api_instance.get_ocean_currents.return_value = [
                OceanCurrentData(lat, lon, 0.35, 0.35, 0.5, 45.0, datetime.now())
            ]
            mock_api_instance.get_water_temperature.return_value = [
                WaterTemperatureData(lat, lon, 8.5, 0.0, datetime.now())
            ]
            mock_api_instance.get_tidal_data.return_value = [
                TidalData("8447930", "Boston, MA", lat, lon, 2.1, 2.0, datetime.now())
            ]
            mock_api_instance.get_marine_weather.return_value = WeatherData(
                lat, lon, 7.2, 4.5, 315.0
            )
            
            mock_api_class.return_value.__aenter__.return_value = mock_api_instance
            
            conditions = await get_marine_conditions(lat, lon, hours_back=6)
            
            assert 'currents' in conditions
            assert 'temperature' in conditions
            assert 'tides' in conditions
            assert 'weather' in conditions
            assert 'timestamp' in conditions
            
            assert len(conditions['currents']) == 1
            assert len(conditions['temperature']) == 1
            assert len(conditions['tides']) == 1
            assert conditions['weather'] is not None


class TestDataParsing:
    """Test cases for data parsing functionality"""
    
    def test_parse_current_data(self):
        """Test parsing of ocean current data"""
        api_client = NOAAOceanAPI()
        
        mock_data = {
            "data": [
                {"t": "2024-01-15 12:00", "s": "0.5", "d": "45", "bin": "1", "f": "0,0,0,0"},
                {"t": "2024-01-15 13:00", "s": "0.7", "d": "50", "bin": "1", "f": "0,0,0,0"}
            ]
        }
        
        currents = api_client._parse_current_data(mock_data, 42.0, -71.0)
        
        assert len(currents) == 2
        assert currents[0].speed == 0.5
        assert currents[0].direction == 45.0
        assert currents[1].speed == 0.7
        assert currents[1].direction == 50.0
        
        # Check velocity components calculation
        import math
        expected_u = 0.5 * math.sin(math.radians(45))
        expected_v = 0.5 * math.cos(math.radians(45))
        assert currents[0].u_velocity == pytest.approx(expected_u, rel=1e-3)
        assert currents[0].v_velocity == pytest.approx(expected_v, rel=1e-3)
    
    def test_parse_temperature_data(self):
        """Test parsing of water temperature data"""
        api_client = NOAAOceanAPI()
        
        mock_data = {
            "metadata": {"id": "8447930"},
            "data": [
                {"t": "2024-01-15 12:00", "v": "8.5", "f": "0,0,0,0"},
                {"t": "2024-01-15 13:00", "v": "8.7", "f": "0,0,0,0"}
            ]
        }
        
        temperatures = api_client._parse_temperature_data(mock_data, 42.0, -71.0)
        
        assert len(temperatures) == 2
        assert temperatures[0].temperature == 8.5
        assert temperatures[1].temperature == 8.7
        assert temperatures[0].station_id == "8447930"
    
    def test_parse_tidal_data(self):
        """Test parsing of tidal data"""
        api_client = NOAAOceanAPI()
        
        mock_data = {
            "metadata": {"id": "8447930", "name": "Boston, MA"},
            "data": [
                {"t": "2024-01-15 12:00", "v": "2.1", "f": "0,0,0,0"},
                {"t": "2024-01-15 13:00", "v": "2.3", "f": "0,0,0,0"}
            ]
        }
        
        tides = api_client._parse_tidal_data(mock_data, 42.0, -71.0)
        
        assert len(tides) == 2
        assert tides[0].water_level == 2.1
        assert tides[1].water_level == 2.3
        assert tides[0].station_id == "8447930"
        assert tides[0].station_name == "Boston, MA"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
