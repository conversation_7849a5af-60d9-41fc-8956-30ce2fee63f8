"""
Simple test for temperature trend analysis.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta
import numpy as np

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.analysis.temperature_trends import TemperatureTrendAnalyzer
from src.data.preprocessing.climate_preprocessor import ProcessedClimateData


async def simple_test():
    """Simple test of temperature trend analysis."""
    print("🧪 Simple Temperature Trend Analysis Test")
    
    try:
        # Initialize analyzer
        analyzer = TemperatureTrendAnalyzer()
        init_result = await analyzer.initialize()
        print(f"✅ Analyzer initialized: {init_result}")
        
        # Create simple test data
        data = []
        for i in range(30):  # 30 days of data
            date = datetime.now() - timedelta(days=30-i)
            temp = 20.0 + i * 0.1  # Simple increasing trend
            
            data_point = ProcessedClimateData(
                timestamp=date,
                location="Test Location",
                latitude=40.0,
                longitude=-74.0,
                source="test",
                temperature=temp
            )
            data.append(data_point)
        
        print(f"📊 Created {len(data)} test data points")
        
        # Analyze trends
        result = await analyzer.analyze_temperature_trends(data, "Test Location")
        
        if result:
            print("✅ Analysis completed successfully!")
            print(f"📈 Trend direction: {result.trend_direction}")
            print(f"📊 Trend magnitude: {result.trend_magnitude:.3f}°C/year")
            print(f"🎯 Confidence: {result.trend_confidence:.3f}")
            print(f"📅 Analysis period: {result.analysis_period}")
            return True
        else:
            print("❌ Analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    asyncio.run(simple_test())
