# =============================================================================
# WATER MANAGEMENT DECARBONISATION PROJECT - MAKEFILE
# =============================================================================

.PHONY: help install dev test clean build deploy docs lint format

# Default target
help: ## Show this help message
	@echo "Water Management Decarbonisation Project"
	@echo "========================================"
	@echo ""
	@echo "Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# =============================================================================
# DEVELOPMENT SETUP
# =============================================================================

install: ## Install all dependencies
	@echo "Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "Installing pre-commit hooks..."
	pre-commit install
	@echo "Setting up environment..."
	cp .env.example .env
	@echo "Installation complete!"

install-dev: ## Install development dependencies
	pip install -r requirements-dev.txt
	npm install -g @commitlint/cli @commitlint/config-conventional

setup-db: ## Initialize database
	@echo "Setting up database..."
	docker-compose up -d postgres redis
	sleep 10
	python src/utils/init_db.py
	@echo "Database setup complete!"

# =============================================================================
# DEVELOPMENT COMMANDS
# =============================================================================

dev: ## Start development environment
	@echo "Starting development environment..."
	docker-compose up -d postgres redis rabbitmq
	sleep 10
	@echo "Starting backend and frontend..."
	make dev-backend &
	make dev-frontend &
	make dev-streamlit &
	wait

dev-backend: ## Start backend development server
	@echo "Starting backend server..."
	cd src && uvicorn main:app --reload --host 0.0.0.0 --port 8000

dev-frontend: ## Start frontend development server
	@echo "Starting frontend server..."
	cd frontend && npm run dev

dev-streamlit: ## Start Streamlit dashboard
	@echo "Starting Streamlit dashboard..."
	streamlit run src/app.py --server.port 8501

dev-jupyter: ## Start Jupyter lab
	@echo "Starting Jupyter lab..."
	jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root

# =============================================================================
# TESTING
# =============================================================================

test: ## Run all tests
	@echo "Running tests..."
	pytest tests/ -v --cov=src --cov-report=html --cov-report=term

test-unit: ## Run unit tests only
	pytest tests/unit/ -v

test-integration: ## Run integration tests only
	pytest tests/integration/ -v

test-e2e: ## Run end-to-end tests
	pytest tests/e2e/ -v

test-performance: ## Run performance tests
	pytest tests/performance/ -v

test-coverage: ## Generate test coverage report
	pytest --cov=src --cov-report=html --cov-report=term-missing

# =============================================================================
# CODE QUALITY
# =============================================================================

lint: ## Run linting checks
	@echo "Running linting checks..."
	flake8 src/ tests/
	mypy src/
	bandit -r src/

format: ## Format code
	@echo "Formatting code..."
	black src/ tests/
	isort src/ tests/

format-check: ## Check code formatting
	black --check src/ tests/
	isort --check-only src/ tests/

pre-commit: ## Run pre-commit hooks
	pre-commit run --all-files

# =============================================================================
# DOCKER OPERATIONS
# =============================================================================

build: ## Build Docker images
	@echo "Building Docker images..."
	docker-compose build

up: ## Start all services
	@echo "Starting all services..."
	docker-compose up -d

down: ## Stop all services
	@echo "Stopping all services..."
	docker-compose down

restart: ## Restart all services
	@echo "Restarting all services..."
	docker-compose restart

logs: ## View logs from all services
	docker-compose logs -f

logs-backend: ## View backend logs
	docker-compose logs -f backend

logs-frontend: ## View frontend logs
	docker-compose logs -f frontend

logs-db: ## View database logs
	docker-compose logs -f postgres

# =============================================================================
# DATABASE OPERATIONS
# =============================================================================

db-migrate: ## Run database migrations
	@echo "Running database migrations..."
	alembic upgrade head

db-rollback: ## Rollback database migration
	@echo "Rolling back database migration..."
	alembic downgrade -1

db-reset: ## Reset database
	@echo "Resetting database..."
	docker-compose down postgres
	docker volume rm watermanagement_postgres_data
	docker-compose up -d postgres
	sleep 10
	python src/utils/init_db.py

db-backup: ## Backup database
	@echo "Backing up database..."
	docker-compose exec postgres pg_dump -U postgres watermanagement > backup_$(shell date +%Y%m%d_%H%M%S).sql

db-restore: ## Restore database from backup
	@echo "Restoring database..."
	@read -p "Enter backup file path: " backup_file; \
	docker-compose exec -T postgres psql -U postgres watermanagement < $$backup_file

# =============================================================================
# DATA OPERATIONS
# =============================================================================

data-collect: ## Collect climate and environmental data
	@echo "Collecting data..."
	python src/data/collectors/climate_collector.py
	python src/data/collectors/water_quality_collector.py
	python src/data/collectors/energy_collector.py

data-process: ## Process collected data
	@echo "Processing data..."
	python src/data/processors/climate_processor.py
	python src/data/processors/water_processor.py

data-validate: ## Validate data quality
	@echo "Validating data..."
	python src/data/validators/data_validator.py

# =============================================================================
# MODEL OPERATIONS
# =============================================================================

train: ## Train machine learning models
	@echo "Training models..."
	python src/models/train.py

train-climate: ## Train climate prediction models
	python src/models/climate/train_climate_model.py

train-optimization: ## Train optimization models
	python src/models/optimization/train_optimization_model.py

evaluate: ## Evaluate model performance
	@echo "Evaluating models..."
	python src/models/evaluate.py

predict: ## Run predictions
	@echo "Running predictions..."
	python src/models/predict.py

# =============================================================================
# DEPLOYMENT
# =============================================================================

deploy-dev: ## Deploy to development environment
	@echo "Deploying to development..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

deploy-staging: ## Deploy to staging environment
	@echo "Deploying to staging..."
	docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d

deploy-prod: ## Deploy to production environment
	@echo "Deploying to production..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# =============================================================================
# MONITORING
# =============================================================================

monitor: ## Start monitoring services
	@echo "Starting monitoring services..."
	docker-compose up -d prometheus grafana

health-check: ## Check service health
	@echo "Checking service health..."
	curl -f http://localhost:8000/health || echo "Backend unhealthy"
	curl -f http://localhost:3000 || echo "Frontend unhealthy"
	curl -f http://localhost:8501 || echo "Streamlit unhealthy"

# =============================================================================
# DOCUMENTATION
# =============================================================================

docs: ## Generate documentation
	@echo "Generating documentation..."
	sphinx-build -b html docs/ docs/_build/html

docs-serve: ## Serve documentation locally
	@echo "Serving documentation..."
	cd docs/_build/html && python -m http.server 8080

docs-api: ## Generate API documentation
	@echo "Generating API documentation..."
	python src/utils/generate_api_docs.py

# =============================================================================
# UTILITIES
# =============================================================================

clean: ## Clean up temporary files and caches
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +

clean-docker: ## Clean up Docker resources
	@echo "Cleaning up Docker resources..."
	docker-compose down --volumes --remove-orphans
	docker system prune -f
	docker volume prune -f

reset: ## Reset entire development environment
	@echo "Resetting development environment..."
	make clean
	make clean-docker
	make install
	make setup-db

# =============================================================================
# SECURITY
# =============================================================================

security-scan: ## Run security scans
	@echo "Running security scans..."
	bandit -r src/
	safety check
	docker run --rm -v $(PWD):/app clair-scanner:latest

audit: ## Audit dependencies for vulnerabilities
	@echo "Auditing dependencies..."
	pip-audit
	npm audit

# =============================================================================
# PERFORMANCE
# =============================================================================

benchmark: ## Run performance benchmarks
	@echo "Running benchmarks..."
	python tests/performance/benchmark.py

profile: ## Profile application performance
	@echo "Profiling application..."
	python -m cProfile -o profile.stats src/main.py

load-test: ## Run load tests
	@echo "Running load tests..."
	locust -f tests/performance/locustfile.py --host=http://localhost:8000

# =============================================================================
# RESEARCH AND DEVELOPMENT
# =============================================================================

research: ## Run research experiments
	@echo "Running research experiments..."
	python src/research/experiments/run_experiments.py

analyze: ## Analyze experimental results
	@echo "Analyzing results..."
	python src/research/analysis/analyze_results.py

visualize: ## Create visualizations
	@echo "Creating visualizations..."
	python src/visualization/create_plots.py

# =============================================================================
# BACKUP AND RESTORE
# =============================================================================

backup: ## Create full system backup
	@echo "Creating system backup..."
	make db-backup
	tar -czf backup_$(shell date +%Y%m%d_%H%M%S).tar.gz data/ logs/ config/

restore: ## Restore from backup
	@echo "Restoring from backup..."
	@read -p "Enter backup file path: " backup_file; \
	tar -xzf $$backup_file

# =============================================================================
# ENVIRONMENT MANAGEMENT
# =============================================================================

env-check: ## Check environment configuration
	@echo "Checking environment..."
	python src/utils/env_check.py

env-update: ## Update environment variables
	@echo "Updating environment..."
	cp .env.example .env.new
	@echo "Please update .env.new with your values, then rename to .env"

# =============================================================================
# QUICK COMMANDS
# =============================================================================

quick-start: install setup-db dev ## Quick start for new developers

quick-test: format lint test ## Quick quality check

quick-deploy: build up ## Quick deployment
