"""
Test script for World Bank Climate Data API integration.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.data.collectors.worldbank_collector import (
    WorldBankClimateCollector, 
    get_country_climate_data, 
    get_global_climate_indicators,
    get_climate_comparison
)


async def test_worldbank_api_connection():
    """Test World Bank API connection and basic functionality."""
    print("🧪 Testing World Bank Climate Data API Integration...")
    
    try:
        # Initialize collector
        collector = WorldBankClimateCollector()
        init_success = await collector.initialize()
        
        if not init_success:
            print("❌ Failed to initialize World Bank collector")
            return False
        
        print("✅ World Bank Climate Data collector initialized successfully")
        
        # Test countries endpoint
        print("\n🔄 Testing countries data retrieval...")
        countries = await collector.get_countries()
        
        if countries:
            print(f"✅ Retrieved {len(countries)} countries from World Bank API")
            
            # Show sample countries
            sample_countries = [c for c in countries if c.get('name') and c.get('iso2Code')][:5]
            print("🌍 Sample countries:")
            for country in sample_countries:
                print(f"  • {country.get('name')} ({country.get('iso2Code')})")
        else:
            print("⚠️ No countries data retrieved")
        
        await collector.shutdown()
        return len(countries) > 0
        
    except Exception as e:
        print(f"❌ World Bank API connection test failed: {e}")
        return False


async def test_climate_indicators():
    """Test climate indicators data collection."""
    print("\n🧪 Testing Climate Indicators Data...")
    
    try:
        collector = WorldBankClimateCollector()
        await collector.initialize()
        
        # Test CO2 emissions indicator
        print("🔄 Testing CO2 emissions per capita data...")
        
        co2_data = await collector.get_indicator_data(
            "EN.ATM.CO2E.PC",  # CO2 emissions per capita
            ["USA", "CHN", "DEU", "JPN"],  # Sample countries
            start_year=2018,
            end_year=2022
        )
        
        if co2_data:
            print(f"✅ Retrieved {len(co2_data)} CO2 emissions data points")
            
            # Show sample data
            print("📊 Sample CO2 emissions data:")
            for data_point in co2_data[:5]:
                print(f"  • {data_point.country_name} ({data_point.year}): {data_point.value:.2f} metric tons per capita")
        else:
            print("⚠️ No CO2 emissions data retrieved")
        
        await collector.shutdown()
        return len(co2_data) > 0
        
    except Exception as e:
        print(f"❌ Climate indicators test failed: {e}")
        return False


async def test_climate_indicators_summary():
    """Test comprehensive climate indicators summary."""
    print("\n🧪 Testing Climate Indicators Summary...")
    
    try:
        collector = WorldBankClimateCollector()
        await collector.initialize()
        
        print("🔄 Generating climate indicators summary...")
        
        summary = await collector.get_climate_indicators_summary(["USA", "CHN", "DEU"])
        
        if summary:
            print("✅ Climate indicators summary generated successfully")
            print(f"📊 Countries analyzed: {summary.get('countries_analyzed', [])}")
            print(f"📈 Overall data availability: {summary.get('overall_availability', 0):.1%}")
            
            indicators = summary.get('indicators', {})
            print(f"🌍 Indicators available: {len(indicators)}")
            
            for indicator_code, indicator_data in indicators.items():
                name = indicator_data.get('name', 'Unknown')
                data_points = indicator_data.get('data_points', 0)
                latest_year = indicator_data.get('latest_year', 'Unknown')
                print(f"  • {name}: {data_points} data points (latest: {latest_year})")
        else:
            print("⚠️ Climate indicators summary could not be generated")
        
        await collector.shutdown()
        return bool(summary)
        
    except Exception as e:
        print(f"❌ Climate indicators summary test failed: {e}")
        return False


async def test_country_climate_profile():
    """Test country-specific climate profile."""
    print("\n🧪 Testing Country Climate Profile...")
    
    try:
        collector = WorldBankClimateCollector()
        await collector.initialize()
        
        # Test with Germany
        country_code = "DE"
        print(f"🔄 Generating climate profile for {country_code}...")
        
        profile = await collector.get_country_climate_profile(country_code)
        
        if profile:
            print("✅ Country climate profile generated successfully")
            print(f"🇩🇪 Country: {profile.get('country_name', 'Unknown')}")
            
            indicators = profile.get('indicators', {})
            print(f"📊 Climate indicators: {len(indicators)}")
            
            summary_stats = profile.get('summary_stats', {})
            if summary_stats:
                print(f"📈 Data completeness: {summary_stats.get('data_completeness', 0):.1%}")
                
                trends = summary_stats.get('trends', {})
                print("📈 Indicator trends:")
                for trend_type, count in trends.items():
                    if count > 0:
                        print(f"  • {trend_type.replace('_', ' ').title()}: {count}")
            
            # Show sample indicators
            sample_indicators = list(indicators.items())[:3]
            print("🌡️ Sample indicators:")
            for code, data in sample_indicators:
                name = data.get('name', 'Unknown')
                latest_value = data.get('latest_value')
                latest_year = data.get('latest_year')
                trend = data.get('trend', 'unknown')
                
                if latest_value is not None:
                    print(f"  • {name}: {latest_value} ({latest_year}) - {trend}")
        else:
            print("⚠️ Country climate profile could not be generated")
        
        await collector.shutdown()
        return bool(profile)
        
    except Exception as e:
        print(f"❌ Country climate profile test failed: {e}")
        return False


async def test_global_climate_comparison():
    """Test global climate comparison."""
    print("\n🧪 Testing Global Climate Comparison...")
    
    try:
        collector = WorldBankClimateCollector()
        await collector.initialize()
        
        print("🔄 Generating global climate comparison...")
        
        comparison = await collector.get_global_climate_comparison()
        
        if comparison:
            print("✅ Global climate comparison generated successfully")
            print(f"📊 Indicator: {comparison.get('indicator', 'Unknown')}")
            
            countries = comparison.get('countries', [])
            print(f"🌍 Countries compared: {len(countries)}")
            
            global_stats = comparison.get('global_stats', {})
            if global_stats:
                highest = global_stats.get('highest', {})
                lowest = global_stats.get('lowest', {})
                average = global_stats.get('average', 0)
                
                print("📈 Global statistics:")
                if highest:
                    print(f"  • Highest: {highest.get('country_name')} ({highest.get('co2_per_capita', 0):.2f})")
                if lowest:
                    print(f"  • Lowest: {lowest.get('country_name')} ({lowest.get('co2_per_capita', 0):.2f})")
                print(f"  • Average: {average:.2f}")
            
            # Show top 5 countries
            print("🏆 Top 5 CO2 emitters per capita:")
            for i, country in enumerate(countries[:5], 1):
                name = country.get('country_name', 'Unknown')
                value = country.get('co2_per_capita', 0)
                year = country.get('year', 'Unknown')
                print(f"  {i}. {name}: {value:.2f} metric tons ({year})")
        else:
            print("⚠️ Global climate comparison could not be generated")
        
        await collector.shutdown()
        return bool(comparison)
        
    except Exception as e:
        print(f"❌ Global climate comparison test failed: {e}")
        return False


async def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing Convenience Functions...")
    
    try:
        print("🔄 Testing get_country_climate_data function...")
        
        climate_data = await get_country_climate_data("US")
        
        if climate_data:
            print("✅ get_country_climate_data working correctly")
            print(f"🇺🇸 Country: {climate_data.get('country_name', 'Unknown')}")
            indicators_count = len(climate_data.get('indicators', {}))
            print(f"📊 Indicators: {indicators_count}")
        else:
            print("⚠️ get_country_climate_data returned no data")
        
        print("\n🔄 Testing get_global_climate_indicators function...")
        
        global_indicators = await get_global_climate_indicators()
        
        if global_indicators:
            print("✅ get_global_climate_indicators working correctly")
            availability = global_indicators.get('overall_availability', 0)
            print(f"📈 Data availability: {availability:.1%}")
        else:
            print("⚠️ get_global_climate_indicators returned no data")
        
        return bool(climate_data and global_indicators)
        
    except Exception as e:
        print(f"❌ Convenience functions test failed: {e}")
        return False


async def main():
    """Run all World Bank Climate Data tests."""
    print("🚀 World Bank Climate Data API Integration Test Suite")
    print("=" * 65)
    
    test_results = []
    
    # Test 1: API connection
    connection_result = await test_worldbank_api_connection()
    test_results.append(("World Bank API Connection", connection_result))
    
    # Only run other tests if API is working
    if connection_result:
        # Test 2: Climate indicators
        indicators_result = await test_climate_indicators()
        test_results.append(("Climate Indicators Data", indicators_result))
        
        # Test 3: Climate indicators summary
        summary_result = await test_climate_indicators_summary()
        test_results.append(("Climate Indicators Summary", summary_result))
        
        # Test 4: Country climate profile
        profile_result = await test_country_climate_profile()
        test_results.append(("Country Climate Profile", profile_result))
        
        # Test 5: Global climate comparison
        comparison_result = await test_global_climate_comparison()
        test_results.append(("Global Climate Comparison", comparison_result))
        
        # Test 6: Convenience functions
        convenience_result = await test_convenience_functions()
        test_results.append(("Convenience Functions", convenience_result))
    else:
        test_results.extend([
            ("Climate Indicators Data", False),
            ("Climate Indicators Summary", False),
            ("Country Climate Profile", False),
            ("Global Climate Comparison", False),
            ("Convenience Functions", False)
        ])
        print("⏭️ Skipping additional tests due to API connection failure")
    
    # Summary
    print("\n" + "=" * 65)
    print("📋 TEST SUMMARY")
    print("=" * 65)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("-" * 65)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All World Bank Climate Data tests passed!")
        print("Global climate indicators collection is ready for use.")
    elif passed >= 3:
        print(f"\n🎉 World Bank integration is functional! ({passed}/{total} tests passed)")
        print("Core climate data collection capabilities are available.")
    elif passed > 0:
        print(f"\n⚠️ Partial functionality ({passed}/{total} tests passed)")
        print("Some World Bank data sources are working.")
    else:
        print("\n❌ World Bank integration needs attention.")
        print("Please check internet connection and API availability.")
    
    print("\n📋 Next Steps:")
    if passed >= 3:
        print("  1. World Bank climate data integration is ready!")
        print("  2. Global climate indicators enhance system capabilities")
        print("  3. Country-specific climate profiles available")
        print("  4. Climate data will support water treatment optimization")
    else:
        print("  1. Check internet connection")
        print("  2. World Bank APIs are free and don't require API keys")
        print("  3. Re-run this test when connection is stable")


if __name__ == "__main__":
    asyncio.run(main())
