"""
Test script for OpenWeatherMap API integration.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.data.collectors.openweather_collector import OpenWeatherMapCollector, get_weather_for_location
from src.utils.config import get_settings


async def test_openweather_api():
    """Test OpenWeatherMap API integration."""
    print("🧪 Testing OpenWeatherMap API Integration...")
    
    try:
        settings = get_settings()
        
        if not settings.OPENWEATHER_API_KEY:
            print("⚠️ OpenWeatherMap API key not configured")
            print("To test this feature, add OPENWEATHER_API_KEY to your .env file")
            print("You can get a free API key from: https://openweathermap.org/api")
            return False
        
        print(f"✅ OpenWeatherMap API key found: {settings.OPENWEATHER_API_KEY[:10]}...")
        
        # Initialize collector
        collector = OpenWeatherMapCollector()
        init_success = await collector.initialize()
        
        if not init_success:
            print("❌ Failed to initialize OpenWeatherMap collector")
            return False
        
        print("✅ OpenWeatherMap collector initialized successfully")
        
        # Test current weather for a single location
        print("\n🔄 Testing current weather data...")
        test_location = {"name": "London", "lat": 51.5074, "lon": -0.1278}
        
        weather_data = await collector.get_current_weather(test_location)
        
        if weather_data:
            print("✅ Current weather data retrieved successfully")
            print(f"📍 Location: {weather_data.location}")
            print(f"🌡️ Temperature: {weather_data.temperature}°C (feels like {weather_data.feels_like}°C)")
            print(f"💧 Humidity: {weather_data.humidity}%")
            print(f"🌧️ Precipitation: {weather_data.precipitation}mm")
            print(f"💨 Wind: {weather_data.wind_speed} m/s")
            print(f"☁️ Weather: {weather_data.weather_description}")
        else:
            print("❌ Failed to retrieve current weather data")
            await collector.shutdown()
            return False
        
        # Test weather forecast
        print("\n🔄 Testing weather forecast...")
        forecast_data = await collector.get_weather_forecast(test_location, days=3)
        
        if forecast_data:
            print(f"✅ Weather forecast retrieved: {len(forecast_data)} data points")
            print("📅 Next 24 hours forecast:")
            
            for i, forecast in enumerate(forecast_data[:8]):  # Show first 8 (24 hours)
                print(f"  {forecast.timestamp.strftime('%H:%M')}: {forecast.temperature}°C, {forecast.weather_description}")
                if i >= 3:  # Limit output
                    print(f"  ... and {len(forecast_data) - 4} more forecasts")
                    break
        else:
            print("❌ Failed to retrieve weather forecast")
        
        # Test air pollution data
        print("\n🔄 Testing air pollution data...")
        pollution_data = await collector.get_air_pollution(test_location)
        
        if pollution_data:
            print("✅ Air pollution data retrieved successfully")
            print(f"🏭 Air Quality Index: {pollution_data.get('aqi', 'N/A')}")
            print(f"🌫️ PM2.5: {pollution_data.get('pm2_5', 'N/A')} μg/m³")
            print(f"🌫️ PM10: {pollution_data.get('pm10', 'N/A')} μg/m³")
        else:
            print("⚠️ Air pollution data not available")
        
        # Test weather summary
        print("\n🔄 Testing comprehensive weather summary...")
        summary = await collector.get_weather_summary(test_location)
        
        if summary:
            print("✅ Weather summary generated successfully")
            print(f"📊 Summary for {summary.get('location', 'Unknown')}")
            
            if summary.get('forecast_stats'):
                stats = summary['forecast_stats']
                print(f"🌡️ Temperature range: {stats['min_temp']:.1f}°C to {stats['max_temp']:.1f}°C")
                print(f"🌧️ Total precipitation: {stats['total_precipitation']:.1f}mm")
        
        await collector.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ OpenWeatherMap API test failed: {e}")
        return False


async def test_multiple_locations():
    """Test weather collection for multiple locations."""
    print("\n🧪 Testing Multiple Locations Collection...")
    
    try:
        collector = OpenWeatherMapCollector()
        init_success = await collector.initialize()
        
        if not init_success:
            print("❌ Failed to initialize collector")
            return False
        
        print("🔄 Collecting weather data for all default locations...")
        weather_data_list = await collector.collect_all_locations()
        
        if weather_data_list:
            print(f"✅ Collected weather data for {len(weather_data_list)} locations")
            
            print("\n🌍 Global Weather Summary:")
            for weather in weather_data_list:
                print(f"  📍 {weather.location}: {weather.temperature}°C, {weather.weather_description}")
        else:
            print("❌ Failed to collect weather data for multiple locations")
            await collector.shutdown()
            return False
        
        await collector.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ Multiple locations test failed: {e}")
        return False


async def test_convenience_functions():
    """Test convenience functions."""
    print("\n🧪 Testing Convenience Functions...")
    
    try:
        print("🔄 Testing get_weather_for_location function...")
        
        weather = await get_weather_for_location("Tokyo", 35.6762, 139.6503)
        
        if weather:
            print("✅ Convenience function working correctly")
            print(f"📍 {weather.location}: {weather.temperature}°C")
            return True
        else:
            print("❌ Convenience function failed")
            return False
            
    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        return False


async def test_caching():
    """Test caching functionality."""
    print("\n🧪 Testing Caching Functionality...")
    
    try:
        collector = OpenWeatherMapCollector()
        await collector.initialize()
        
        test_location = {"name": "Sydney", "lat": -33.8688, "lon": 151.2093}
        
        print("🔄 First request (should fetch from API)...")
        start_time = asyncio.get_event_loop().time()
        weather1 = await collector.get_current_weather(test_location)
        time1 = asyncio.get_event_loop().time() - start_time
        
        print("🔄 Second request (should use cache)...")
        start_time = asyncio.get_event_loop().time()
        weather2 = await collector.get_current_weather(test_location)
        time2 = asyncio.get_event_loop().time() - start_time
        
        if weather1 and weather2 and time2 < time1:
            print("✅ Caching working correctly")
            print(f"⏱️ First request: {time1:.3f}s, Second request: {time2:.3f}s")
            await collector.shutdown()
            return True
        else:
            print("⚠️ Caching may not be working optimally")
            await collector.shutdown()
            return True  # Not a critical failure
            
    except Exception as e:
        print(f"❌ Caching test failed: {e}")
        return False


async def main():
    """Run all OpenWeatherMap tests."""
    print("🚀 OpenWeatherMap API Integration Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Basic API integration
    api_result = await test_openweather_api()
    test_results.append(("OpenWeatherMap API Integration", api_result))
    
    # Only run other tests if API is working
    if api_result:
        # Test 2: Multiple locations
        multi_result = await test_multiple_locations()
        test_results.append(("Multiple Locations Collection", multi_result))
        
        # Test 3: Convenience functions
        convenience_result = await test_convenience_functions()
        test_results.append(("Convenience Functions", convenience_result))
        
        # Test 4: Caching
        cache_result = await test_caching()
        test_results.append(("Caching Functionality", cache_result))
    else:
        test_results.append(("Multiple Locations Collection", False))
        test_results.append(("Convenience Functions", False))
        test_results.append(("Caching Functionality", False))
        print("⏭️ Skipping additional tests due to API failure")
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All OpenWeatherMap tests passed!")
        print("Weather data collection is ready for use.")
    elif passed > 0:
        print(f"\n⚠️ Partial success ({passed}/{total} tests passed)")
        print("Basic functionality is working, but some features may need attention.")
    else:
        print("\n❌ All tests failed.")
        print("Please check your OpenWeatherMap API key and internet connection.")
    
    print("\n📋 Next Steps:")
    if not api_result:
        print("  1. Get a free API key from: https://openweathermap.org/api")
        print("  2. Add OPENWEATHER_API_KEY=your_key_here to your .env file")
        print("  3. Re-run this test")
    else:
        print("  1. OpenWeatherMap integration is ready!")
        print("  2. You can now collect real-time weather data")
        print("  3. Weather data will be used for climate analysis")


if __name__ == "__main__":
    asyncio.run(main())
