<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Error Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1e3c72;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .success { color: #4ade80; }
        .error { color: #ef4444; }
        .warning { color: #fbbf24; }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #2563eb;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Frontend Error Testing & Debugging</h1>
    
    <div class="test-section">
        <h2>📡 API Connectivity Tests</h2>
        <button onclick="testAPI('/api/health')">Test Health API</button>
        <button onclick="testAPI('/api/water-quality')">Test Water Quality API</button>
        <button onclick="testAPI('/api/energy/grid')">Test Energy Grid API</button>
        <button onclick="testAPI('/api/ai/agents')">Test AI Agents API</button>
        <button onclick="testChatAPI()">Test Chat API</button>
    </div>

    <div class="test-section">
        <h2>🎮 Interactive Element Tests</h2>
        <button onclick="testButtonActions()">Test Button Actions</button>
        <button onclick="testDropdowns()">Test Dropdowns</button>
        <button onclick="testFormSubmission()">Test Form Submission</button>
        <button onclick="testNotifications()">Test Notifications</button>
    </div>

    <div class="test-section">
        <h2>📄 Page Navigation Tests</h2>
        <button onclick="testPageNavigation()">Test All Page Navigation</button>
        <button onclick="testContentLoading()">Test Content Loading</button>
    </div>

    <div class="test-section">
        <h2>🔧 JavaScript Error Detection</h2>
        <button onclick="checkConsoleErrors()">Check Console Errors</button>
        <button onclick="testGlobalVariables()">Test Global Variables</button>
        <button onclick="runComprehensiveTest()">Run All Tests</button>
    </div>

    <div id="results">
        <h3>📊 Test Results</h3>
        <div id="test-output">Click a test button to start testing...</div>
    </div>

    <script>
        let testResults = [];
        let errorCount = 0;
        let warningCount = 0;
        let successCount = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const output = document.getElementById('test-output');
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : '';
            
            const logEntry = `<div class="${className}">[${timestamp}] ${message}</div>`;
            output.innerHTML += logEntry;
            output.scrollTop = output.scrollHeight;
            
            testResults.push({timestamp, message, type});
            
            if (type === 'error') errorCount++;
            else if (type === 'warning') warningCount++;
            else if (type === 'success') successCount++;
        }

        async function testAPI(endpoint) {
            log(`🔍 Testing API endpoint: ${endpoint}`, 'info');
            try {
                const response = await fetch(endpoint);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ ${endpoint} - HTTP ${response.status} - Success`, 'success');
                    return true;
                } else {
                    log(`❌ ${endpoint} - HTTP ${response.status} - Failed`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ ${endpoint} - Network Error: ${error.message}`, 'error');
                return false;
            }
        }

        async function testChatAPI() {
            log('🔍 Testing Chat API...', 'info');
            try {
                const response = await fetch('/api/ai/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: 'Test message from frontend' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.response) {
                        log('✅ Chat API - Response received successfully', 'success');
                        return true;
                    } else {
                        log('⚠️ Chat API - No response content', 'warning');
                        return false;
                    }
                } else {
                    log(`❌ Chat API - HTTP ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Chat API - Error: ${error.message}`, 'error');
                return false;
            }
        }

        function testButtonActions() {
            log('🔍 Testing button actions...', 'info');
            
            // Test if PageManager exists
            if (typeof window.pageManager !== 'undefined') {
                log('✅ PageManager is available', 'success');
                
                // Test some action methods
                const testActions = ['exportData', 'showSettings', 'showHelp', 'updateRealTimeData'];
                let actionTests = 0;
                
                testActions.forEach(action => {
                    if (typeof window.pageManager[action] === 'function') {
                        log(`✅ Action method ${action} exists`, 'success');
                        actionTests++;
                    } else {
                        log(`❌ Action method ${action} missing`, 'error');
                    }
                });
                
                log(`📊 Button Actions: ${actionTests}/${testActions.length} methods available`, 
                    actionTests === testActions.length ? 'success' : 'warning');
            } else {
                log('❌ PageManager not found - Button actions may not work', 'error');
            }
        }

        function testDropdowns() {
            log('🔍 Testing dropdown functionality...', 'info');
            
            const dropdowns = document.querySelectorAll('select[data-action]');
            if (dropdowns.length > 0) {
                log(`✅ Found ${dropdowns.length} interactive dropdowns`, 'success');
                
                // Test dropdown change handler
                if (typeof window.pageManager !== 'undefined' && 
                    typeof window.pageManager.handleDropdownChange === 'function') {
                    log('✅ Dropdown change handler exists', 'success');
                } else {
                    log('❌ Dropdown change handler missing', 'error');
                }
            } else {
                log('⚠️ No interactive dropdowns found', 'warning');
            }
        }

        function testFormSubmission() {
            log('🔍 Testing form submission...', 'info');
            
            // Check for chat form
            const chatForm = document.getElementById('chatForm');
            if (chatForm) {
                log('✅ Chat form found', 'success');
                
                // Test form submission handler
                if (typeof window.pageManager !== 'undefined' && 
                    typeof window.pageManager.handleChatSubmission === 'function') {
                    log('✅ Chat submission handler exists', 'success');
                } else {
                    log('❌ Chat submission handler missing', 'error');
                }
            } else {
                log('⚠️ Chat form not found', 'warning');
            }
        }

        function testNotifications() {
            log('🔍 Testing notification system...', 'info');
            
            if (typeof window.pageManager !== 'undefined' && 
                typeof window.pageManager.showNotification === 'function') {
                log('✅ Notification system available', 'success');
                
                // Test notification
                try {
                    window.pageManager.showNotification('Test notification', 'info');
                    log('✅ Test notification displayed', 'success');
                } catch (error) {
                    log(`❌ Notification test failed: ${error.message}`, 'error');
                }
            } else {
                log('❌ Notification system not available', 'error');
            }
        }

        function testPageNavigation() {
            log('🔍 Testing page navigation...', 'info');
            
            const sidebarItems = document.querySelectorAll('.sidebar-item[data-page]');
            if (sidebarItems.length > 0) {
                log(`✅ Found ${sidebarItems.length} navigation items`, 'success');
                
                // Test loadPage method
                if (typeof window.pageManager !== 'undefined' && 
                    typeof window.pageManager.loadPage === 'function') {
                    log('✅ Page loading method exists', 'success');
                } else {
                    log('❌ Page loading method missing', 'error');
                }
            } else {
                log('❌ No navigation items found', 'error');
            }
        }

        function testContentLoading() {
            log('🔍 Testing content loading methods...', 'info');
            
            const contentMethods = [
                'getWaterQualityPageContent',
                'getTreatmentSystemsPageContent', 
                'getEnergyGridPageContent',
                'getAIAgentsPageContent'
            ];
            
            let availableMethods = 0;
            
            if (typeof window.pageManager !== 'undefined') {
                contentMethods.forEach(method => {
                    if (typeof window.pageManager[method] === 'function') {
                        log(`✅ Content method ${method} exists`, 'success');
                        availableMethods++;
                    } else {
                        log(`❌ Content method ${method} missing`, 'error');
                    }
                });
            } else {
                log('❌ PageManager not available for content testing', 'error');
            }
            
            log(`📊 Content Methods: ${availableMethods}/${contentMethods.length} available`, 
                availableMethods === contentMethods.length ? 'success' : 'warning');
        }

        function checkConsoleErrors() {
            log('🔍 Checking for JavaScript console errors...', 'info');
            
            // Override console.error to catch errors
            const originalError = console.error;
            let errorsCaught = 0;
            
            console.error = function(...args) {
                errorsCaught++;
                log(`❌ Console Error: ${args.join(' ')}`, 'error');
                originalError.apply(console, args);
            };
            
            // Check for common global variables
            const requiredGlobals = ['pageManager', 'app'];
            requiredGlobals.forEach(global => {
                if (typeof window[global] !== 'undefined') {
                    log(`✅ Global variable ${global} exists`, 'success');
                } else {
                    log(`⚠️ Global variable ${global} missing`, 'warning');
                }
            });
            
            setTimeout(() => {
                if (errorsCaught === 0) {
                    log('✅ No console errors detected', 'success');
                } else {
                    log(`❌ ${errorsCaught} console errors detected`, 'error');
                }
            }, 1000);
        }

        function testGlobalVariables() {
            log('🔍 Testing global variables and functions...', 'info');
            
            // Test Chart.js availability
            if (typeof Chart !== 'undefined') {
                log('✅ Chart.js library loaded', 'success');
            } else {
                log('⚠️ Chart.js library not loaded', 'warning');
            }
            
            // Test PageManager initialization
            if (typeof window.pageManager !== 'undefined') {
                log('✅ PageManager initialized', 'success');
                
                // Test key methods
                const keyMethods = ['loadPage', 'handleGenericAction', 'updateRealTimeData'];
                keyMethods.forEach(method => {
                    if (typeof window.pageManager[method] === 'function') {
                        log(`✅ Key method ${method} available`, 'success');
                    } else {
                        log(`❌ Key method ${method} missing`, 'error');
                    }
                });
            } else {
                log('❌ PageManager not initialized', 'error');
            }
        }

        async function runComprehensiveTest() {
            log('🚀 Starting comprehensive frontend test...', 'info');
            
            // Clear previous results
            errorCount = 0;
            warningCount = 0;
            successCount = 0;
            
            // Run all tests
            await testAPI('/api/health');
            await testAPI('/api/water-quality');
            await testChatAPI();
            testButtonActions();
            testDropdowns();
            testFormSubmission();
            testNotifications();
            testPageNavigation();
            testContentLoading();
            checkConsoleErrors();
            testGlobalVariables();
            
            // Generate summary
            setTimeout(() => {
                log('', 'info');
                log('📊 COMPREHENSIVE TEST SUMMARY', 'info');
                log('═══════════════════════════════', 'info');
                log(`✅ Successes: ${successCount}`, 'success');
                log(`⚠️ Warnings: ${warningCount}`, 'warning');
                log(`❌ Errors: ${errorCount}`, 'error');
                
                const status = errorCount === 0 ? 
                    (warningCount === 0 ? '🟢 EXCELLENT' : '🟡 GOOD') : 
                    '🔴 NEEDS ATTENTION';
                    
                log(`🎯 Overall Status: ${status}`, errorCount === 0 ? 'success' : 'error');
            }, 2000);
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            log('🔍 Frontend testing page loaded', 'info');
            log('Click "Run All Tests" for comprehensive analysis', 'info');
        });
    </script>
</body>
</html>
