<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌊💧 Unified Environmental Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a1929 0%, #132f4c 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-online { background: #4caf50; }
        .status-offline { background: #f44336; }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: linear-gradient(135deg, #132f4c 0%, #1e3a5f 100%);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .card h3 {
            margin-bottom: 1rem;
            color: #64b5f6;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #4caf50;
        }
        
        .btn {
            background: linear-gradient(45deg, #2196f3 30%, #21cbf3 90%);
            border: none;
            border-radius: 6px;
            color: white;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #b0bec5;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid #f44336;
            color: #f44336;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid #4caf50;
            color: #4caf50;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌊💧 Unified Environmental Platform</h1>
        <div class="status">
            <div class="status-item">
                <div class="status-dot" id="backend-status"></div>
                <span>Backend</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="frontend-status"></div>
                <span>Frontend</span>
            </div>
            <button class="btn" onclick="refreshData()">🔄 Refresh</button>
        </div>
    </div>

    <div class="container">
        <div id="status-message"></div>
        
        <div class="dashboard-grid">
            <!-- Marine Conservation Card -->
            <div class="card">
                <h3>🌊 Marine Conservation</h3>
                <div id="marine-data">
                    <div class="loading">Loading marine data...</div>
                </div>
            </div>

            <!-- Water Management Card -->
            <div class="card">
                <h3>💧 Water Management</h3>
                <div id="water-data">
                    <div class="loading">Loading water data...</div>
                </div>
            </div>

            <!-- System Status Card -->
            <div class="card">
                <h3>🔧 System Status</h3>
                <div id="system-data">
                    <div class="loading">Loading system data...</div>
                </div>
            </div>

            <!-- Integrated Analytics Card -->
            <div class="card">
                <h3>📊 Integrated Analytics</h3>
                <div id="analytics-data">
                    <div class="loading">Loading analytics...</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>🔗 API Endpoints</h3>
            <div>
                <button class="btn" onclick="testEndpoint('/health')">Test Health</button>
                <button class="btn" onclick="testEndpoint('/api/status')">Test Status</button>
                <button class="btn" onclick="testEndpoint('/api/dashboard')">Test Dashboard</button>
                <button class="btn" onclick="window.open('http://localhost:8000/docs', '_blank')">📚 API Docs</button>
            </div>
            <div id="endpoint-results"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Update status indicators
        function updateStatus(elementId, isOnline) {
            const element = document.getElementById(elementId);
            element.className = `status-dot ${isOnline ? 'status-online' : 'status-offline'}`;
        }
        
        // Show message
        function showMessage(message, type = 'success') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => statusDiv.innerHTML = '', 5000);
        }
        
        // Test specific endpoint
        async function testEndpoint(endpoint) {
            const resultsDiv = document.getElementById('endpoint-results');
            try {
                const response = await fetch(API_BASE + endpoint);
                const data = await response.json();
                resultsDiv.innerHTML = `
                    <div class="success">
                        <strong>${endpoint}</strong> - Status: ${response.status}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <strong>${endpoint}</strong> - Error: ${error.message}
                    </div>
                `;
            }
        }
        
        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Test backend connection
                const healthResponse = await fetch(API_BASE + '/health');
                updateStatus('backend-status', healthResponse.ok);
                updateStatus('frontend-status', true);
                
                if (!healthResponse.ok) {
                    throw new Error('Backend not responding');
                }
                
                // Load dashboard data
                const dashboardResponse = await fetch(API_BASE + '/api/dashboard');
                const dashboardData = await dashboardResponse.json();
                
                if (dashboardData.success) {
                    const data = dashboardData.data;
                    
                    // Update Marine Conservation
                    document.getElementById('marine-data').innerHTML = `
                        <div class="metric">
                            <span>Health Score</span>
                            <span class="metric-value">${(data.marine_conservation?.health_score * 100 || 85).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Debris Count</span>
                            <span class="metric-value">${data.marine_conservation?.debris_count || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Vessel Count</span>
                            <span class="metric-value">${data.marine_conservation?.vessel_count || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Risk Level</span>
                            <span class="metric-value">${data.marine_conservation?.risk_level || 'Low'}</span>
                        </div>
                    `;
                    
                    // Update Water Management
                    document.getElementById('water-data').innerHTML = `
                        <div class="metric">
                            <span>Treatment Efficiency</span>
                            <span class="metric-value">${(data.water_management?.treatment_efficiency * 100 || 92).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Energy Efficiency</span>
                            <span class="metric-value">${(data.water_management?.energy_efficiency * 100 || 87).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Carbon Footprint</span>
                            <span class="metric-value">${data.water_management?.carbon_footprint || 246} kg CO₂</span>
                        </div>
                        <div class="metric">
                            <span>Status</span>
                            <span class="metric-value">${data.water_management?.status || 'Active'}</span>
                        </div>
                    `;
                    
                    // Update System Status
                    document.getElementById('system-data').innerHTML = `
                        <div class="metric">
                            <span>System Health</span>
                            <span class="metric-value">${(data.real_time?.system_health * 100 || 88).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Active Operations</span>
                            <span class="metric-value">${data.real_time?.active_operations || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Alerts</span>
                            <span class="metric-value">${data.real_time?.alerts_count || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Last Update</span>
                            <span class="metric-value">${new Date(data.timestamp).toLocaleTimeString()}</span>
                        </div>
                    `;
                    
                    // Update Analytics
                    document.getElementById('analytics-data').innerHTML = `
                        <div class="metric">
                            <span>Environmental Score</span>
                            <span class="metric-value">${(data.integrated_analytics?.environmental_score * 100 || 88).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Synergy Score</span>
                            <span class="metric-value">${(data.integrated_analytics?.synergy_score * 100 || 75).toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Correlations</span>
                            <span class="metric-value">${data.integrated_analytics?.correlations?.length || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Recommendations</span>
                            <span class="metric-value">${data.integrated_analytics?.recommendations?.length || 0}</span>
                        </div>
                    `;
                    
                    showMessage('✅ Dashboard data loaded successfully!');
                } else {
                    throw new Error('Dashboard API returned error');
                }
                
            } catch (error) {
                updateStatus('backend-status', false);
                showMessage(`❌ Error loading data: ${error.message}`, 'error');
                
                // Show error in all cards
                const errorMsg = '<div class="error">Unable to load data. Check backend connection.</div>';
                document.getElementById('marine-data').innerHTML = errorMsg;
                document.getElementById('water-data').innerHTML = errorMsg;
                document.getElementById('system-data').innerHTML = errorMsg;
                document.getElementById('analytics-data').innerHTML = errorMsg;
            }
        }
        
        // Refresh data
        function refreshData() {
            showMessage('🔄 Refreshing data...');
            loadDashboardData();
        }
        
        // Auto-refresh every 30 seconds
        setInterval(loadDashboardData, 30000);
        
        // Load data on page load
        window.addEventListener('load', loadDashboardData);
    </script>
</body>
</html>
