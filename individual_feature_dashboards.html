<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Individual Feature Dashboards - Environmental Platform</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css">
    <script src="all_feature_configurations.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: #f1f5f9;
            min-height: 100vh;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Dashboard Navigation Sidebar */
        .dashboard-nav {
            width: 350px;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(59, 130, 246, 0.2);
            padding: 20px 0;
            overflow-y: auto;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .nav-header {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            margin-bottom: 20px;
        }

        .nav-header h1 {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .nav-header p {
            color: #64748b;
            font-size: 12px;
        }

        .dashboard-search {
            margin: 0 20px 20px;
            position: relative;
        }

        .search-input {
            width: 100%;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding: 10px 15px 10px 40px;
            color: #f1f5f9;
            font-size: 14px;
            outline: none;
        }

        .search-input::placeholder {
            color: #64748b;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
        }

        .dashboard-category {
            margin-bottom: 25px;
        }

        .category-header {
            padding: 0 20px 10px;
            color: #64748b;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .dashboard-count {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
        }

        .dashboard-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #94a3b8;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            cursor: pointer;
            margin-bottom: 2px;
        }

        .dashboard-item:hover, .dashboard-item.active {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-left-color: #3b82f6;
        }

        .dashboard-icon {
            width: 18px;
            margin-right: 12px;
            font-size: 16px;
        }

        .dashboard-info {
            flex: 1;
        }

        .dashboard-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .dashboard-desc {
            font-size: 11px;
            color: #64748b;
            line-height: 1.3;
        }

        .dashboard-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #22c55e;
            margin-left: 8px;
        }

        /* Main Dashboard Area */
        .main-dashboard {
            flex: 1;
            margin-left: 350px;
            padding: 30px;
        }

        .dashboard-header {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .dashboard-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .dashboard-title-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
        }

        .dashboard-title-icon i {
            font-size: 32px;
            color: white;
        }

        .dashboard-title-text h1 {
            font-size: 36px;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 5px;
        }

        .dashboard-title-text p {
            color: #64748b;
            font-size: 16px;
        }

        .dashboard-badges {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-status {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .badge-type {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .badge-priority {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .dashboard-description {
            color: #e2e8f0;
            font-size: 16px;
            line-height: 1.6;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.4);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .card-icon i {
            font-size: 24px;
            color: white;
        }

        .card-title {
            font-size: 20px;
            font-weight: 700;
            color: #f1f5f9;
            margin-bottom: 5px;
        }

        .card-subtitle {
            font-size: 12px;
            color: #64748b;
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(59, 130, 246, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #64748b;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Chart Container */
        .chart-container {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #f1f5f9;
        }

        .chart-canvas {
            height: 200px;
        }

        /* Control Panel */
        .control-panel {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 12px;
            padding: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            color: #94a3b8;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .control-input {
            width: 100%;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            color: #f1f5f9;
            font-size: 12px;
            outline: none;
        }

        .control-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #22c55e, #16a34a);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        /* Activity Feed */
        .activity-feed {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 12px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 30px;
            height: 30px;
            background: rgba(59, 130, 246, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }

        .activity-icon i {
            font-size: 12px;
            color: #3b82f6;
        }

        .activity-content {
            flex: 1;
        }

        .activity-text {
            color: #e2e8f0;
            font-size: 12px;
            margin-bottom: 2px;
        }

        .activity-time {
            color: #64748b;
            font-size: 10px;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .dashboard-nav {
                width: 300px;
            }
            .main-dashboard {
                margin-left: 300px;
            }
            .dashboard-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-nav {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .dashboard-nav.open {
                transform: translateX(0);
            }
            .main-dashboard {
                margin-left: 0;
                padding: 20px;
            }
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Dashboard Navigation Sidebar -->
        <div class="dashboard-nav">
            <div class="nav-header">
                <h1><i class="fas fa-chart-pie"></i> Feature Dashboards</h1>
                <p>Individual Dashboard for Every Feature</p>
            </div>

            <div class="dashboard-search">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Search dashboards..." id="dashboard-search">
            </div>

            <div id="dashboard-navigation">
                <!-- Dashboard categories will be dynamically loaded here -->
            </div>
        </div>

        <!-- Main Dashboard Area -->
        <div class="main-dashboard">
            <div id="feature-dashboard">
                <!-- Individual feature dashboard will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Current state
        let currentDashboard = null;
        let searchTerm = '';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardNavigation();
            setupSearch();
            loadDefaultDashboard();
        });

        // Load dashboard navigation
        function loadDashboardNavigation() {
            const navigation = document.getElementById('dashboard-navigation');
            const categories = {};
            
            // Group features by category
            Object.keys(allFeatureConfigurations).forEach(featureId => {
                const feature = allFeatureConfigurations[featureId];
                if (!categories[feature.category]) {
                    categories[feature.category] = [];
                }
                categories[feature.category].push({ id: featureId, ...feature });
            });
            
            // Generate navigation HTML
            let navigationHTML = '';
            Object.keys(categories).forEach(categoryName => {
                const features = categories[categoryName];
                navigationHTML += `
                    <div class="dashboard-category">
                        <div class="category-header">
                            <span>${categoryName}</span>
                            <span class="dashboard-count">${features.length}</span>
                        </div>
                        ${features.map(feature => `
                            <div class="dashboard-item" data-dashboard="${feature.id}" onclick="loadDashboard('${feature.id}')">
                                <i class="${feature.icon} dashboard-icon"></i>
                                <div class="dashboard-info">
                                    <div class="dashboard-name">${feature.name}</div>
                                    <div class="dashboard-desc">${feature.description.substring(0, 50)}...</div>
                                </div>
                                <div class="dashboard-status"></div>
                            </div>
                        `).join('')}
                    </div>
                `;
            });
            
            navigation.innerHTML = navigationHTML;
        }

        // Setup search functionality
        function setupSearch() {
            const searchInput = document.getElementById('dashboard-search');
            searchInput.addEventListener('input', function() {
                searchTerm = this.value.toLowerCase();
                filterDashboards();
            });
        }

        // Filter dashboards based on search
        function filterDashboards() {
            const dashboardItems = document.querySelectorAll('.dashboard-item');
            dashboardItems.forEach(item => {
                const dashboardName = item.querySelector('.dashboard-name').textContent.toLowerCase();
                const dashboardDesc = item.querySelector('.dashboard-desc').textContent.toLowerCase();
                if (dashboardName.includes(searchTerm) || dashboardDesc.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Load default dashboard
        function loadDefaultDashboard() {
            const firstFeature = Object.keys(allFeatureConfigurations)[0];
            if (firstFeature) {
                loadDashboard(firstFeature);
            }
        }

        // Load individual dashboard
        function loadDashboard(featureId) {
            currentDashboard = featureId;
            const feature = allFeatureConfigurations[featureId];

            if (!feature) return;

            // Update active state
            document.querySelectorAll('.dashboard-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-dashboard="${featureId}"]`).classList.add('active');

            // Generate dashboard interface
            const dashboardContainer = document.getElementById('feature-dashboard');
            dashboardContainer.innerHTML = generateDashboard(feature);

            // Initialize dashboard components
            setTimeout(() => {
                initializeDashboardComponents(feature);
            }, 100);
        }

        // Generate dashboard HTML
        function generateDashboard(feature) {
            return `
                <!-- Dashboard Header -->
                <div class="dashboard-header">
                    <div class="dashboard-title">
                        <div class="dashboard-title-icon">
                            <i class="${feature.icon}"></i>
                        </div>
                        <div class="dashboard-title-text">
                            <h1>${feature.name} Dashboard</h1>
                            <p>Comprehensive monitoring and control dashboard</p>
                        </div>
                    </div>
                    <div class="dashboard-badges">
                        <span class="badge badge-status">
                            <i class="fas fa-circle"></i> ${feature.status.toUpperCase()}
                        </span>
                        <span class="badge badge-type">
                            <i class="fas fa-tag"></i> ${feature.type.toUpperCase()}
                        </span>
                        <span class="badge badge-priority">
                            <i class="fas fa-star"></i> HIGH PRIORITY
                        </span>
                    </div>
                    <div class="dashboard-description">
                        ${feature.description}
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Real-time Metrics Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <div class="card-title">Real-time Metrics</div>
                                <div class="card-subtitle">Live performance indicators</div>
                            </div>
                        </div>
                        <div class="metrics-grid">
                            ${Object.entries(feature.metrics).map(([key, value]) => `
                                <div class="metric-item">
                                    <div class="metric-value">${value}</div>
                                    <div class="metric-label">${key}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Performance Chart Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-chart-area"></i>
                            </div>
                            <div>
                                <div class="card-title">Performance Analytics</div>
                                <div class="card-subtitle">Historical performance data</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">Performance Trend</div>
                                <button class="btn" onclick="refreshChart('${feature.name}')">
                                    <i class="fas fa-sync"></i> Refresh
                                </button>
                            </div>
                            <canvas id="performance-chart-${feature.feature_id}" class="chart-canvas"></canvas>
                        </div>
                    </div>

                    <!-- Control Panel Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-sliders-h"></i>
                            </div>
                            <div>
                                <div class="card-title">Control Panel</div>
                                <div class="card-subtitle">Feature configuration and controls</div>
                            </div>
                        </div>
                        <div class="control-panel">
                            ${generateControlPanel(feature)}
                        </div>
                    </div>

                    <!-- Activity Feed Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div>
                                <div class="card-title">Activity Feed</div>
                                <div class="card-subtitle">Recent events and actions</div>
                            </div>
                        </div>
                        <div class="activity-feed">
                            ${generateActivityFeed(feature)}
                        </div>
                    </div>

                    <!-- Status Overview Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div>
                                <div class="card-title">Status Overview</div>
                                <div class="card-subtitle">Current operational status</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">System Health</div>
                            </div>
                            <canvas id="status-chart-${feature.feature_id}" class="chart-canvas"></canvas>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div>
                                <div class="card-title">Quick Actions</div>
                                <div class="card-subtitle">Common operations and tasks</div>
                            </div>
                        </div>
                        <div class="control-panel">
                            <button class="btn btn-success" onclick="startFeature('${feature.name}')">
                                <i class="fas fa-play"></i> Start Feature
                            </button>
                            <button class="btn btn-warning" onclick="restartFeature('${feature.name}')">
                                <i class="fas fa-redo"></i> Restart
                            </button>
                            <button class="btn" onclick="testFeature('${feature.name}')">
                                <i class="fas fa-vial"></i> Test
                            </button>
                            <button class="btn" onclick="exportData('${feature.name}')">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-danger" onclick="stopFeature('${feature.name}')">
                                <i class="fas fa-stop"></i> Stop
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Generate control panel
        function generateControlPanel(feature) {
            const configControls = feature.options.configuration.controls;
            return configControls.map(control => {
                switch(control.type) {
                    case 'checkbox':
                        return `
                            <div class="control-group">
                                <label class="control-label">
                                    <input type="checkbox" ${control.checked ? 'checked' : ''}> ${control.label}
                                </label>
                            </div>
                        `;
                    case 'select':
                        return `
                            <div class="control-group">
                                <label class="control-label">${control.label}</label>
                                <select class="control-input">
                                    ${control.options.map(option => `
                                        <option value="${option}" ${option === control.value ? 'selected' : ''}>${option}</option>
                                    `).join('')}
                                </select>
                            </div>
                        `;
                    case 'range':
                        return `
                            <div class="control-group">
                                <label class="control-label">${control.label}: <span id="range-value-${control.id}">${control.value}</span></label>
                                <input type="range" class="control-input" min="${control.min}" max="${control.max}" value="${control.value}"
                                       oninput="document.getElementById('range-value-${control.id}').textContent = this.value">
                            </div>
                        `;
                    default:
                        return '';
                }
            }).join('');
        }

        // Generate activity feed
        function generateActivityFeed(feature) {
            const activities = [
                { icon: 'fas fa-play', text: `${feature.name} started successfully`, time: '2 minutes ago' },
                { icon: 'fas fa-chart-line', text: 'Performance metrics updated', time: '5 minutes ago' },
                { icon: 'fas fa-cog', text: 'Configuration changed', time: '10 minutes ago' },
                { icon: 'fas fa-check', text: 'Health check completed', time: '15 minutes ago' },
                { icon: 'fas fa-upload', text: 'Data synchronized', time: '20 minutes ago' },
                { icon: 'fas fa-bell', text: 'Alert threshold updated', time: '25 minutes ago' }
            ];

            return activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">${activity.text}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }

        // Initialize dashboard components
        function initializeDashboardComponents(feature) {
            // Initialize performance chart
            const performanceCtx = document.getElementById(`performance-chart-${feature.feature_id}`);
            if (performanceCtx) {
                new Chart(performanceCtx, {
                    type: 'line',
                    data: {
                        labels: ['1h ago', '45m ago', '30m ago', '15m ago', 'Now'],
                        datasets: [{
                            label: 'Performance Score',
                            data: [85, 87, 83, 89, 91],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                labels: { color: '#94a3b8' }
                            }
                        },
                        scales: {
                            x: {
                                ticks: { color: '#94a3b8' },
                                grid: { color: 'rgba(148, 163, 184, 0.1)' }
                            },
                            y: {
                                ticks: { color: '#94a3b8' },
                                grid: { color: 'rgba(148, 163, 184, 0.1)' }
                            }
                        }
                    }
                });
            }

            // Initialize status chart
            const statusCtx = document.getElementById(`status-chart-${feature.feature_id}`);
            if (statusCtx) {
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Healthy', 'Warning', 'Critical'],
                        datasets: [{
                            data: [85, 12, 3],
                            backgroundColor: [
                                '#22c55e',
                                '#f59e0b',
                                '#ef4444'
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#94a3b8',
                                    padding: 20
                                }
                            }
                        }
                    }
                });
            }
        }

        // Action functions
        function startFeature(featureName) {
            showNotification(`Starting ${featureName}...`, 'success');
            setTimeout(() => {
                showNotification(`${featureName} started successfully!`, 'success');
            }, 2000);
        }

        function stopFeature(featureName) {
            showNotification(`Stopping ${featureName}...`, 'warning');
            setTimeout(() => {
                showNotification(`${featureName} stopped safely!`, 'warning');
            }, 2000);
        }

        function restartFeature(featureName) {
            showNotification(`Restarting ${featureName}...`, 'info');
            setTimeout(() => {
                showNotification(`${featureName} restarted successfully!`, 'success');
            }, 3000);
        }

        function testFeature(featureName) {
            showNotification(`Testing ${featureName}...`, 'info');
            setTimeout(() => {
                showNotification(`${featureName} test completed - All systems operational!`, 'success');
            }, 2500);
        }

        function exportData(featureName) {
            showNotification(`Exporting data for ${featureName}...`, 'info');
            setTimeout(() => {
                showNotification(`Data exported successfully! Download started.`, 'success');
            }, 1500);
        }

        function refreshChart(featureName) {
            showNotification(`Refreshing ${featureName} chart data...`, 'info');
            setTimeout(() => {
                showNotification(`Chart data updated with latest metrics!`, 'success');
                // Reload current dashboard to refresh charts
                if (currentDashboard) {
                    loadDashboard(currentDashboard);
                }
            }, 1000);
        }

        // Notification system
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#22c55e' : type === 'warning' ? '#f59e0b' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        if (currentDashboard) {
                            loadDashboard(currentDashboard);
                            showNotification('Dashboard refreshed!', 'info');
                        }
                        break;
                    case 'f':
                        e.preventDefault();
                        document.getElementById('dashboard-search').focus();
                        break;
                }
            }
        });

        // Auto-refresh dashboard every 30 seconds
        setInterval(() => {
            if (currentDashboard) {
                // Update metrics without full reload
                updateDashboardMetrics();
            }
        }, 30000);

        function updateDashboardMetrics() {
            // Simulate metric updates
            const metricValues = document.querySelectorAll('.metric-value');
            metricValues.forEach(metric => {
                const currentValue = metric.textContent;
                if (currentValue.includes('%')) {
                    const newValue = Math.max(0, Math.min(100, parseInt(currentValue) + Math.floor(Math.random() * 6 - 3)));
                    metric.textContent = newValue + '%';
                }
            });
        }
    </script>
</body>
</html>
