#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Working Features Comprehensive Test Suite
Testing all working components of the marine conservation platform
"""

import asyncio
import sys
import os
import time
from datetime import datetime
from typing import Dict, List, Any

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

print("🔄 Loading marine conservation components...")

# Import working components
working_components = {}

try:
    from marine_conservation.agents.climate_marine_agent import ClimateMarineAgent
    working_components['climate_agent'] = ClimateMarineAgent
    print("   ✅ Climate Marine Agent loaded")
except Exception as e:
    print(f"   ❌ Climate Marine Agent: {e}")

try:
    from marine_conservation.agents.water_treatment_marine_agent import WaterTreatmentMarineAgent
    working_components['water_agent'] = WaterTreatmentMarineAgent
    print("   ✅ Water Treatment Agent loaded")
except Exception as e:
    print(f"   ❌ Water Treatment Agent: {e}")

try:
    from marine_conservation.agents.energy_efficiency_marine_agent import EnergyEfficiencyMarineAgent
    working_components['energy_agent'] = EnergyEfficiencyMarineAgent
    print("   ✅ Energy Efficiency Agent loaded")
except Exception as e:
    print(f"   ❌ Energy Efficiency Agent: {e}")

try:
    from marine_conservation.agents.sustainability_marine_agent import SustainabilityMarineAgent
    working_components['sustainability_agent'] = SustainabilityMarineAgent
    print("   ✅ Sustainability Agent loaded")
except Exception as e:
    print(f"   ❌ Sustainability Agent: {e}")

try:
    from marine_conservation.agents.risk_analysis_marine_agent import RiskAnalysisMarineAgent
    working_components['risk_agent'] = RiskAnalysisMarineAgent
    print("   ✅ Risk Analysis Agent loaded")
except Exception as e:
    print(f"   ❌ Risk Analysis Agent: {e}")

try:
    from marine_conservation.ml_models.debris_categorization import MLDebrisCategorizer
    working_components['ml_categorizer'] = MLDebrisCategorizer
    print("   ✅ ML Debris Categorizer loaded")
except Exception as e:
    print(f"   ❌ ML Debris Categorizer: {e}")

try:
    from marine_conservation.recycling.ai_recycling_optimizer import AIRecyclingOptimizer
    working_components['recycling_optimizer'] = AIRecyclingOptimizer
    print("   ✅ AI Recycling Optimizer loaded")
except Exception as e:
    print(f"   ❌ AI Recycling Optimizer: {e}")

try:
    from marine_conservation.rapid_implementation.all_remaining_tasks import (
        CommunityEngagementAgent,
        PolicyAnalysisAgent,
        InnovationAgent,
        AdvancedAnalyticsEngine,
        BlockchainIntegration,
        ARVRExperiences,
        IoTSensorNetworks,
        GlobalScaling
    )
    working_components.update({
        'community_agent': CommunityEngagementAgent,
        'policy_agent': PolicyAnalysisAgent,
        'innovation_agent': InnovationAgent,
        'analytics_engine': AdvancedAnalyticsEngine,
        'blockchain_system': BlockchainIntegration,
        'ar_vr_suite': ARVRExperiences,
        'iot_network': IoTSensorNetworks,
        'global_scaling': GlobalScaling
    })
    print("   ✅ New feature components loaded")
except Exception as e:
    print(f"   ❌ New feature components: {e}")

try:
    from marine_conservation.integrated_platform.simplified_unified_platform import SimplifiedUnifiedPlatform
    working_components['unified_platform'] = SimplifiedUnifiedPlatform
    print("   ✅ Unified Platform loaded")
except Exception as e:
    print(f"   ❌ Unified Platform: {e}")

print(f"\n📊 Components loaded: {len(working_components)}")


class WorkingFeaturesTestSuite:
    """Test suite for all working marine conservation features"""
    
    def __init__(self):
        self.test_areas = {
            'taiwan_strait': (119.0, 23.0, 121.0, 25.0),
            'mediterranean': (2.0, 41.0, 3.0, 42.0),
            'pacific_coast': (-125.0, 32.0, -117.0, 37.0)
        }
        
        self.test_results = {}
        self.components = working_components
    
    async def test_ai_agents(self) -> Dict[str, bool]:
        """Test all AI agents"""
        print("\n🤖 Testing AI Agents")
        print("=" * 40)
        
        agent_results = {}
        test_area = self.test_areas['taiwan_strait']
        
        # Test Climate Agent
        if 'climate_agent' in self.components:
            try:
                agent = self.components['climate_agent']()
                result = await agent.generate_climate_report(test_area)
                agent_results['climate_agent'] = result is not None
                print(f"   ✅ Climate Agent: {'Success' if agent_results['climate_agent'] else 'No data'}")
            except Exception as e:
                agent_results['climate_agent'] = False
                print(f"   ❌ Climate Agent: {e}")
        
        # Test Water Treatment Agent
        if 'water_agent' in self.components:
            try:
                agent = self.components['water_agent']()
                result = await agent.optimize_water_treatment(test_area)
                agent_results['water_agent'] = result is not None
                print(f"   ✅ Water Treatment Agent: {'Success' if agent_results['water_agent'] else 'No data'}")
            except Exception as e:
                agent_results['water_agent'] = False
                print(f"   ❌ Water Treatment Agent: {e}")
        
        # Test Energy Efficiency Agent
        if 'energy_agent' in self.components:
            try:
                agent = self.components['energy_agent']()
                result = await agent.optimize_energy_systems(test_area)
                agent_results['energy_agent'] = result is not None
                print(f"   ✅ Energy Efficiency Agent: {'Success' if agent_results['energy_agent'] else 'No data'}")
            except Exception as e:
                agent_results['energy_agent'] = False
                print(f"   ❌ Energy Efficiency Agent: {e}")
        
        # Test Sustainability Agent
        if 'sustainability_agent' in self.components:
            try:
                agent = self.components['sustainability_agent']()
                result = await agent.assess_marine_ecosystem(test_area)
                agent_results['sustainability_agent'] = result is not None
                print(f"   ✅ Sustainability Agent: {'Success' if agent_results['sustainability_agent'] else 'No data'}")
            except Exception as e:
                agent_results['sustainability_agent'] = False
                print(f"   ❌ Sustainability Agent: {e}")
        
        # Test Risk Analysis Agent
        if 'risk_agent' in self.components:
            try:
                agent = self.components['risk_agent']()
                result = await agent.assess_marine_conservation_risks(test_area)
                agent_results['risk_agent'] = result is not None
                print(f"   ✅ Risk Analysis Agent: {'Success' if agent_results['risk_agent'] else 'No data'}")
            except Exception as e:
                agent_results['risk_agent'] = False
                print(f"   ❌ Risk Analysis Agent: {e}")
        
        success_rate = sum(agent_results.values()) / len(agent_results) if agent_results else 0
        print(f"\n📊 AI Agents: {sum(agent_results.values())}/{len(agent_results)} passed ({success_rate:.1%})")
        
        return agent_results
    
    async def test_ml_components(self) -> Dict[str, bool]:
        """Test ML and analysis components"""
        print("\n🔬 Testing ML Components")
        print("=" * 40)
        
        ml_results = {}
        test_area = self.test_areas['taiwan_strait']
        
        # Test ML Debris Categorizer
        if 'ml_categorizer' in self.components:
            try:
                categorizer = self.components['ml_categorizer']()
                result = await categorizer.classify_debris_in_area(test_area)
                ml_results['ml_categorizer'] = isinstance(result, list)
                print(f"   ✅ ML Debris Categorizer: {'Success' if ml_results['ml_categorizer'] else 'Error'}")
            except Exception as e:
                ml_results['ml_categorizer'] = False
                print(f"   ❌ ML Debris Categorizer: {e}")
        
        # Test AI Recycling Optimizer
        if 'recycling_optimizer' in self.components:
            try:
                optimizer = self.components['recycling_optimizer']()
                # Create mock debris data
                mock_debris = []
                result = await optimizer.optimize_recycling_pathways(mock_debris)
                ml_results['recycling_optimizer'] = result is not None
                print(f"   ✅ AI Recycling Optimizer: {'Success' if ml_results['recycling_optimizer'] else 'Error'}")
            except Exception as e:
                ml_results['recycling_optimizer'] = False
                print(f"   ❌ AI Recycling Optimizer: {e}")
        
        success_rate = sum(ml_results.values()) / len(ml_results) if ml_results else 0
        print(f"\n📊 ML Components: {sum(ml_results.values())}/{len(ml_results)} passed ({success_rate:.1%})")
        
        return ml_results
    
    async def test_new_features(self) -> Dict[str, bool]:
        """Test new feature components"""
        print("\n✨ Testing New Features")
        print("=" * 40)
        
        new_feature_results = {}
        test_area = self.test_areas['taiwan_strait']
        
        # Test Community Engagement Agent
        if 'community_agent' in self.components:
            try:
                agent = self.components['community_agent']()
                result = await agent.create_engagement_campaign(test_area)
                new_feature_results['community_agent'] = 'campaign_id' in result
                print(f"   ✅ Community Engagement: {'Success' if new_feature_results['community_agent'] else 'Error'}")
            except Exception as e:
                new_feature_results['community_agent'] = False
                print(f"   ❌ Community Engagement: {e}")
        
        # Test Policy Analysis Agent
        if 'policy_agent' in self.components:
            try:
                agent = self.components['policy_agent']()
                result = await agent.analyze_policy_compliance({})
                new_feature_results['policy_agent'] = 'analysis_id' in result
                print(f"   ✅ Policy Analysis: {'Success' if new_feature_results['policy_agent'] else 'Error'}")
            except Exception as e:
                new_feature_results['policy_agent'] = False
                print(f"   ❌ Policy Analysis: {e}")
        
        # Test Innovation Agent
        if 'innovation_agent' in self.components:
            try:
                agent = self.components['innovation_agent']()
                result = await agent.identify_innovation_opportunities({})
                new_feature_results['innovation_agent'] = 'analysis_id' in result
                print(f"   ✅ Innovation Agent: {'Success' if new_feature_results['innovation_agent'] else 'Error'}")
            except Exception as e:
                new_feature_results['innovation_agent'] = False
                print(f"   ❌ Innovation Agent: {e}")
        
        # Test Advanced Analytics Engine
        if 'analytics_engine' in self.components:
            try:
                engine = self.components['analytics_engine']()
                result = await engine.generate_predictive_analytics({})
                new_feature_results['analytics_engine'] = 'analysis_id' in result
                print(f"   ✅ Advanced Analytics: {'Success' if new_feature_results['analytics_engine'] else 'Error'}")
            except Exception as e:
                new_feature_results['analytics_engine'] = False
                print(f"   ❌ Advanced Analytics: {e}")
        
        # Test Blockchain Integration
        if 'blockchain_system' in self.components:
            try:
                blockchain = self.components['blockchain_system']()
                result = await blockchain.implement_blockchain_system()
                new_feature_results['blockchain_system'] = 'system_id' in result
                print(f"   ✅ Blockchain Integration: {'Success' if new_feature_results['blockchain_system'] else 'Error'}")
            except Exception as e:
                new_feature_results['blockchain_system'] = False
                print(f"   ❌ Blockchain Integration: {e}")
        
        # Test AR/VR Experiences
        if 'ar_vr_suite' in self.components:
            try:
                ar_vr = self.components['ar_vr_suite']()
                result = await ar_vr.develop_ar_vr_experiences()
                new_feature_results['ar_vr_suite'] = 'suite_id' in result
                print(f"   ✅ AR/VR Experiences: {'Success' if new_feature_results['ar_vr_suite'] else 'Error'}")
            except Exception as e:
                new_feature_results['ar_vr_suite'] = False
                print(f"   ❌ AR/VR Experiences: {e}")
        
        # Test IoT Sensor Networks
        if 'iot_network' in self.components:
            try:
                iot = self.components['iot_network']()
                result = await iot.deploy_iot_network(test_area)
                new_feature_results['iot_network'] = 'deployment_id' in result
                print(f"   ✅ IoT Sensor Networks: {'Success' if new_feature_results['iot_network'] else 'Error'}")
            except Exception as e:
                new_feature_results['iot_network'] = False
                print(f"   ❌ IoT Sensor Networks: {e}")
        
        # Test Global Scaling
        if 'global_scaling' in self.components:
            try:
                scaling = self.components['global_scaling']()
                result = await scaling.implement_global_scaling()
                new_feature_results['global_scaling'] = 'strategy_id' in result
                print(f"   ✅ Global Scaling: {'Success' if new_feature_results['global_scaling'] else 'Error'}")
            except Exception as e:
                new_feature_results['global_scaling'] = False
                print(f"   ❌ Global Scaling: {e}")
        
        success_rate = sum(new_feature_results.values()) / len(new_feature_results) if new_feature_results else 0
        print(f"\n📊 New Features: {sum(new_feature_results.values())}/{len(new_feature_results)} passed ({success_rate:.1%})")
        
        return new_feature_results
    
    async def test_unified_platform(self) -> Dict[str, bool]:
        """Test unified platform integration"""
        print("\n🔄 Testing Unified Platform")
        print("=" * 40)
        
        platform_results = {}
        test_area = self.test_areas['taiwan_strait']
        
        # Test Unified Platform Operation
        if 'unified_platform' in self.components:
            try:
                platform = self.components['unified_platform']()
                result = await platform.execute_integrated_operation(
                    area_bbox=test_area,
                    operation_type="comprehensive_test"
                )
                platform_results['unified_operation'] = hasattr(result, 'operation_id')
                print(f"   ✅ Unified Operation: {'Success' if platform_results['unified_operation'] else 'Error'}")
                
                if platform_results['unified_operation']:
                    print(f"      Operation ID: {result.operation_id}")
                    print(f"      Health Score: {result.overall_health_score:.2f}")
                    print(f"      Processing Time: {result.processing_time_seconds:.2f}s")
                    print(f"      Recommendations: {len(result.recommendations)}")
                
            except Exception as e:
                platform_results['unified_operation'] = False
                print(f"   ❌ Unified Operation: {e}")
        
        # Test Multi-Area Operations
        if 'unified_platform' in self.components:
            try:
                platform = self.components['unified_platform']()
                
                # Test multiple areas
                multi_area_results = []
                for area_name, area_coords in self.test_areas.items():
                    try:
                        result = await platform.execute_integrated_operation(
                            area_bbox=area_coords,
                            operation_type=f"multi_area_{area_name}"
                        )
                        multi_area_results.append(hasattr(result, 'operation_id'))
                    except Exception:
                        multi_area_results.append(False)
                
                platform_results['multi_area_operations'] = sum(multi_area_results) >= 2  # At least 2 should succeed
                print(f"   ✅ Multi-Area Operations: {sum(multi_area_results)}/{len(multi_area_results)} areas successful")
                
            except Exception as e:
                platform_results['multi_area_operations'] = False
                print(f"   ❌ Multi-Area Operations: {e}")
        
        # Test Performance
        if 'unified_platform' in self.components:
            try:
                platform = self.components['unified_platform']()
                
                start_time = time.time()
                result = await platform.execute_integrated_operation(
                    area_bbox=test_area,
                    operation_type="performance_test"
                )
                end_time = time.time()
                
                processing_time = end_time - start_time
                platform_results['performance'] = processing_time < 60  # Should complete within 60 seconds
                print(f"   ✅ Performance Test: {processing_time:.2f}s ({'Success' if platform_results['performance'] else 'Too slow'})")
                
            except Exception as e:
                platform_results['performance'] = False
                print(f"   ❌ Performance Test: {e}")
        
        success_rate = sum(platform_results.values()) / len(platform_results) if platform_results else 0
        print(f"\n📊 Unified Platform: {sum(platform_results.values())}/{len(platform_results)} passed ({success_rate:.1%})")
        
        return platform_results
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive tests on all working features"""
        print("🧪 COMPREHENSIVE WORKING FEATURES TEST SUITE")
        print("=" * 70)
        print(f"🕒 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📦 Working Components: {len(self.components)}")
        
        overall_start_time = time.time()
        
        # Run all test categories
        test_results = {}
        
        if self.components:
            test_results['ai_agents'] = await self.test_ai_agents()
            test_results['ml_components'] = await self.test_ml_components()
            test_results['new_features'] = await self.test_new_features()
            test_results['unified_platform'] = await self.test_unified_platform()
        else:
            print("\n⚠️ No working components found - skipping tests")
            return {'status': 'no_components'}
        
        overall_end_time = time.time()
        total_test_time = overall_end_time - overall_start_time
        
        # Calculate overall results
        total_tests = 0
        total_passed = 0
        category_summaries = {}
        
        for category, results in test_results.items():
            if results:
                category_passed = sum(results.values())
                category_total = len(results)
                category_summaries[category] = {
                    'passed': category_passed,
                    'total': category_total,
                    'success_rate': category_passed / category_total if category_total > 0 else 0
                }
                total_tests += category_total
                total_passed += category_passed
        
        overall_success_rate = total_passed / total_tests if total_tests > 0 else 0
        
        # Print comprehensive summary
        print("\n" + "=" * 70)
        print("🏆 COMPREHENSIVE TEST RESULTS")
        print("=" * 70)
        
        for category, summary in category_summaries.items():
            category_name = category.replace('_', ' ').title()
            print(f"   {category_name}: {summary['passed']}/{summary['total']} ({summary['success_rate']:.1%})")
        
        print(f"\n📊 OVERALL RESULTS:")
        print(f"   Working Components: {len(self.components)}")
        print(f"   Total Tests: {total_tests}")
        print(f"   Tests Passed: {total_passed}")
        print(f"   Success Rate: {overall_success_rate:.1%}")
        print(f"   Test Duration: {total_test_time:.2f} seconds")
        
        # Determine platform status
        if overall_success_rate >= 0.9:
            status = "🎉 EXCELLENT - Platform fully operational"
            deployment_ready = True
        elif overall_success_rate >= 0.8:
            status = "✅ GOOD - Platform mostly operational"
            deployment_ready = True
        elif overall_success_rate >= 0.7:
            status = "⚠️ ACCEPTABLE - Platform needs minor fixes"
            deployment_ready = False
        else:
            status = "❌ NEEDS WORK - Platform requires attention"
            deployment_ready = False
        
        print(f"\n🎯 PLATFORM STATUS: {status}")
        
        if deployment_ready:
            print("\n🚀 DEPLOYMENT READINESS:")
            print("   ✅ Core functionality operational")
            print("   ✅ AI agents working")
            print("   ✅ New features integrated")
            print("   ✅ Platform integration successful")
            print("   ✅ Ready for production deployment")
        else:
            print("\n🔧 IMPROVEMENT AREAS:")
            failed_categories = [cat for cat, summary in category_summaries.items() if summary['success_rate'] < 0.8]
            for category in failed_categories:
                print(f"   • Improve {category.replace('_', ' ')}")
        
        return {
            'status': 'completed',
            'overall_success_rate': overall_success_rate,
            'total_tests': total_tests,
            'total_passed': total_passed,
            'test_duration': total_test_time,
            'working_components': len(self.components),
            'category_results': category_summaries,
            'deployment_ready': deployment_ready,
            'test_results': test_results
        }


async def run_working_features_tests():
    """Run tests on all working features"""
    test_suite = WorkingFeaturesTestSuite()
    return await test_suite.run_comprehensive_tests()


if __name__ == "__main__":
    # Run comprehensive test suite
    results = asyncio.run(run_working_features_tests())
    
    # Exit with appropriate code
    if results.get('deployment_ready', False):
        print("\n✅ All working features tested successfully - Platform ready!")
        exit(0)
    else:
        print("\n⚠️ Some features need attention")
        exit(1)
