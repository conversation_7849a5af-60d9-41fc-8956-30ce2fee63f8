"""
Test script for cache functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.utils.cache import init_cache, cache_get, cache_set, cache_delete, cache_exists, get_cache


async def test_cache_operations():
    """Test basic cache operations."""
    print("🧪 Testing Cache Operations...")
    
    try:
        # Initialize cache
        await init_cache()
        cache = await get_cache()
        
        print(f"✅ Cache initialized (Redis available: {cache.is_redis_available})")
        
        # Test set and get
        test_key = "test:key:1"
        test_value = {"message": "Hello, Cache!", "timestamp": "2024-01-01T00:00:00"}
        
        print("🔄 Testing set operation...")
        success = await cache_set(test_key, test_value, ttl=60)
        print(f"Set operation: {'✅ Success' if success else '❌ Failed'}")
        
        print("🔄 Testing get operation...")
        retrieved_value = await cache_get(test_key)
        print(f"Get operation: {'✅ Success' if retrieved_value == test_value else '❌ Failed'}")
        print(f"Retrieved value: {retrieved_value}")
        
        # Test exists
        print("🔄 Testing exists operation...")
        exists = await cache_exists(test_key)
        print(f"Exists operation: {'✅ Success' if exists else '❌ Failed'}")
        
        # Test delete
        print("🔄 Testing delete operation...")
        deleted = await cache_delete(test_key)
        print(f"Delete operation: {'✅ Success' if deleted else '❌ Failed'}")
        
        # Verify deletion
        exists_after_delete = await cache_exists(test_key)
        print(f"Exists after delete: {'✅ Correctly deleted' if not exists_after_delete else '❌ Still exists'}")
        
        # Test cache stats
        print("🔄 Testing cache stats...")
        stats = await cache.get_stats()
        print(f"Cache stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache test failed: {e}")
        return False


async def test_cache_decorator():
    """Test cache decorator functionality."""
    print("\n🧪 Testing Cache Decorator...")
    
    try:
        from src.utils.cache import cache_decorator
        
        @cache_decorator(ttl=30, key_prefix="test_func")
        async def expensive_function(x: int, y: int) -> dict:
            """Simulate an expensive function."""
            await asyncio.sleep(0.1)  # Simulate work
            return {"result": x + y, "computed": True}
        
        print("🔄 First call (should compute)...")
        start_time = asyncio.get_event_loop().time()
        result1 = await expensive_function(5, 3)
        time1 = asyncio.get_event_loop().time() - start_time
        print(f"Result: {result1}, Time: {time1:.3f}s")
        
        print("🔄 Second call (should use cache)...")
        start_time = asyncio.get_event_loop().time()
        result2 = await expensive_function(5, 3)
        time2 = asyncio.get_event_loop().time() - start_time
        print(f"Result: {result2}, Time: {time2:.3f}s")
        
        if result1 == result2 and time2 < time1:
            print("✅ Cache decorator working correctly")
            return True
        else:
            print("❌ Cache decorator not working as expected")
            return False
            
    except Exception as e:
        print(f"❌ Cache decorator test failed: {e}")
        return False


async def test_climate_data_caching():
    """Test caching with climate data simulation."""
    print("\n🧪 Testing Climate Data Caching...")
    
    try:
        # Simulate climate data
        climate_data = {
            "location": "New York",
            "timestamp": "2024-01-01T12:00:00",
            "temperature": 22.5,
            "humidity": 65,
            "precipitation": 0,
            "wind_speed": 12.3,
            "source": "test"
        }
        
        # Cache climate data
        cache_key = "climate:new_york:latest"
        print(f"🔄 Caching climate data for key: {cache_key}")
        
        success = await cache_set(cache_key, climate_data, ttl=300)  # 5 minutes
        print(f"Cache set: {'✅ Success' if success else '❌ Failed'}")
        
        # Retrieve climate data
        print("🔄 Retrieving cached climate data...")
        cached_data = await cache_get(cache_key)
        
        if cached_data == climate_data:
            print("✅ Climate data caching successful")
            print(f"Cached data: {cached_data}")
            return True
        else:
            print("❌ Climate data caching failed")
            print(f"Expected: {climate_data}")
            print(f"Got: {cached_data}")
            return False
            
    except Exception as e:
        print(f"❌ Climate data caching test failed: {e}")
        return False


async def main():
    """Run all cache tests."""
    print("🚀 Cache System Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Test 1: Basic cache operations
    basic_result = await test_cache_operations()
    test_results.append(("Basic Cache Operations", basic_result))
    
    # Test 2: Cache decorator
    decorator_result = await test_cache_decorator()
    test_results.append(("Cache Decorator", decorator_result))
    
    # Test 3: Climate data caching
    climate_result = await test_climate_data_caching()
    test_results.append(("Climate Data Caching", climate_result))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All cache tests passed!")
        print("Cache system is ready for use.")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed.")
        print("Please check the cache configuration.")
    
    # Cleanup
    try:
        from src.utils.cache import close_cache
        await close_cache()
        print("\n🔧 Cache connections closed")
    except Exception as e:
        print(f"⚠️ Error closing cache: {e}")


if __name__ == "__main__":
    asyncio.run(main())
