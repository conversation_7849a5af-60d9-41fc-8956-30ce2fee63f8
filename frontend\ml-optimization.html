<!-- Machine Learning & Optimization Page -->
<div class="page" id="ml-optimization-page">
    <div class="page-header">
        <h1><i class="fas fa-brain"></i> ML & Optimization</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Train Model
            </button>
            <button class="btn-secondary">
                <i class="fas fa-dna"></i> Run Genetic Algorithm
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Models
            </button>
        </div>
    </div>

    <!-- ML & Optimization Dashboard -->
    <div class="ml-optimization-dashboard">
        <!-- Neural Networks Section -->
        <div class="panel neural-networks-panel">
            <div class="panel-header">
                <h3>Neural Networks</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="train">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-icon" data-action="evaluate">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>
            </div>
            <div class="neural-networks-content">
                <div class="network-models">
                    <div class="model-card water-quality-nn">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="model-info">
                                <h4>Water Quality Prediction</h4>
                                <div class="model-type">Deep Neural Network</div>
                                <div class="model-status training">Training</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Accuracy</span>
                                <span class="metric-value">94.7%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Loss</span>
                                <span class="metric-value">0.023</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Epochs</span>
                                <span class="metric-value">847/1000</span>
                            </div>
                        </div>
                        <div class="model-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 84.7%"></div>
                            </div>
                            <span class="progress-text">84.7% Complete</span>
                        </div>
                        <div class="model-architecture">
                            <div class="layer">Input: 15 features</div>
                            <div class="layer">Hidden: 128 neurons</div>
                            <div class="layer">Hidden: 64 neurons</div>
                            <div class="layer">Output: 4 classes</div>
                        </div>
                    </div>

                    <div class="model-card energy-optimization-nn">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="model-info">
                                <h4>Energy Optimization</h4>
                                <div class="model-type">Recurrent Neural Network</div>
                                <div class="model-status deployed">Deployed</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Accuracy</span>
                                <span class="metric-value">91.2%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">RMSE</span>
                                <span class="metric-value">0.087</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Predictions</span>
                                <span class="metric-value">12,847</span>
                            </div>
                        </div>
                        <div class="model-performance">
                            <canvas id="energyModelPerformance"></canvas>
                        </div>
                        <div class="model-architecture">
                            <div class="layer">Input: 24 time steps</div>
                            <div class="layer">LSTM: 100 units</div>
                            <div class="layer">Dense: 50 neurons</div>
                            <div class="layer">Output: 1 value</div>
                        </div>
                    </div>

                    <div class="model-card predictive-maintenance-nn">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="model-info">
                                <h4>Predictive Maintenance</h4>
                                <div class="model-type">Convolutional Neural Network</div>
                                <div class="model-status deployed">Deployed</div>
                            </div>
                        </div>
                        <div class="model-metrics">
                            <div class="metric">
                                <span class="metric-label">Precision</span>
                                <span class="metric-value">89.3%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Recall</span>
                                <span class="metric-value">92.1%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">F1-Score</span>
                                <span class="metric-value">90.7%</span>
                            </div>
                        </div>
                        <div class="model-performance">
                            <canvas id="maintenanceModelPerformance"></canvas>
                        </div>
                        <div class="model-architecture">
                            <div class="layer">Input: 32x32 spectrograms</div>
                            <div class="layer">Conv2D: 32 filters</div>
                            <div class="layer">Conv2D: 64 filters</div>
                            <div class="layer">Dense: 128 neurons</div>
                            <div class="layer">Output: 3 classes</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Genetic Algorithm Optimization -->
        <div class="panel genetic-algorithm-panel">
            <div class="panel-header">
                <h3>Genetic Algorithm Optimization</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="start">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-icon" data-action="pause">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn-icon" data-action="reset">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </div>
            <div class="genetic-algorithm-content">
                <div class="algorithm-status">
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">Generation 247</div>
                            <div class="status-label">Current Generation</div>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">0.9847</div>
                            <div class="status-label">Best Fitness</div>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">0.7234</div>
                            <div class="status-label">Average Fitness</div>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">12.7s</div>
                            <div class="status-label">Generation Time</div>
                        </div>
                    </div>
                </div>

                <div class="algorithm-visualization">
                    <div class="fitness-evolution">
                        <h4>Fitness Evolution</h4>
                        <canvas id="fitnessEvolutionChart"></canvas>
                    </div>
                    <div class="population-diversity">
                        <h4>Population Diversity</h4>
                        <canvas id="populationDiversityChart"></canvas>
                    </div>
                </div>

                <div class="algorithm-parameters">
                    <h4>Algorithm Parameters</h4>
                    <div class="parameters-grid">
                        <div class="parameter-item">
                            <label>Population Size</label>
                            <input type="number" value="100" min="10" max="1000">
                        </div>
                        <div class="parameter-item">
                            <label>Mutation Rate</label>
                            <input type="number" value="0.01" min="0" max="1" step="0.01">
                        </div>
                        <div class="parameter-item">
                            <label>Crossover Rate</label>
                            <input type="number" value="0.8" min="0" max="1" step="0.1">
                        </div>
                        <div class="parameter-item">
                            <label>Elite Size</label>
                            <input type="number" value="10" min="1" max="50">
                        </div>
                        <div class="parameter-item">
                            <label>Max Generations</label>
                            <input type="number" value="1000" min="10" max="10000">
                        </div>
                        <div class="parameter-item">
                            <label>Selection Method</label>
                            <select>
                                <option value="tournament">Tournament</option>
                                <option value="roulette">Roulette Wheel</option>
                                <option value="rank">Rank Selection</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="best-solutions">
                    <h4>Best Solutions</h4>
                    <div class="solutions-list">
                        <div class="solution-item">
                            <div class="solution-rank">1</div>
                            <div class="solution-info">
                                <div class="solution-fitness">Fitness: 0.9847</div>
                                <div class="solution-parameters">
                                    Flow Rate: 2847 L/min, Chemical Dosing: 12.3 mg/L, Energy: 847 kWh
                                </div>
                            </div>
                            <div class="solution-actions">
                                <button class="btn-solution">Apply</button>
                                <button class="btn-solution">Analyze</button>
                            </div>
                        </div>
                        <div class="solution-item">
                            <div class="solution-rank">2</div>
                            <div class="solution-info">
                                <div class="solution-fitness">Fitness: 0.9823</div>
                                <div class="solution-parameters">
                                    Flow Rate: 2834 L/min, Chemical Dosing: 11.8 mg/L, Energy: 852 kWh
                                </div>
                            </div>
                            <div class="solution-actions">
                                <button class="btn-solution">Apply</button>
                                <button class="btn-solution">Analyze</button>
                            </div>
                        </div>
                        <div class="solution-item">
                            <div class="solution-rank">3</div>
                            <div class="solution-info">
                                <div class="solution-fitness">Fitness: 0.9801</div>
                                <div class="solution-parameters">
                                    Flow Rate: 2856 L/min, Chemical Dosing: 12.7 mg/L, Energy: 839 kWh
                                </div>
                            </div>
                            <div class="solution-actions">
                                <button class="btn-solution">Apply</button>
                                <button class="btn-solution">Analyze</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Performance Comparison -->
        <div class="panel model-comparison-panel">
            <div class="panel-header">
                <h3>Model Performance Comparison</h3>
                <div class="panel-controls">
                    <select class="metric-select">
                        <option value="accuracy">Accuracy</option>
                        <option value="precision">Precision</option>
                        <option value="recall">Recall</option>
                        <option value="f1">F1-Score</option>
                    </select>
                </div>
            </div>
            <div class="model-comparison-content">
                <div class="comparison-chart">
                    <canvas id="modelComparisonChart"></canvas>
                </div>
                <div class="model-rankings">
                    <h4>Model Rankings</h4>
                    <div class="ranking-list">
                        <div class="ranking-item">
                            <div class="ranking-position">1</div>
                            <div class="ranking-model">Water Quality Prediction</div>
                            <div class="ranking-score">94.7%</div>
                            <div class="ranking-trend positive">+2.3%</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-position">2</div>
                            <div class="ranking-model">Predictive Maintenance</div>
                            <div class="ranking-score">90.7%</div>
                            <div class="ranking-trend positive">+1.8%</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-position">3</div>
                            <div class="ranking-model">Energy Optimization</div>
                            <div class="ranking-score">91.2%</div>
                            <div class="ranking-trend negative">-0.5%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AutoML Pipeline -->
        <div class="panel automl-panel">
            <div class="panel-header">
                <h3>AutoML Pipeline</h3>
                <div class="automl-status">
                    <span class="status-indicator active">
                        <i class="fas fa-magic"></i>
                        <span>AutoML Active</span>
                    </span>
                </div>
            </div>
            <div class="automl-content">
                <div class="pipeline-stages">
                    <div class="stage completed">
                        <div class="stage-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stage-info">
                            <div class="stage-title">Data Preprocessing</div>
                            <div class="stage-status">Completed</div>
                        </div>
                    </div>
                    <div class="stage completed">
                        <div class="stage-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stage-info">
                            <div class="stage-title">Feature Engineering</div>
                            <div class="stage-status">Completed</div>
                        </div>
                    </div>
                    <div class="stage active">
                        <div class="stage-icon">
                            <i class="fas fa-cog fa-spin"></i>
                        </div>
                        <div class="stage-info">
                            <div class="stage-title">Model Selection</div>
                            <div class="stage-status">In Progress</div>
                        </div>
                    </div>
                    <div class="stage pending">
                        <div class="stage-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stage-info">
                            <div class="stage-title">Hyperparameter Tuning</div>
                            <div class="stage-status">Pending</div>
                        </div>
                    </div>
                    <div class="stage pending">
                        <div class="stage-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stage-info">
                            <div class="stage-title">Model Validation</div>
                            <div class="stage-status">Pending</div>
                        </div>
                    </div>
                </div>

                <div class="automl-results">
                    <h4>Current Best Models</h4>
                    <div class="automl-models">
                        <div class="automl-model">
                            <div class="model-name">Random Forest</div>
                            <div class="model-score">0.9234</div>
                            <div class="model-params">n_estimators: 100, max_depth: 15</div>
                        </div>
                        <div class="automl-model">
                            <div class="model-name">XGBoost</div>
                            <div class="model-score">0.9187</div>
                            <div class="model-params">learning_rate: 0.1, n_estimators: 200</div>
                        </div>
                        <div class="automl-model">
                            <div class="model-name">Neural Network</div>
                            <div class="model-score">0.9156</div>
                            <div class="model-params">layers: [128, 64, 32], dropout: 0.2</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
