<!-- Digital Twin Dashboard Page -->
<div class="page" id="digital-twin-dashboard-page">
    <div class="page-header">
        <h1><i class="fas fa-cube"></i> Digital Twin Dashboard</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Create Twin
            </button>
            <button class="btn-secondary">
                <i class="fas fa-sync"></i> Sync All
            </button>
            <button class="btn-secondary">
                <i class="fas fa-play"></i> Run Simulation
            </button>
        </div>
    </div>

    <!-- Digital Twin Dashboard -->
    <div class="digital-twin-dashboard">
        <!-- Twin Overview -->
        <div class="twin-overview">
            <div class="twin-card treatment-plant">
                <div class="twin-header">
                    <div class="twin-icon">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="twin-info">
                        <h3>Treatment Plant Digital Twin</h3>
                        <div class="twin-status active">Active</div>
                    </div>
                    <div class="sync-indicator">
                        <i class="fas fa-sync"></i>
                        <span>99.7% Sync</span>
                    </div>
                </div>
                <div class="twin-metrics">
                    <div class="metric">
                        <span class="metric-label">Real-time Accuracy</span>
                        <span class="metric-value">99.7%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Prediction Accuracy</span>
                        <span class="metric-value">94.2%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Last Update</span>
                        <span class="metric-value">2s ago</span>
                    </div>
                </div>
            </div>

            <div class="twin-card energy-grid">
                <div class="twin-header">
                    <div class="twin-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="twin-info">
                        <h3>Energy Grid Digital Twin</h3>
                        <div class="twin-status active">Active</div>
                    </div>
                    <div class="sync-indicator">
                        <i class="fas fa-sync"></i>
                        <span>98.9% Sync</span>
                    </div>
                </div>
                <div class="twin-metrics">
                    <div class="metric">
                        <span class="metric-label">Real-time Accuracy</span>
                        <span class="metric-value">98.9%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Prediction Accuracy</span>
                        <span class="metric-value">91.8%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Last Update</span>
                        <span class="metric-value">1s ago</span>
                    </div>
                </div>
            </div>

            <div class="twin-card sensor-network">
                <div class="twin-header">
                    <div class="twin-icon">
                        <i class="fas fa-satellite-dish"></i>
                    </div>
                    <div class="twin-info">
                        <h3>Sensor Network Digital Twin</h3>
                        <div class="twin-status active">Active</div>
                    </div>
                    <div class="sync-indicator">
                        <i class="fas fa-sync"></i>
                        <span>97.3% Sync</span>
                    </div>
                </div>
                <div class="twin-metrics">
                    <div class="metric">
                        <span class="metric-label">Real-time Accuracy</span>
                        <span class="metric-value">97.3%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Prediction Accuracy</span>
                        <span class="metric-value">89.4%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Last Update</span>
                        <span class="metric-value">3s ago</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3D Visualization Panel -->
        <div class="panel twin-visualization-panel">
            <div class="panel-header">
                <h3>3D Digital Twin Visualization</h3>
                <div class="panel-controls">
                    <select class="twin-select">
                        <option value="treatment-plant">Treatment Plant</option>
                        <option value="energy-grid">Energy Grid</option>
                        <option value="sensor-network">Sensor Network</option>
                        <option value="full-system">Full System</option>
                    </select>
                    <button class="btn-icon" data-action="fullscreen">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="btn-icon" data-action="reset-view">
                        <i class="fas fa-home"></i>
                    </button>
                </div>
            </div>
            <div class="twin-visualization-content">
                <div class="visualization-container">
                    <canvas id="digitalTwin3D" width="800" height="500"></canvas>
                    <div class="visualization-overlay">
                        <div class="overlay-info">
                            <div class="info-item">
                                <span class="info-label">Selected Component:</span>
                                <span class="info-value">Primary Clarifier</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Status:</span>
                                <span class="info-value status-active">Operational</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Efficiency:</span>
                                <span class="info-value">94.7%</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Temperature:</span>
                                <span class="info-value">23.4°C</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="visualization-controls">
                    <div class="control-group">
                        <label>View Mode</label>
                        <select class="view-mode-select">
                            <option value="realistic">Realistic</option>
                            <option value="schematic">Schematic</option>
                            <option value="thermal">Thermal</option>
                            <option value="flow">Flow Visualization</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>Time Scale</label>
                        <select class="time-scale-select">
                            <option value="realtime">Real-time</option>
                            <option value="accelerated">Accelerated (10x)</option>
                            <option value="historical">Historical</option>
                            <option value="prediction">Prediction</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>Data Layers</label>
                        <div class="layer-toggles">
                            <label class="layer-toggle">
                                <input type="checkbox" checked>
                                <span>Temperature</span>
                            </label>
                            <label class="layer-toggle">
                                <input type="checkbox" checked>
                                <span>Flow Rates</span>
                            </label>
                            <label class="layer-toggle">
                                <input type="checkbox">
                                <span>Pressure</span>
                            </label>
                            <label class="layer-toggle">
                                <input type="checkbox">
                                <span>Chemical Levels</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Simulation Control Panel -->
        <div class="panel simulation-control-panel">
            <div class="panel-header">
                <h3>Simulation Control</h3>
                <div class="simulation-status">
                    <span class="status-indicator running">
                        <i class="fas fa-play"></i>
                        <span>Simulation Running</span>
                    </span>
                </div>
            </div>
            <div class="simulation-control-content">
                <div class="simulation-scenarios">
                    <h4>Scenario Selection</h4>
                    <div class="scenario-list">
                        <div class="scenario-item active">
                            <div class="scenario-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="scenario-info">
                                <div class="scenario-name">Equipment Failure Simulation</div>
                                <div class="scenario-description">Simulate pump failure in Treatment Plant A</div>
                            </div>
                            <div class="scenario-status running">Running</div>
                        </div>

                        <div class="scenario-item">
                            <div class="scenario-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="scenario-info">
                                <div class="scenario-name">Demand Surge Simulation</div>
                                <div class="scenario-description">Test system response to 150% demand increase</div>
                            </div>
                            <div class="scenario-status ready">Ready</div>
                        </div>

                        <div class="scenario-item">
                            <div class="scenario-icon">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div class="scenario-info">
                                <div class="scenario-name">Optimization Scenario</div>
                                <div class="scenario-description">Test AI-driven efficiency improvements</div>
                            </div>
                            <div class="scenario-status ready">Ready</div>
                        </div>
                    </div>
                </div>

                <div class="simulation-parameters">
                    <h4>Simulation Parameters</h4>
                    <div class="parameter-controls">
                        <div class="parameter-group">
                            <label>Simulation Speed</label>
                            <input type="range" min="1" max="100" value="10" class="speed-slider">
                            <span class="speed-value">10x</span>
                        </div>
                        <div class="parameter-group">
                            <label>Duration</label>
                            <select class="duration-select">
                                <option value="1h">1 Hour</option>
                                <option value="24h">24 Hours</option>
                                <option value="7d">7 Days</option>
                                <option value="30d">30 Days</option>
                            </select>
                        </div>
                        <div class="parameter-group">
                            <label>Failure Probability</label>
                            <input type="range" min="0" max="100" value="5" class="failure-slider">
                            <span class="failure-value">5%</span>
                        </div>
                    </div>
                </div>

                <div class="simulation-controls">
                    <button class="btn-simulation primary">
                        <i class="fas fa-play"></i>
                        Start Simulation
                    </button>
                    <button class="btn-simulation">
                        <i class="fas fa-pause"></i>
                        Pause
                    </button>
                    <button class="btn-simulation">
                        <i class="fas fa-stop"></i>
                        Stop
                    </button>
                    <button class="btn-simulation">
                        <i class="fas fa-redo"></i>
                        Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Real-time Sync Status -->
        <div class="panel sync-status-panel">
            <div class="panel-header">
                <h3>Real-time Synchronization</h3>
                <div class="sync-health">
                    <span class="health-indicator excellent">
                        <i class="fas fa-heartbeat"></i>
                        <span>Excellent</span>
                    </span>
                </div>
            </div>
            <div class="sync-status-content">
                <div class="sync-metrics">
                    <div class="sync-metric">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">1.2ms</div>
                            <div class="metric-label">Avg Latency</div>
                        </div>
                    </div>
                    <div class="sync-metric">
                        <div class="metric-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">2,847</div>
                            <div class="metric-label">Data Points/sec</div>
                        </div>
                    </div>
                    <div class="sync-metric">
                        <div class="metric-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">99.97%</div>
                            <div class="metric-label">Sync Accuracy</div>
                        </div>
                    </div>
                    <div class="sync-metric">
                        <div class="metric-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">847 GB</div>
                            <div class="metric-label">Data Volume</div>
                        </div>
                    </div>
                </div>

                <div class="data-sources">
                    <h4>Data Sources</h4>
                    <div class="source-list">
                        <div class="source-item">
                            <div class="source-icon">
                                <i class="fas fa-satellite-dish"></i>
                            </div>
                            <div class="source-info">
                                <div class="source-name">IoT Sensors</div>
                                <div class="source-count">1,247 sensors</div>
                            </div>
                            <div class="source-status">
                                <div class="status-indicator online">Online</div>
                                <div class="data-rate">847 msg/sec</div>
                            </div>
                        </div>

                        <div class="source-item">
                            <div class="source-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="source-info">
                                <div class="source-name">Control Systems</div>
                                <div class="source-count">23 systems</div>
                            </div>
                            <div class="source-status">
                                <div class="status-indicator online">Online</div>
                                <div class="data-rate">156 msg/sec</div>
                            </div>
                        </div>

                        <div class="source-item">
                            <div class="source-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="source-info">
                                <div class="source-name">Analytics Engine</div>
                                <div class="source-count">5 models</div>
                            </div>
                            <div class="source-status">
                                <div class="status-indicator online">Online</div>
                                <div class="data-rate">89 msg/sec</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="sync-timeline">
                    <h4>Synchronization Timeline</h4>
                    <canvas id="syncTimelineChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Predictive Analytics -->
        <div class="panel predictive-analytics-panel">
            <div class="panel-header">
                <h3>Predictive Analytics</h3>
                <div class="panel-controls">
                    <select class="prediction-horizon">
                        <option value="1h">Next Hour</option>
                        <option value="24h">Next 24 Hours</option>
                        <option value="7d">Next 7 Days</option>
                        <option value="30d">Next 30 Days</option>
                    </select>
                </div>
            </div>
            <div class="predictive-analytics-content">
                <div class="prediction-cards">
                    <div class="prediction-card maintenance">
                        <div class="prediction-header">
                            <div class="prediction-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="prediction-info">
                                <h4>Maintenance Prediction</h4>
                                <div class="prediction-confidence">Confidence: 94.7%</div>
                            </div>
                        </div>
                        <div class="prediction-content">
                            <div class="prediction-alert warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Pump 3 requires maintenance in 5 days</span>
                            </div>
                            <div class="prediction-details">
                                <div class="detail-item">
                                    <span class="detail-label">Failure Probability:</span>
                                    <span class="detail-value">23%</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Recommended Action:</span>
                                    <span class="detail-value">Schedule Maintenance</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="prediction-card performance">
                        <div class="prediction-header">
                            <div class="prediction-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="prediction-info">
                                <h4>Performance Forecast</h4>
                                <div class="prediction-confidence">Confidence: 91.2%</div>
                            </div>
                        </div>
                        <div class="prediction-content">
                            <div class="prediction-alert info">
                                <i class="fas fa-info-circle"></i>
                                <span>Efficiency will increase by 3.2% next week</span>
                            </div>
                            <div class="prediction-details">
                                <div class="detail-item">
                                    <span class="detail-label">Expected Efficiency:</span>
                                    <span class="detail-value">97.9%</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Contributing Factor:</span>
                                    <span class="detail-value">Weather Improvement</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="prediction-charts">
                    <div class="chart-container">
                        <h4>Performance Prediction</h4>
                        <canvas id="performancePredictionChart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h4>Maintenance Schedule</h4>
                        <canvas id="maintenanceScheduleChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
