#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Test Cases for All Marine Conservation Features
Quick and focused tests for all platform components
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

print("🧪 SIMPLE FEATURE TESTS - MARINE CONSERVATION PLATFORM")
print("=" * 60)

# Test configuration
TEST_AREA = (119.0, 23.0, 121.0, 25.0)  # Taiwan Strait
TEST_TIMEOUT = 30  # seconds per test

class SimpleFeatureTests:
    """Simple test cases for all features"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
    
    def test_result(self, component_name: str, success: bool, message: str = ""):
        """Record test result"""
        self.results[component_name] = {
            'success': success,
            'message': message,
            'timestamp': datetime.now()
        }
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {component_name}: {message}")
    
    async def test_ai_agents(self):
        """Test all AI agents with simple calls"""
        print("\n🤖 Testing AI Agents")
        print("-" * 30)
        
        # Test Climate Agent
        try:
            from marine_conservation.agents.climate_marine_agent import ClimateMarineAgent
            agent = ClimateMarineAgent()
            result = await asyncio.wait_for(
                agent.generate_climate_report(TEST_AREA), 
                timeout=TEST_TIMEOUT
            )
            self.test_result("Climate Agent", result is not None, f"Generated report with ID: {getattr(result, 'report_id', 'N/A')}")
        except Exception as e:
            self.test_result("Climate Agent", False, str(e))
        
        # Test Water Treatment Agent
        try:
            from marine_conservation.agents.water_treatment_marine_agent import WaterTreatmentMarineAgent
            agent = WaterTreatmentMarineAgent()
            result = await asyncio.wait_for(
                agent.optimize_water_treatment(TEST_AREA), 
                timeout=TEST_TIMEOUT
            )
            self.test_result("Water Treatment Agent", result is not None, f"Optimization ID: {getattr(result, 'optimization_id', 'N/A')}")
        except Exception as e:
            self.test_result("Water Treatment Agent", False, str(e))
        
        # Test Energy Efficiency Agent
        try:
            from marine_conservation.agents.energy_efficiency_marine_agent import EnergyEfficiencyMarineAgent
            agent = EnergyEfficiencyMarineAgent()
            result = await asyncio.wait_for(
                agent.optimize_energy_systems(TEST_AREA), 
                timeout=TEST_TIMEOUT
            )
            self.test_result("Energy Efficiency Agent", result is not None, f"Analysis ID: {getattr(result, 'analysis_id', 'N/A')}")
        except Exception as e:
            self.test_result("Energy Efficiency Agent", False, str(e))
        
        # Test Sustainability Agent
        try:
            from marine_conservation.agents.sustainability_marine_agent import SustainabilityMarineAgent
            agent = SustainabilityMarineAgent()
            result = await asyncio.wait_for(
                agent.assess_marine_ecosystem(TEST_AREA), 
                timeout=TEST_TIMEOUT
            )
            self.test_result("Sustainability Agent", result is not None, f"Assessment ID: {getattr(result, 'assessment_id', 'N/A')}")
        except Exception as e:
            self.test_result("Sustainability Agent", False, str(e))
        
        # Test Risk Analysis Agent
        try:
            from marine_conservation.agents.risk_analysis_marine_agent import RiskAnalysisMarineAgent
            agent = RiskAnalysisMarineAgent()
            result = await asyncio.wait_for(
                agent.assess_marine_conservation_risks(TEST_AREA), 
                timeout=TEST_TIMEOUT
            )
            self.test_result("Risk Analysis Agent", result is not None, f"Risk Level: {getattr(result, 'risk_level', 'N/A')}")
        except Exception as e:
            self.test_result("Risk Analysis Agent", False, str(e))
    
    async def test_ml_components(self):
        """Test ML components"""
        print("\n🔬 Testing ML Components")
        print("-" * 30)
        
        # Test ML Debris Categorizer
        try:
            from marine_conservation.ml_models.debris_categorization import MLDebrisCategorizer
            categorizer = MLDebrisCategorizer()
            
            # Check if category_definitions exists
            has_categories = hasattr(categorizer, 'category_definitions')
            self.test_result("ML Categorizer - Categories", has_categories, f"Categories defined: {has_categories}")
            
            if has_categories:
                result = await asyncio.wait_for(
                    categorizer.classify_debris_in_area(TEST_AREA), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("ML Categorizer - Classification", isinstance(result, list), f"Classified {len(result) if result else 0} items")
            
        except Exception as e:
            self.test_result("ML Debris Categorizer", False, str(e))
        
        # Test AI Recycling Optimizer
        try:
            from marine_conservation.recycling.ai_recycling_optimizer import AIRecyclingOptimizer
            optimizer = AIRecyclingOptimizer()
            result = await asyncio.wait_for(
                optimizer.optimize_recycling_pathways([]), 
                timeout=TEST_TIMEOUT
            )
            self.test_result("AI Recycling Optimizer", result is not None, "Optimization completed")
        except Exception as e:
            self.test_result("AI Recycling Optimizer", False, str(e))
    
    async def test_new_features(self):
        """Test new rapid implementation features"""
        print("\n✨ Testing New Features")
        print("-" * 30)
        
        try:
            from marine_conservation.rapid_implementation.all_remaining_tasks import (
                CommunityEngagementAgent,
                PolicyAnalysisAgent,
                InnovationAgent,
                AdvancedAnalyticsEngine,
                BlockchainIntegration,
                ARVRExperiences,
                IoTSensorNetworks,
                GlobalScaling
            )
            
            # Test Community Engagement
            try:
                agent = CommunityEngagementAgent()
                result = await asyncio.wait_for(
                    agent.create_engagement_campaign(TEST_AREA), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("Community Engagement", 'campaign_id' in result, f"Campaign ID: {result.get('campaign_id', 'N/A')}")
            except Exception as e:
                self.test_result("Community Engagement", False, str(e))
            
            # Test Policy Analysis
            try:
                agent = PolicyAnalysisAgent()
                result = await asyncio.wait_for(
                    agent.analyze_policy_compliance({}), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("Policy Analysis", 'analysis_id' in result, f"Analysis ID: {result.get('analysis_id', 'N/A')}")
            except Exception as e:
                self.test_result("Policy Analysis", False, str(e))
            
            # Test Innovation Agent
            try:
                agent = InnovationAgent()
                result = await asyncio.wait_for(
                    agent.identify_innovation_opportunities({}), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("Innovation Agent", 'analysis_id' in result, f"Analysis ID: {result.get('analysis_id', 'N/A')}")
            except Exception as e:
                self.test_result("Innovation Agent", False, str(e))
            
            # Test Advanced Analytics
            try:
                engine = AdvancedAnalyticsEngine()
                result = await asyncio.wait_for(
                    engine.generate_predictive_analytics({}), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("Advanced Analytics", 'analysis_id' in result, f"Analysis ID: {result.get('analysis_id', 'N/A')}")
            except Exception as e:
                self.test_result("Advanced Analytics", False, str(e))
            
            # Test Blockchain Integration
            try:
                blockchain = BlockchainIntegration()
                result = await asyncio.wait_for(
                    blockchain.implement_blockchain_system(), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("Blockchain Integration", 'system_id' in result, f"System ID: {result.get('system_id', 'N/A')}")
            except Exception as e:
                self.test_result("Blockchain Integration", False, str(e))
            
            # Test AR/VR Experiences
            try:
                ar_vr = ARVRExperiences()
                result = await asyncio.wait_for(
                    ar_vr.develop_ar_vr_experiences(), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("AR/VR Experiences", 'suite_id' in result, f"Suite ID: {result.get('suite_id', 'N/A')}")
            except Exception as e:
                self.test_result("AR/VR Experiences", False, str(e))
            
            # Test IoT Sensor Networks
            try:
                iot = IoTSensorNetworks()
                result = await asyncio.wait_for(
                    iot.deploy_iot_network(TEST_AREA), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("IoT Sensor Networks", 'deployment_id' in result, f"Deployment ID: {result.get('deployment_id', 'N/A')}")
            except Exception as e:
                self.test_result("IoT Sensor Networks", False, str(e))
            
            # Test Global Scaling
            try:
                scaling = GlobalScaling()
                result = await asyncio.wait_for(
                    scaling.implement_global_scaling(), 
                    timeout=TEST_TIMEOUT
                )
                self.test_result("Global Scaling", 'strategy_id' in result, f"Strategy ID: {result.get('strategy_id', 'N/A')}")
            except Exception as e:
                self.test_result("Global Scaling", False, str(e))
                
        except ImportError as e:
            self.test_result("New Features Import", False, f"Import error: {e}")
    
    async def test_unified_platform(self):
        """Test unified platform integration"""
        print("\n🔄 Testing Unified Platform")
        print("-" * 30)
        
        try:
            from marine_conservation.integrated_platform.simplified_unified_platform import SimplifiedUnifiedPlatform
            
            platform = SimplifiedUnifiedPlatform()
            result = await asyncio.wait_for(
                platform.execute_integrated_operation(
                    area_bbox=TEST_AREA,
                    operation_type="simple_test"
                ), 
                timeout=TEST_TIMEOUT * 2  # Give more time for integration
            )
            
            success = hasattr(result, 'operation_id')
            message = f"Operation ID: {getattr(result, 'operation_id', 'N/A')}"
            if success:
                message += f", Health Score: {getattr(result, 'overall_health_score', 'N/A'):.2f}"
            
            self.test_result("Unified Platform", success, message)
            
        except Exception as e:
            self.test_result("Unified Platform", False, str(e))
    
    def print_summary(self):
        """Print test summary"""
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 60)
        print("🏆 SIMPLE TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for r in self.results.values() if r['success'])
        total = len(self.results)
        success_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"📊 Overall Results:")
        print(f"   Tests Passed: {passed}/{total}")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Total Time: {total_time:.1f} seconds")
        
        # Group results by category
        categories = {
            'AI Agents': [k for k in self.results.keys() if 'Agent' in k and 'Community' not in k and 'Policy' not in k and 'Innovation' not in k],
            'ML Components': [k for k in self.results.keys() if 'ML' in k or 'Recycling' in k],
            'New Features': [k for k in self.results.keys() if any(x in k for x in ['Community', 'Policy', 'Innovation', 'Analytics', 'Blockchain', 'AR/VR', 'IoT', 'Global'])],
            'Platform': [k for k in self.results.keys() if 'Platform' in k]
        }
        
        print(f"\n📋 Results by Category:")
        for category, components in categories.items():
            if components:
                cat_passed = sum(1 for c in components if self.results[c]['success'])
                cat_total = len(components)
                cat_rate = (cat_passed / cat_total * 100) if cat_total > 0 else 0
                status = "✅" if cat_rate >= 80 else "⚠️" if cat_rate >= 60 else "❌"
                print(f"   {status} {category}: {cat_passed}/{cat_total} ({cat_rate:.0f}%)")
        
        print(f"\n🔍 Failed Tests:")
        failed_tests = [k for k, v in self.results.items() if not v['success']]
        if failed_tests:
            for test in failed_tests:
                print(f"   ❌ {test}: {self.results[test]['message']}")
        else:
            print("   🎉 No failed tests!")
        
        # Deployment readiness
        if success_rate >= 90:
            status = "🚀 READY FOR DEPLOYMENT"
        elif success_rate >= 80:
            status = "✅ MOSTLY READY - Minor fixes needed"
        elif success_rate >= 70:
            status = "⚠️ NEEDS ATTENTION - Some fixes required"
        else:
            status = "❌ NOT READY - Major fixes needed"
        
        print(f"\n🎯 Platform Status: {status}")
        print("=" * 60)


async def run_simple_tests():
    """Run all simple feature tests"""
    tester = SimpleFeatureTests()
    
    print(f"🕒 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Test Area: Taiwan Strait {TEST_AREA}")
    print(f"⏱️ Timeout per test: {TEST_TIMEOUT} seconds")
    
    # Run all test categories
    await tester.test_ai_agents()
    await tester.test_ml_components()
    await tester.test_new_features()
    await tester.test_unified_platform()
    
    # Print summary
    tester.print_summary()
    
    return tester.results


if __name__ == "__main__":
    # Run simple tests
    results = asyncio.run(run_simple_tests())
    
    # Exit with appropriate code
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    success_rate = (passed / total) if total > 0 else 0
    
    if success_rate >= 0.8:
        print("\n✅ Tests completed successfully!")
        exit(0)
    else:
        print(f"\n⚠️ Some tests failed ({success_rate:.1%} success rate)")
        exit(1)
