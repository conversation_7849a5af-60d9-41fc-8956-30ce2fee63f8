#!/usr/bin/env node
/**
 * Water Management System Startup Script
 * Starts integrated server with both frontend and backend on single port
 */

const { spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const net = require('net');

console.log('🌊 Water Management System - Integrated Startup Script');
console.log('=====================================================\n');

// Configuration - Using integrated server approach
const config = {
    integrated: {
        script: 'integrated_server.py',
        port: 8000,
        name: 'Integrated Server (Frontend + Backend)'
    },
    // Fallback configuration if integrated server fails
    fallback: {
        backend: {
            script: 'simple_server.py',
            port: 8001,
            name: 'Backend API'
        },
        frontend: {
            script: 'server.js',
            port: 3000,
            name: 'Frontend Server',
            cwd: 'frontend-nodejs'
        }
    }
};

// Track running processes
const processes = new Map();

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

// Check if required files exist
function checkRequiredFiles() {
    console.log('📋 Checking required files...');

    const requiredFiles = [
        { path: config.integrated.script, name: 'Integrated server script' },
        { path: 'requirements.txt', name: 'Python requirements' },
        { path: 'frontend/index.html', name: 'Frontend HTML' },
        { path: 'frontend/script.js', name: 'Frontend JavaScript' },
        { path: 'frontend/styles.css', name: 'Frontend CSS' }
    ];

    let allFilesExist = true;

    requiredFiles.forEach(file => {
        if (fs.existsSync(file.path)) {
            console.log(`   ✅ ${file.name}: ${file.path}`);
        } else {
            console.log(`   ⚠️ ${file.name}: ${file.path} (NOT FOUND)`);
            if (file.path === config.integrated.script) {
                allFilesExist = false;
            }
        }
    });

    if (!allFilesExist) {
        console.error('\n❌ Critical files are missing. Please check the installation.');
        process.exit(1);
    }

    console.log('✅ Required files checked\n');
}

// Check Python environment and dependencies
function checkPythonEnvironment() {
    console.log('🐍 Checking Python environment...');

    // Check if Python is available
    const pythonCommands = ['python', 'python3', 'py'];
    let pythonCmd = null;

    for (const cmd of pythonCommands) {
        try {
            execSync(`${cmd} --version`, { stdio: 'pipe' });
            pythonCmd = cmd;
            console.log(`   ✅ Found Python: ${cmd}`);
            break;
        } catch (error) {
            // Try next command
        }
    }

    if (!pythonCmd) {
        console.error('❌ Python not found. Please install Python 3.9+ and try again.');
        process.exit(1);
    }

    // Check if virtual environment exists
    const venvPath = path.join(process.cwd(), 'venv');
    if (fs.existsSync(venvPath)) {
        console.log('   ✅ Virtual environment found');
    } else {
        console.log('   ⚠️ Virtual environment not found (optional)');
    }

    console.log('✅ Python environment ready\n');
    return pythonCmd;
}

// Check environment variables
function checkEnvironmentVariables() {
    console.log('🔑 Checking environment variables...');

    const requiredEnvVars = [
        { name: 'GEMINI_API_KEY', value: 'AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk' },
        { name: 'OPENWEATHER_API_KEY', value: '********************************' }
    ];

    // Set environment variables if not already set
    requiredEnvVars.forEach(envVar => {
        if (!process.env[envVar.name]) {
            process.env[envVar.name] = envVar.value;
            console.log(`   ✅ Set ${envVar.name}`);
        } else {
            console.log(`   ✅ ${envVar.name} already set`);
        }
    });

    console.log('✅ Environment variables configured\n');
}

// Start a process
function startProcess(name, command, args, options = {}) {
    console.log(`🚀 Starting ${colorize(name, 'cyan')}...`);
    
    const process = spawn(command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        ...options
    });
    
    processes.set(name, process);
    
    // Handle stdout
    process.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`${colorize(`[${name}]`, 'blue')} ${output}`);
        }
    });
    
    // Handle stderr
    process.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            console.log(`${colorize(`[${name}]`, 'red')} ${output}`);
        }
    });
    
    // Handle process exit
    process.on('close', (code) => {
        processes.delete(name);
        if (code === 0) {
            console.log(`${colorize(`[${name}]`, 'green')} Process exited successfully`);
        } else {
            console.log(`${colorize(`[${name}]`, 'red')} Process exited with code ${code}`);
        }
    });
    
    // Handle process error
    process.on('error', (error) => {
        console.error(`${colorize(`[${name}]`, 'red')} Error: ${error.message}`);
        processes.delete(name);
    });
    
    return process;
}

// Start integrated server
function startIntegratedServer(pythonCmd) {
    const { script, name } = config.integrated;

    console.log(`🚀 Starting ${colorize(name, 'cyan')} with ${pythonCmd}...`);

    // Set environment variables for the process
    const env = {
        ...process.env,
        GEMINI_API_KEY: 'AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk',
        OPENWEATHER_API_KEY: '********************************',
        PYTHONUNBUFFERED: '1'
    };

    return startProcess(name, pythonCmd, ['-u', script], { env });
}

// Fallback: Start separate backend and frontend servers
function startSeparateServers(pythonCmd) {
    console.log('🔄 Starting separate backend and frontend servers...');

    // Start backend
    const backendProcess = startProcess(
        config.fallback.backend.name,
        pythonCmd,
        ['-u', config.fallback.backend.script]
    );

    // Start frontend (if Node.js frontend exists)
    let frontendProcess = null;
    const frontendPath = config.fallback.frontend.cwd;
    if (fs.existsSync(path.join(frontendPath, config.fallback.frontend.script))) {
        frontendProcess = startProcess(
            config.fallback.frontend.name,
            'node',
            [config.fallback.frontend.script],
            { cwd: frontendPath }
        );
    }

    return { backendProcess, frontendProcess };
}

// Wait for a port to be available
function waitForPort(port, timeout = 30000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        function checkPort() {
            const net = require('net');
            const socket = new net.Socket();
            
            socket.setTimeout(1000);
            
            socket.on('connect', () => {
                socket.destroy();
                resolve();
            });
            
            socket.on('timeout', () => {
                socket.destroy();
                checkAgain();
            });
            
            socket.on('error', () => {
                checkAgain();
            });
            
            socket.connect(port, 'localhost');
        }
        
        function checkAgain() {
            if (Date.now() - startTime > timeout) {
                reject(new Error(`Timeout waiting for port ${port}`));
            } else {
                setTimeout(checkPort, 1000);
            }
        }
        
        checkPort();
    });
}

// Display startup information
function displayStartupInfo(mode = 'integrated') {
    console.log('\n' + '='.repeat(60));
    console.log(colorize('🎉 Water Management System Started!', 'green'));
    console.log('='.repeat(60));

    if (mode === 'integrated') {
        console.log(`\n🌐 ${colorize('Application URL:', 'cyan')} http://localhost:${config.integrated.port}`);
        console.log(`📖 ${colorize('API Documentation:', 'cyan')} http://localhost:${config.integrated.port}/docs`);
        console.log(`🔗 ${colorize('Health Check:', 'cyan')} http://localhost:${config.integrated.port}/api/health`);
        console.log(`\n✨ ${colorize('Features Available:', 'magenta')}`);
        console.log('   • Complete Water Management Dashboard');
        console.log('   • AI-Powered Climate Analysis');
        console.log('   • Real-time Sensor Monitoring');
        console.log('   • Predictive Maintenance');
        console.log('   • Energy Optimization');
        console.log('   • Blockchain Integration');
    } else {
        console.log(`\n📊 ${colorize('Frontend Dashboard:', 'cyan')} http://localhost:${config.fallback.frontend.port}`);
        console.log(`🔗 ${colorize('Backend API:', 'cyan')} http://localhost:${config.fallback.backend.port}`);
        console.log(`📖 ${colorize('API Documentation:', 'cyan')} http://localhost:${config.fallback.backend.port}/docs`);
    }

    console.log(`\n💡 ${colorize('Tips:', 'yellow')}`);
    console.log('   • Press Ctrl+C to stop all services');
    console.log('   • Check the logs above for any errors');
    console.log('   • All services are running and ready');
    console.log('   • API keys are pre-configured');
    console.log('\n' + '='.repeat(60) + '\n');
}

// Handle graceful shutdown
function setupGracefulShutdown() {
    const shutdown = () => {
        console.log('\n🛑 Shutting down services...');
        
        processes.forEach((process, name) => {
            console.log(`   Stopping ${name}...`);
            process.kill('SIGTERM');
        });
        
        setTimeout(() => {
            console.log('✅ All services stopped');
            process.exit(0);
        }, 2000);
    };
    
    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
}

// Test server connectivity
async function testServerConnectivity(port, timeout = 30000) {
    console.log(`🔍 Testing connectivity to port ${port}...`);

    try {
        await waitForPort(port, timeout);

        // Additional HTTP test
        const http = require('http');
        const options = {
            hostname: 'localhost',
            port: port,
            path: '/api/health',
            method: 'GET',
            timeout: 5000
        };

        return new Promise((resolve, reject) => {
            const req = http.request(options, (res) => {
                console.log(`   ✅ Server responding with status: ${res.statusCode}`);
                resolve(true);
            });

            req.on('error', (err) => {
                console.log(`   ⚠️ Health check failed: ${err.message}`);
                resolve(false); // Don't reject, just indicate failure
            });

            req.on('timeout', () => {
                console.log('   ⚠️ Health check timeout');
                req.destroy();
                resolve(false);
            });

            req.end();
        });
    } catch (error) {
        console.log(`   ❌ Port ${port} not accessible: ${error.message}`);
        return false;
    }
}

// Main startup function
async function main() {
    try {
        console.log('🔧 Performing system checks...\n');

        checkRequiredFiles();
        const pythonCmd = checkPythonEnvironment();
        checkEnvironmentVariables();
        setupGracefulShutdown();

        console.log('🚀 Starting Water Management System...\n');

        // Try integrated server first
        console.log('🎯 Attempting to start integrated server...');
        const integratedProcess = startIntegratedServer(pythonCmd);

        // Wait for integrated server to be ready
        console.log(`⏳ Waiting for integrated server on port ${config.integrated.port}...`);

        let serverReady = false;
        try {
            await waitForPort(config.integrated.port, 45000); // Longer timeout for integrated server
            serverReady = await testServerConnectivity(config.integrated.port);
        } catch (error) {
            console.log(`⚠️ Integrated server startup issue: ${error.message}`);
        }

        if (serverReady) {
            console.log('✅ Integrated server is ready and responding\n');
            displayStartupInfo('integrated');
        } else {
            console.log('⚠️ Integrated server not responding, trying fallback...\n');

            // Kill integrated process if it's running
            if (processes.has(config.integrated.name)) {
                processes.get(config.integrated.name).kill('SIGTERM');
                processes.delete(config.integrated.name);
            }

            // Start separate servers as fallback
            const { backendProcess, frontendProcess } = startSeparateServers(pythonCmd);

            // Wait for backend
            console.log(`⏳ Waiting for backend on port ${config.fallback.backend.port}...`);
            await waitForPort(config.fallback.backend.port);
            console.log('✅ Backend is ready');

            // Wait for frontend if it exists
            if (frontendProcess) {
                console.log(`⏳ Waiting for frontend on port ${config.fallback.frontend.port}...`);
                await waitForPort(config.fallback.frontend.port);
                console.log('✅ Frontend is ready');
            }

            displayStartupInfo('separate');
        }

        // Keep the script running
        console.log('🎮 System is running. Press Ctrl+C to stop.\n');
        await new Promise(() => {}); // Keep alive

    } catch (error) {
        console.error(`\n❌ Startup failed: ${error.message}`);
        console.error('Stack trace:', error.stack);

        // Clean up any running processes
        processes.forEach((process, name) => {
            console.log(`🧹 Cleaning up ${name}...`);
            try {
                process.kill('SIGTERM');
            } catch (e) {
                console.log(`   Warning: Could not kill ${name}: ${e.message}`);
            }
        });

        console.log('\n💡 Troubleshooting tips:');
        console.log('   • Check if Python 3.9+ is installed');
        console.log('   • Ensure all required files are present');
        console.log('   • Check if ports 8000/8001/3000 are available');
        console.log('   • Review the error logs above');

        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    startIntegratedServer,
    startSeparateServers,
    config,
    main
};
