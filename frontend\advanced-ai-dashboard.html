<!-- Advanced AI Dashboard Page -->
<div class="page" id="advanced-ai-dashboard-page">
    <div class="page-header">
        <h1><i class="fas fa-brain"></i> Advanced AI Dashboard</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Deploy Model
            </button>
            <button class="btn-secondary">
                <i class="fas fa-sync"></i> Sync Models
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Results
            </button>
        </div>
    </div>

    <!-- Advanced AI Dashboard -->
    <div class="advanced-ai-dashboard">
        <!-- Federated Learning Section -->
        <div class="panel federated-learning-panel">
            <div class="panel-header">
                <h3>Federated Learning Network</h3>
                <div class="panel-controls">
                    <span class="status-indicator active">
                        <i class="fas fa-network-wired"></i>
                        <span>5 Clients Connected</span>
                    </span>
                </div>
            </div>
            <div class="federated-learning-content">
                <div class="federated-overview">
                    <div class="federated-metric">
                        <div class="metric-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">Round 247</div>
                            <div class="metric-label">Current Round</div>
                        </div>
                    </div>
                    <div class="federated-metric">
                        <div class="metric-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">94.7%</div>
                            <div class="metric-label">Global Accuracy</div>
                        </div>
                    </div>
                    <div class="federated-metric">
                        <div class="metric-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">5/8</div>
                            <div class="metric-label">Active Clients</div>
                        </div>
                    </div>
                    <div class="federated-metric">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-info">
                            <div class="metric-value">12.3s</div>
                            <div class="metric-label">Avg Round Time</div>
                        </div>
                    </div>
                </div>

                <div class="federated-clients">
                    <h4>Client Status</h4>
                    <div class="client-list">
                        <div class="client-item active">
                            <div class="client-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="client-info">
                                <div class="client-name">Treatment Plant A</div>
                                <div class="client-location">New York, USA</div>
                                <div class="client-data">2,847 samples</div>
                            </div>
                            <div class="client-metrics">
                                <div class="client-accuracy">Accuracy: 96.2%</div>
                                <div class="client-contribution">Weight: 0.23</div>
                            </div>
                            <div class="client-status active">Active</div>
                        </div>

                        <div class="client-item active">
                            <div class="client-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="client-info">
                                <div class="client-name">Treatment Plant B</div>
                                <div class="client-location">London, UK</div>
                                <div class="client-data">1,923 samples</div>
                            </div>
                            <div class="client-metrics">
                                <div class="client-accuracy">Accuracy: 94.1%</div>
                                <div class="client-contribution">Weight: 0.19</div>
                            </div>
                            <div class="client-status active">Active</div>
                        </div>

                        <div class="client-item offline">
                            <div class="client-icon">
                                <i class="fas fa-server"></i>
                            </div>
                            <div class="client-info">
                                <div class="client-name">Treatment Plant C</div>
                                <div class="client-location">Tokyo, Japan</div>
                                <div class="client-data">1,456 samples</div>
                            </div>
                            <div class="client-metrics">
                                <div class="client-accuracy">Accuracy: 92.8%</div>
                                <div class="client-contribution">Weight: 0.15</div>
                            </div>
                            <div class="client-status offline">Offline</div>
                        </div>
                    </div>
                </div>

                <div class="federated-progress">
                    <h4>Training Progress</h4>
                    <canvas id="federatedProgressChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Reinforcement Learning Section -->
        <div class="panel reinforcement-learning-panel">
            <div class="panel-header">
                <h3>Reinforcement Learning Agents</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="start-training">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-icon" data-action="pause-training">
                        <i class="fas fa-pause"></i>
                    </button>
                </div>
            </div>
            <div class="reinforcement-learning-content">
                <div class="rl-agents">
                    <div class="rl-agent-card">
                        <div class="agent-header">
                            <div class="agent-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="agent-info">
                                <h4>Water Treatment RL Agent</h4>
                                <div class="agent-algorithm">Deep Q-Network (DQN)</div>
                                <div class="agent-status training">Training</div>
                            </div>
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <span class="metric-label">Episode</span>
                                <span class="metric-value">1,247</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Avg Reward</span>
                                <span class="metric-value">847.3</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Epsilon</span>
                                <span class="metric-value">0.12</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Loss</span>
                                <span class="metric-value">0.023</span>
                            </div>
                        </div>
                        <div class="agent-progress">
                            <canvas id="rlAgent1Progress"></canvas>
                        </div>
                    </div>

                    <div class="rl-agent-card">
                        <div class="agent-header">
                            <div class="agent-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="agent-info">
                                <h4>Energy Optimization RL Agent</h4>
                                <div class="agent-algorithm">Proximal Policy Optimization (PPO)</div>
                                <div class="agent-status deployed">Deployed</div>
                            </div>
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <span class="metric-label">Episode</span>
                                <span class="metric-value">2,847</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Avg Reward</span>
                                <span class="metric-value">1,234.7</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Policy Loss</span>
                                <span class="metric-value">0.015</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Value Loss</span>
                                <span class="metric-value">0.008</span>
                            </div>
                        </div>
                        <div class="agent-progress">
                            <canvas id="rlAgent2Progress"></canvas>
                        </div>
                    </div>
                </div>

                <div class="rl-environment">
                    <h4>Training Environment</h4>
                    <div class="environment-visualization">
                        <canvas id="rlEnvironmentViz"></canvas>
                    </div>
                    <div class="environment-controls">
                        <button class="btn-env">Reset Environment</button>
                        <button class="btn-env">Step Forward</button>
                        <button class="btn-env">Auto Play</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfer Learning Section -->
        <div class="panel transfer-learning-panel">
            <div class="panel-header">
                <h3>Transfer Learning Hub</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="transfer-model">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <button class="btn-icon" data-action="fine-tune">
                        <i class="fas fa-cogs"></i>
                    </button>
                </div>
            </div>
            <div class="transfer-learning-content">
                <div class="transfer-models">
                    <div class="transfer-model-item">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="model-info">
                                <h4>Water Quality Predictor</h4>
                                <div class="model-source">Source: Treatment Plant A</div>
                                <div class="model-target">Target: Treatment Plant B</div>
                            </div>
                        </div>
                        <div class="transfer-metrics">
                            <div class="metric">
                                <span class="metric-label">Source Accuracy</span>
                                <span class="metric-value">96.2%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Transfer Accuracy</span>
                                <span class="metric-value">89.7%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Improvement</span>
                                <span class="metric-value">+12.3%</span>
                            </div>
                        </div>
                        <div class="transfer-status">
                            <div class="status-indicator completed">
                                <i class="fas fa-check"></i>
                                <span>Transfer Complete</span>
                            </div>
                        </div>
                    </div>

                    <div class="transfer-model-item">
                        <div class="model-header">
                            <div class="model-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="model-info">
                                <h4>Energy Efficiency Model</h4>
                                <div class="model-source">Source: Energy Grid A</div>
                                <div class="model-target">Target: Energy Grid B</div>
                            </div>
                        </div>
                        <div class="transfer-metrics">
                            <div class="metric">
                                <span class="metric-label">Source Accuracy</span>
                                <span class="metric-value">94.1%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Transfer Accuracy</span>
                                <span class="metric-value">87.3%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Improvement</span>
                                <span class="metric-value">+8.9%</span>
                            </div>
                        </div>
                        <div class="transfer-status">
                            <div class="status-indicator in-progress">
                                <i class="fas fa-cog fa-spin"></i>
                                <span>Fine-tuning</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="transfer-progress">
                    <h4>Transfer Learning Progress</h4>
                    <canvas id="transferLearningChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Model Interpretability Section -->
        <div class="panel model-interpretability-panel">
            <div class="panel-header">
                <h3>Model Interpretability</h3>
                <div class="panel-controls">
                    <select class="model-select">
                        <option value="water-quality">Water Quality Model</option>
                        <option value="energy-efficiency">Energy Efficiency Model</option>
                        <option value="treatment-optimization">Treatment Optimization Model</option>
                    </select>
                </div>
            </div>
            <div class="model-interpretability-content">
                <div class="interpretability-methods">
                    <div class="method-card shap">
                        <div class="method-header">
                            <h4>SHAP Analysis</h4>
                            <div class="method-status active">Active</div>
                        </div>
                        <div class="shap-visualization">
                            <canvas id="shapAnalysisChart"></canvas>
                        </div>
                        <div class="shap-insights">
                            <div class="insight-item">
                                <span class="feature-name">Temperature</span>
                                <span class="feature-importance">0.34</span>
                                <span class="feature-impact positive">Positive</span>
                            </div>
                            <div class="insight-item">
                                <span class="feature-name">pH Level</span>
                                <span class="feature-importance">0.28</span>
                                <span class="feature-impact positive">Positive</span>
                            </div>
                            <div class="insight-item">
                                <span class="feature-name">Turbidity</span>
                                <span class="feature-importance">0.23</span>
                                <span class="feature-impact negative">Negative</span>
                            </div>
                        </div>
                    </div>

                    <div class="method-card lime">
                        <div class="method-header">
                            <h4>LIME Explanations</h4>
                            <div class="method-status active">Active</div>
                        </div>
                        <div class="lime-visualization">
                            <canvas id="limeAnalysisChart"></canvas>
                        </div>
                        <div class="lime-explanation">
                            <p>The model predicts <strong>High Quality</strong> with 94.7% confidence.</p>
                            <p>Key factors: Temperature (+0.34), pH Level (+0.28), Low Turbidity (+0.23)</p>
                        </div>
                    </div>
                </div>

                <div class="feature-importance">
                    <h4>Global Feature Importance</h4>
                    <div class="importance-chart">
                        <canvas id="featureImportanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hyperparameter Optimization Section -->
        <div class="panel hyperparameter-optimization-panel">
            <div class="panel-header">
                <h3>Hyperparameter Optimization</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="start-optimization">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-icon" data-action="stop-optimization">
                        <i class="fas fa-stop"></i>
                    </button>
                </div>
            </div>
            <div class="hyperparameter-optimization-content">
                <div class="optimization-status">
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">Trial 47/100</div>
                            <div class="status-label">Current Trial</div>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">0.9847</div>
                            <div class="status-label">Best Score</div>
                        </div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-value">23.4 min</div>
                            <div class="status-label">Elapsed Time</div>
                        </div>
                    </div>
                </div>

                <div class="optimization-progress">
                    <h4>Optimization Progress</h4>
                    <canvas id="hyperparameterProgressChart"></canvas>
                </div>

                <div class="best-parameters">
                    <h4>Best Parameters Found</h4>
                    <div class="parameter-list">
                        <div class="parameter-item">
                            <span class="parameter-name">Learning Rate</span>
                            <span class="parameter-value">0.001</span>
                        </div>
                        <div class="parameter-item">
                            <span class="parameter-name">Batch Size</span>
                            <span class="parameter-value">64</span>
                        </div>
                        <div class="parameter-item">
                            <span class="parameter-name">Hidden Layers</span>
                            <span class="parameter-value">3</span>
                        </div>
                        <div class="parameter-item">
                            <span class="parameter-name">Dropout Rate</span>
                            <span class="parameter-value">0.2</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
