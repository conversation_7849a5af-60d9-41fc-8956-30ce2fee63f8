#!/usr/bin/env python3
"""
Comprehensive Error Check and Debugging Analysis
for Water Management System Application
"""

import requests
import json
import time
from datetime import datetime

class SystemTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.errors = []
        self.warnings = []
        self.successes = []
        
    def log_error(self, test_name, error_msg):
        self.errors.append(f"❌ CRITICAL: {test_name} - {error_msg}")
        print(f"❌ CRITICAL: {test_name} - {error_msg}")
        
    def log_warning(self, test_name, warning_msg):
        self.warnings.append(f"⚠️ WARNING: {test_name} - {warning_msg}")
        print(f"⚠️ WARNING: {test_name} - {warning_msg}")
        
    def log_success(self, test_name, success_msg="OK"):
        self.successes.append(f"✅ SUCCESS: {test_name} - {success_msg}")
        print(f"✅ SUCCESS: {test_name} - {success_msg}")

    def test_server_health(self):
        """Test basic server health and connectivity"""
        print("\n🔍 Testing Server Health...")
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_success("Server Health", f"HTTP {response.status_code}")
                
                # Check service availability
                services = data.get('services', {})
                for service, available in services.items():
                    if not available:
                        self.log_warning("Backend Service", f"{service} not available (using mock data)")
                    else:
                        self.log_success("Backend Service", f"{service} available")
            else:
                self.log_error("Server Health", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_error("Server Health", str(e))

    def test_frontend_files(self):
        """Test frontend file serving"""
        print("\n🔍 Testing Frontend Files...")
        files = [
            ("/", "text/html"),
            ("/styles.css", "text/css"),
            ("/script.js", "application/javascript")
        ]
        
        for file_path, expected_type in files:
            try:
                response = requests.get(f"{self.base_url}{file_path}", timeout=5)
                if response.status_code == 200:
                    content_length = len(response.content)
                    if content_length > 0:
                        self.log_success("Frontend File", f"{file_path} - {content_length} bytes")
                    else:
                        self.log_warning("Frontend File", f"{file_path} - Empty file")
                else:
                    self.log_error("Frontend File", f"{file_path} - HTTP {response.status_code}")
            except Exception as e:
                self.log_error("Frontend File", f"{file_path} - {str(e)}")

    def test_api_endpoints(self):
        """Test all API endpoints"""
        print("\n🔍 Testing API Endpoints...")
        endpoints = [
            "/api/water-quality",
            "/api/energy/grid", 
            "/api/ai/agents",
            "/api/ml/optimization",
            "/api/sensors/network",
            "/api/treatment/systems",
            "/api/analytics/kpis",
            "/api/blockchain/status",
            "/api/maintenance/predictions",
            "/api/climate/current"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('status') == 'success':
                        self.log_success("API Endpoint", f"{endpoint}")
                    else:
                        self.log_warning("API Endpoint", f"{endpoint} - No success status")
                else:
                    self.log_error("API Endpoint", f"{endpoint} - HTTP {response.status_code}")
            except Exception as e:
                self.log_error("API Endpoint", f"{endpoint} - {str(e)}")

    def test_chat_api(self):
        """Test chat API functionality"""
        print("\n🔍 Testing Chat API...")
        try:
            payload = {"message": "Test message for water management system"}
            response = requests.post(
                f"{self.base_url}/api/ai/chat",
                json=payload,
                timeout=10
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success' and data.get('response'):
                    self.log_success("Chat API", "Response received")
                else:
                    self.log_warning("Chat API", "No response content")
            else:
                self.log_error("Chat API", f"HTTP {response.status_code}")
        except Exception as e:
            self.log_error("Chat API", str(e))

    def test_data_integrity(self):
        """Test data structure integrity"""
        print("\n🔍 Testing Data Integrity...")
        
        # Test water quality data structure
        try:
            response = requests.get(f"{self.base_url}/api/water-quality", timeout=5)
            if response.status_code == 200:
                data = response.json().get('data', {})
                required_fields = ['ph_level', 'turbidity', 'chlorine', 'bacteria_count']
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    self.log_success("Data Integrity", "Water quality data complete")
                else:
                    self.log_warning("Data Integrity", f"Missing fields: {missing_fields}")
            else:
                self.log_error("Data Integrity", "Water quality data unavailable")
        except Exception as e:
            self.log_error("Data Integrity", str(e))

    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 Starting Comprehensive Error Check and Debugging Analysis")
        print("=" * 70)
        
        start_time = time.time()
        
        # Run all tests
        self.test_server_health()
        self.test_frontend_files()
        self.test_api_endpoints()
        self.test_chat_api()
        self.test_data_integrity()
        
        # Generate report
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 70)
        print(f"⏱️ Test Duration: {duration:.2f} seconds")
        print(f"✅ Successes: {len(self.successes)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        print(f"❌ Critical Errors: {len(self.errors)}")
        
        if self.errors:
            print("\n🚨 CRITICAL ERRORS:")
            for error in self.errors:
                print(f"  {error}")
                
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for warning in self.warnings:
                print(f"  {warning}")
                
        print(f"\n🎯 OVERALL STATUS: ", end="")
        if not self.errors:
            if not self.warnings:
                print("🟢 EXCELLENT - No issues found")
            else:
                print("🟡 GOOD - Minor warnings only")
        else:
            print("🔴 NEEDS ATTENTION - Critical errors found")
            
        return len(self.errors) == 0

if __name__ == "__main__":
    tester = SystemTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
