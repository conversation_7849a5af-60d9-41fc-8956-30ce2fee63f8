#!/usr/bin/env python3
"""
Environment Setup Script
Sets up API keys and verifies system readiness
"""

import os
import sys

def setup_api_keys():
    """Set up API keys in environment"""
    # API keys from user's memory
    api_keys = {
        'GEMINI_API_KEY': 'AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk',
        'OPENWEATHER_API_KEY': '********************************'
    }
    
    print("🔑 Setting up API keys...")
    for key, value in api_keys.items():
        os.environ[key] = value
        print(f"✅ {key} configured")
    
    return True

def create_env_file():
    """Create .env file for persistent configuration"""
    env_content = """# Water Management System Environment Variables
GEMINI_API_KEY=AIzaSyDp7zIFyCisoa9gBzAk5ggvFe7QE_0iegk
OPENWEATHER_API_KEY=********************************

# Server Configuration
BACKEND_PORT=8000
FRONTEND_PORT=3000
NODE_ENV=development
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ .env file created successfully")
        return True
    except Exception as e:
        print(f"⚠️ Could not create .env file: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 WATER MANAGEMENT SYSTEM - ENVIRONMENT SETUP")
    print("=" * 55)
    
    # Setup API keys
    setup_api_keys()
    
    # Create .env file
    create_env_file()
    
    print("\n" + "=" * 55)
    print("✅ Environment setup complete!")
    print("\n📋 Next steps:")
    print("1. Start backend: python integrated_server.py")
    print("2. Start frontend: cd frontend-nodejs && npm start")
    print("3. Run health check: python test_system_health.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
