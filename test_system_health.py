#!/usr/bin/env python3
"""
System Health Check Script
Tests all major components to ensure they work despite dependency warnings
"""

import sys
import traceback

def test_component(name, test_func):
    """Test a component and report results"""
    try:
        test_func()
        print(f"✅ {name}: PASSED")
        return True
    except Exception as e:
        print(f"❌ {name}: FAILED - {str(e)}")
        return False

def test_basic_imports():
    """Test basic Python imports"""
    import os
    import json
    import asyncio
    from datetime import datetime
    from pathlib import Path

def test_web_framework():
    """Test web framework components"""
    from fastapi import FastAPI
    import uvicorn

def test_google_ai():
    """Test Google AI components"""
    import google.generativeai as genai
    from langchain_google_genai import ChatGoogleGenerativeAI

def test_backend_modules():
    """Test backend module imports"""
    from src.data.climate_data_collector import ClimateDataCollector
    from src.llm.gemini_integration import GeminiIntegration
    from src.orchestration.agent_coordinator import AgentCoordinator

def test_optimization():
    """Test optimization modules"""
    from src.optimization.genetic_algorithm import WaterManagementGA
    from src.ml.neural_networks import WaterManagementNeuralNetworks

def test_database():
    """Test database connectivity"""
    import sqlite3
    conn = sqlite3.connect('data/watermanagement.db')
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM climate_data")
    count = cursor.fetchone()[0]
    conn.close()
    assert count > 0, f"Database has {count} records"

def test_api_keys():
    """Test API key configuration"""
    import os
    from pathlib import Path

    # Try to load from .env file if environment variables not set
    if not os.environ.get('GEMINI_API_KEY'):
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if line.startswith('GEMINI_API_KEY='):
                        os.environ['GEMINI_API_KEY'] = line.split('=', 1)[1].strip()
                    elif line.startswith('OPENWEATHER_API_KEY='):
                        os.environ['OPENWEATHER_API_KEY'] = line.split('=', 1)[1].strip()

    gemini_key = os.environ.get('GEMINI_API_KEY')
    weather_key = os.environ.get('OPENWEATHER_API_KEY')
    assert gemini_key, "GEMINI_API_KEY not set"
    assert weather_key, "OPENWEATHER_API_KEY not set"

def main():
    """Run all system health checks"""
    print("🔍 WATER MANAGEMENT SYSTEM HEALTH CHECK")
    print("=" * 50)
    
    tests = [
        ("Basic Python Imports", test_basic_imports),
        ("Web Framework (FastAPI/Uvicorn)", test_web_framework),
        ("Google AI Integration", test_google_ai),
        ("Backend Modules", test_backend_modules),
        ("Optimization Modules", test_optimization),
        ("Database Connectivity", test_database),
        ("API Key Configuration", test_api_keys),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        if test_component(name, test_func):
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - System is fully operational!")
        print("⚠️  Note: Dependency version warnings are non-critical")
        return 0
    else:
        print("⚠️  Some tests failed - check individual components")
        return 1

if __name__ == "__main__":
    sys.exit(main())
