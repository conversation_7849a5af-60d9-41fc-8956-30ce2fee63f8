#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unified Platform Integration Test Suite
Testing the complete integration of all existing and new features
"""

import asyncio
import pytest
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Import the unified platform
from marine_conservation.integrated_platform.unified_marine_platform import (
    UnifiedMarineConservationPlatform,
    test_unified_platform_integration,
    demonstrate_unified_capabilities
)


class TestUnifiedPlatformIntegration:
    """Test suite for unified marine conservation platform integration"""
    
    @pytest.fixture
    def taiwan_area(self):
        """Taiwan Strait test area"""
        return (119.0, 23.0, 121.0, 25.0)
    
    @pytest.fixture
    def mediterranean_area(self):
        """Mediterranean Sea test area"""
        return (2.0, 41.0, 3.0, 42.0)
    
    @pytest.fixture
    def pacific_area(self):
        """Pacific Coast test area"""
        return (-125.0, 32.0, -117.0, 37.0)
    
    @pytest.mark.asyncio
    async def test_unified_platform_initialization(self):
        """Test unified platform initialization"""
        print("\n🌊 Testing Unified Platform Initialization")
        
        try:
            platform = UnifiedMarineConservationPlatform()
            
            # Verify all components are initialized
            assert hasattr(platform, 'debris_engine')
            assert hasattr(platform, 'climate_agent')
            assert hasattr(platform, 'sustainability_agent')
            assert hasattr(platform, 'risk_agent')
            assert hasattr(platform, 'community_agent')
            assert hasattr(platform, 'policy_agent')
            assert hasattr(platform, 'blockchain_system')
            assert hasattr(platform, 'ar_vr_suite')
            assert hasattr(platform, 'iot_network')
            assert hasattr(platform, 'global_scaling')
            
            # Test platform status
            status = await platform.get_platform_status()
            assert status['status'] == 'operational'
            assert status['version'] == '2.0.0'
            assert 'existing_features' in status['components']
            assert 'new_features' in status['components']
            
            print(f"   ✅ Platform initialization test passed")
            print(f"      Platform Status: {status['status']}")
            print(f"      Version: {status['version']}")
            print(f"      Components: {len(status['components']['existing_features']) + len(status['components']['new_features'])}")
            return True
            
        except Exception as e:
            print(f"   ❌ Platform initialization test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_comprehensive_marine_operation(self, taiwan_area):
        """Test comprehensive marine operation"""
        print("\n🎯 Testing Comprehensive Marine Operation")
        
        try:
            platform = UnifiedMarineConservationPlatform()
            
            # Execute comprehensive operation
            result = await platform.execute_unified_marine_operation(
                area_bbox=taiwan_area,
                operation_type="comprehensive_assessment",
                include_community_engagement=True,
                include_policy_analysis=True,
                include_innovation_tracking=True,
                blockchain_recording=True
            )
            
            # Validate core result structure
            assert hasattr(result, 'operation_id')
            assert hasattr(result, 'operation_type')
            assert hasattr(result, 'area_covered')
            assert hasattr(result, 'overall_health_score')
            assert hasattr(result, 'processing_time_seconds')
            assert hasattr(result, 'recommendations')
            
            # Validate existing feature results
            assert hasattr(result, 'debris_detections')
            assert hasattr(result, 'hotspot_analysis')
            assert hasattr(result, 'route_optimization')
            assert hasattr(result, 'recycling_analysis')
            assert hasattr(result, 'climate_analysis')
            assert hasattr(result, 'sustainability_assessment')
            assert hasattr(result, 'risk_assessment')
            
            # Validate new feature results
            assert hasattr(result, 'community_engagement')
            assert hasattr(result, 'policy_compliance')
            assert hasattr(result, 'innovation_opportunities')
            assert hasattr(result, 'advanced_analytics')
            assert hasattr(result, 'blockchain_records')
            assert hasattr(result, 'ar_vr_content')
            assert hasattr(result, 'iot_sensor_data')
            
            # Validate integration metrics
            assert hasattr(result, 'environmental_impact')
            assert hasattr(result, 'economic_analysis')
            assert hasattr(result, 'social_impact')
            
            # Validate data quality
            assert result.operation_type == "comprehensive_assessment"
            assert result.area_covered == taiwan_area
            assert 0.0 <= result.overall_health_score <= 1.0
            assert result.processing_time_seconds > 0
            assert len(result.recommendations) > 0
            assert 0.0 <= result.confidence_score <= 1.0
            
            print(f"   ✅ Comprehensive operation test passed")
            print(f"      Operation ID: {result.operation_id}")
            print(f"      Health Score: {result.overall_health_score:.2f}")
            print(f"      Processing Time: {result.processing_time_seconds:.2f}s")
            print(f"      Debris Detected: {len(result.debris_detections)}")
            print(f"      Recommendations: {len(result.recommendations)}")
            print(f"      Confidence Score: {result.confidence_score:.2f}")
            return True
            
        except Exception as e:
            print(f"   ❌ Comprehensive operation test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_multi_area_operations(self, taiwan_area, mediterranean_area, pacific_area):
        """Test operations across multiple areas"""
        print("\n🌍 Testing Multi-Area Operations")
        
        try:
            platform = UnifiedMarineConservationPlatform()
            
            test_areas = [
                ("Taiwan Strait", taiwan_area),
                ("Mediterranean", mediterranean_area),
                ("Pacific Coast", pacific_area)
            ]
            
            results = []
            
            for area_name, area_bbox in test_areas:
                try:
                    result = await platform.execute_unified_marine_operation(
                        area_bbox=area_bbox,
                        operation_type="rapid_assessment",
                        include_community_engagement=True,
                        include_policy_analysis=True,
                        include_innovation_tracking=False,  # Faster execution
                        blockchain_recording=True
                    )
                    
                    results.append({
                        'area': area_name,
                        'success': True,
                        'health_score': result.overall_health_score,
                        'debris_count': len(result.debris_detections),
                        'processing_time': result.processing_time_seconds,
                        'confidence': result.confidence_score
                    })
                    
                except Exception as e:
                    results.append({
                        'area': area_name,
                        'success': False,
                        'error': str(e)
                    })
            
            # Validate results
            successful_operations = [r for r in results if r['success']]
            assert len(successful_operations) >= 2, "At least 2 areas should succeed"
            
            # Validate consistency across areas
            for result in successful_operations:
                assert 0.0 <= result['health_score'] <= 1.0
                assert result['processing_time'] > 0
                assert 0.0 <= result['confidence'] <= 1.0
            
            print(f"   ✅ Multi-area operations test passed")
            print(f"      Areas Tested: {len(test_areas)}")
            print(f"      Successful Operations: {len(successful_operations)}")
            
            for result in results:
                if result['success']:
                    print(f"      {result['area']}: Health {result['health_score']:.2f}, "
                          f"Debris {result['debris_count']}, Time {result['processing_time']:.1f}s")
                else:
                    print(f"      {result['area']}: Failed - {result['error'][:50]}...")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Multi-area operations test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_feature_integration_validation(self, taiwan_area):
        """Test integration between existing and new features"""
        print("\n🔄 Testing Feature Integration Validation")
        
        try:
            platform = UnifiedMarineConservationPlatform()
            
            # Execute operation with all features enabled
            result = await platform.execute_unified_marine_operation(
                area_bbox=taiwan_area,
                operation_type="integration_validation",
                include_community_engagement=True,
                include_policy_analysis=True,
                include_innovation_tracking=True,
                blockchain_recording=True
            )
            
            # Validate cross-feature data consistency
            
            # 1. Debris data should be consistent across components
            debris_count = len(result.debris_detections)
            
            # Hotspot analysis should reference debris data
            if isinstance(result.hotspot_analysis, dict) and 'error' not in result.hotspot_analysis:
                assert 'hotspots_detected' in result.hotspot_analysis or 'analysis_method' in result.hotspot_analysis
            
            # Route optimization should consider debris locations
            if isinstance(result.route_optimization, dict) and 'error' not in result.route_optimization:
                assert 'routes_optimized' in result.route_optimization or 'optimization_method' in result.route_optimization
            
            # 2. Environmental metrics should be integrated
            assert isinstance(result.environmental_impact, dict)
            if 'error' not in result.environmental_impact:
                assert 'debris_impact' in result.environmental_impact
                assert 'ecosystem_health' in result.environmental_impact
            
            # 3. Economic analysis should integrate multiple sources
            assert isinstance(result.economic_analysis, dict)
            if 'error' not in result.economic_analysis:
                assert 'revenue_streams' in result.economic_analysis
            
            # 4. Social impact should integrate community and policy data
            assert isinstance(result.social_impact, dict)
            if 'error' not in result.social_impact:
                assert 'community_impact' in result.social_impact
                assert 'policy_impact' in result.social_impact
            
            # 5. Recommendations should be comprehensive
            assert len(result.recommendations) >= 3
            
            # 6. Blockchain recording should be present
            assert isinstance(result.blockchain_records, dict)
            
            print(f"   ✅ Feature integration validation test passed")
            print(f"      Cross-feature consistency verified")
            print(f"      Environmental impact integrated: {'error' not in result.environmental_impact}")
            print(f"      Economic analysis integrated: {'error' not in result.economic_analysis}")
            print(f"      Social impact integrated: {'error' not in result.social_impact}")
            print(f"      Blockchain recorded: {'error' not in result.blockchain_records}")
            return True
            
        except Exception as e:
            print(f"   ❌ Feature integration validation test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_performance_and_scalability(self, taiwan_area):
        """Test platform performance and scalability"""
        print("\n⚡ Testing Performance and Scalability")
        
        try:
            platform = UnifiedMarineConservationPlatform()
            
            # Test multiple concurrent operations
            concurrent_operations = []
            
            for i in range(3):  # Test 3 concurrent operations
                operation = platform.execute_unified_marine_operation(
                    area_bbox=taiwan_area,
                    operation_type=f"performance_test_{i}",
                    include_community_engagement=True,
                    include_policy_analysis=False,  # Reduce load
                    include_innovation_tracking=False,
                    blockchain_recording=True
                )
                concurrent_operations.append(operation)
            
            # Execute concurrently
            start_time = datetime.now()
            results = await asyncio.gather(*concurrent_operations, return_exceptions=True)
            end_time = datetime.now()
            
            total_time = (end_time - start_time).total_seconds()
            
            # Validate results
            successful_results = [r for r in results if not isinstance(r, Exception)]
            failed_results = [r for r in results if isinstance(r, Exception)]
            
            assert len(successful_results) >= 2, "At least 2 concurrent operations should succeed"
            
            # Performance validation
            assert total_time < 60, "Concurrent operations should complete within 60 seconds"
            
            # Validate individual operation performance
            for result in successful_results:
                assert result.processing_time_seconds < 30, "Individual operations should complete within 30 seconds"
            
            print(f"   ✅ Performance and scalability test passed")
            print(f"      Concurrent Operations: {len(concurrent_operations)}")
            print(f"      Successful: {len(successful_results)}")
            print(f"      Failed: {len(failed_results)}")
            print(f"      Total Time: {total_time:.2f}s")
            print(f"      Average Operation Time: {sum(r.processing_time_seconds for r in successful_results) / len(successful_results):.2f}s")
            return True
            
        except Exception as e:
            print(f"   ❌ Performance and scalability test failed: {e}")
            return False
    
    @pytest.mark.asyncio
    async def test_error_handling_and_resilience(self):
        """Test error handling and system resilience"""
        print("\n🛡️ Testing Error Handling and Resilience")
        
        try:
            platform = UnifiedMarineConservationPlatform()
            
            # Test with invalid area (should handle gracefully)
            invalid_area = (200.0, 100.0, 300.0, 150.0)  # Invalid coordinates
            
            result = await platform.execute_unified_marine_operation(
                area_bbox=invalid_area,
                operation_type="error_handling_test",
                include_community_engagement=True,
                include_policy_analysis=True,
                include_innovation_tracking=True,
                blockchain_recording=True
            )
            
            # System should handle errors gracefully and return a result
            assert hasattr(result, 'operation_id')
            assert hasattr(result, 'processing_time_seconds')
            assert result.processing_time_seconds > 0
            
            # Some components may fail, but system should remain operational
            error_count = 0
            if isinstance(result.hotspot_analysis, dict) and 'error' in result.hotspot_analysis:
                error_count += 1
            if isinstance(result.route_optimization, dict) and 'error' in result.route_optimization:
                error_count += 1
            if isinstance(result.recycling_analysis, dict) and 'error' in result.recycling_analysis:
                error_count += 1
            
            # System should be resilient - not all components should fail
            assert error_count < 10, "System should be resilient to individual component failures"
            
            print(f"   ✅ Error handling and resilience test passed")
            print(f"      Operation completed despite invalid input")
            print(f"      Component errors handled: {error_count}")
            print(f"      System remained operational")
            return True
            
        except Exception as e:
            print(f"   ❌ Error handling and resilience test failed: {e}")
            return False


async def run_unified_platform_integration_tests():
    """Run all unified platform integration tests"""
    print("🧪 UNIFIED PLATFORM INTEGRATION TESTS")
    print("=" * 70)
    
    test_suite = TestUnifiedPlatformIntegration()
    
    # Test areas
    taiwan_area = (119.0, 23.0, 121.0, 25.0)
    mediterranean_area = (2.0, 41.0, 3.0, 42.0)
    pacific_area = (-125.0, 32.0, -117.0, 37.0)
    
    results = {}
    
    # Run all tests
    test_methods = [
        ('initialization', test_suite.test_unified_platform_initialization),
        ('comprehensive_operation', lambda: test_suite.test_comprehensive_marine_operation(taiwan_area)),
        ('multi_area_operations', lambda: test_suite.test_multi_area_operations(taiwan_area, mediterranean_area, pacific_area)),
        ('feature_integration', lambda: test_suite.test_feature_integration_validation(taiwan_area)),
        ('performance_scalability', lambda: test_suite.test_performance_and_scalability(taiwan_area)),
        ('error_handling', test_suite.test_error_handling_and_resilience)
    ]
    
    for test_name, test_method in test_methods:
        try:
            result = await test_method()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 70)
    print("🏆 UNIFIED PLATFORM INTEGRATION TEST RESULTS")
    print("=" * 70)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        display_name = test_name.replace('_', ' ').title()
        print(f"   {display_name}: {status}")
    
    print(f"\nIntegration Tests: {passed}/{total} passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("🚀 UNIFIED PLATFORM FULLY OPERATIONAL")
        print("✅ Ready for production deployment")
        print("🌍 Ready for global scaling")
        print("🇹🇼 Ready for Taiwan government collaboration")
        print("💼 Ready for Y Combinator application")
    else:
        print(f"\n⚠️  {total - passed} integration tests failed")
        print("🔧 Platform needs refinement before deployment")
    
    return results


if __name__ == "__main__":
    # Run unified platform integration tests
    results = asyncio.run(run_unified_platform_integration_tests())
    
    # Exit with appropriate code
    if all(results.values()):
        print("\n✅ Unified platform integration successful!")
        exit(0)
    else:
        print("\n❌ Some integration tests failed")
        exit(1)
