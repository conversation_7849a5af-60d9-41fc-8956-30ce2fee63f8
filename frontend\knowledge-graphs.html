<!-- Knowledge Graphs Page -->
<div class="page" id="knowledge-graphs-page">
    <div class="page-header">
        <h1><i class="fas fa-project-diagram"></i> Knowledge Graphs</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Create Graph
            </button>
            <button class="btn-secondary">
                <i class="fas fa-search"></i> Query Graph
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Graph
            </button>
        </div>
    </div>

    <!-- Knowledge Graphs Dashboard -->
    <div class="knowledge-graphs-dashboard">
        <!-- Graph Overview -->
        <div class="graph-overview">
            <div class="graph-stats">
                <div class="stat-card nodes">
                    <div class="stat-icon">
                        <i class="fas fa-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">12,847</div>
                        <div class="stat-label">Nodes</div>
                    </div>
                </div>
                <div class="stat-card relationships">
                    <div class="stat-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">34,592</div>
                        <div class="stat-label">Relationships</div>
                    </div>
                </div>
                <div class="stat-card entities">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">247</div>
                        <div class="stat-label">Entity Types</div>
                    </div>
                </div>
                <div class="stat-card queries">
                    <div class="stat-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">1,847</div>
                        <div class="stat-label">Queries Today</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Visualization -->
        <div class="panel graph-visualization-panel">
            <div class="panel-header">
                <h3>Knowledge Graph Visualization</h3>
                <div class="panel-controls">
                    <select class="graph-type-select">
                        <option value="water-treatment">Water Treatment</option>
                        <option value="climate-data">Climate Data</option>
                        <option value="energy-systems">Energy Systems</option>
                        <option value="sensor-network">Sensor Network</option>
                    </select>
                    <button class="btn-icon" data-action="center-graph">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                    <button class="btn-icon" data-action="fullscreen">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            <div class="graph-visualization-content">
                <div class="graph-canvas" id="knowledgeGraphCanvas">
                    <!-- Interactive graph visualization will be rendered here -->
                    <svg width="100%" height="500">
                        <!-- Central node -->
                        <circle cx="400" cy="250" r="30" fill="#4ade80" stroke="#22c55e" stroke-width="3"/>
                        <text x="400" y="255" text-anchor="middle" fill="white" font-weight="bold">Water Treatment</text>
                        
                        <!-- Connected nodes -->
                        <circle cx="200" cy="150" r="20" fill="#3b82f6" stroke="#2563eb" stroke-width="2"/>
                        <text x="200" y="155" text-anchor="middle" fill="white" font-size="12">Sensors</text>
                        
                        <circle cx="600" cy="150" r="20" fill="#f59e0b" stroke="#d97706" stroke-width="2"/>
                        <text x="600" y="155" text-anchor="middle" fill="white" font-size="12">Energy</text>
                        
                        <circle cx="200" cy="350" r="20" fill="#8b5cf6" stroke="#7c3aed" stroke-width="2"/>
                        <text x="200" y="355" text-anchor="middle" fill="white" font-size="12">Quality</text>
                        
                        <circle cx="600" cy="350" r="20" fill="#ef4444" stroke="#dc2626" stroke-width="2"/>
                        <text x="600" y="355" text-anchor="middle" fill="white" font-size="12">Climate</text>
                        
                        <circle cx="300" cy="100" r="15" fill="#06b6d4" stroke="#0891b2" stroke-width="2"/>
                        <text x="300" y="105" text-anchor="middle" fill="white" font-size="10">pH</text>
                        
                        <circle cx="500" cy="100" r="15" fill="#06b6d4" stroke="#0891b2" stroke-width="2"/>
                        <text x="500" y="105" text-anchor="middle" fill="white" font-size="10">Flow</text>
                        
                        <!-- Relationships -->
                        <line x1="370" y1="230" x2="220" y2="170" stroke="#4ade80" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="430" y1="230" x2="580" y2="170" stroke="#4ade80" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="370" y1="270" x2="220" y2="330" stroke="#4ade80" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="430" y1="270" x2="580" y2="330" stroke="#4ade80" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="220" y1="140" x2="310" y2="110" stroke="#3b82f6" stroke-width="1" marker-end="url(#arrowhead)"/>
                        <line x1="220" y1="140" x2="490" y2="110" stroke="#3b82f6" stroke-width="1" marker-end="url(#arrowhead)"/>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#4ade80"/>
                            </marker>
                        </defs>
                        
                        <!-- Relationship labels -->
                        <text x="295" y="200" text-anchor="middle" fill="#ffffff" font-size="10">monitors</text>
                        <text x="505" y="200" text-anchor="middle" fill="#ffffff" font-size="10">powers</text>
                        <text x="295" y="300" text-anchor="middle" fill="#ffffff" font-size="10">affects</text>
                        <text x="505" y="300" text-anchor="middle" fill="#ffffff" font-size="10">influences</text>
                    </svg>
                </div>
                <div class="graph-controls">
                    <div class="control-group">
                        <label>Node Size</label>
                        <input type="range" min="10" max="50" value="20" class="slider">
                    </div>
                    <div class="control-group">
                        <label>Edge Thickness</label>
                        <input type="range" min="1" max="5" value="2" class="slider">
                    </div>
                    <div class="control-group">
                        <label>Layout</label>
                        <select class="layout-select">
                            <option value="force">Force-directed</option>
                            <option value="hierarchical">Hierarchical</option>
                            <option value="circular">Circular</option>
                            <option value="grid">Grid</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Entity Explorer -->
        <div class="panel entity-explorer-panel">
            <div class="panel-header">
                <h3>Entity Explorer</h3>
                <div class="panel-controls">
                    <input type="search" placeholder="Search entities..." class="entity-search">
                    <button class="btn-icon" data-action="filter">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
            <div class="entity-explorer-content">
                <div class="entity-categories">
                    <div class="category-item active" data-category="all">
                        <div class="category-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">All Entities</div>
                            <div class="category-count">12,847</div>
                        </div>
                    </div>
                    <div class="category-item" data-category="sensors">
                        <div class="category-icon">
                            <i class="fas fa-satellite-dish"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">Sensors</div>
                            <div class="category-count">1,247</div>
                        </div>
                    </div>
                    <div class="category-item" data-category="treatment">
                        <div class="category-icon">
                            <i class="fas fa-filter"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">Treatment</div>
                            <div class="category-count">342</div>
                        </div>
                    </div>
                    <div class="category-item" data-category="energy">
                        <div class="category-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">Energy</div>
                            <div class="category-count">156</div>
                        </div>
                    </div>
                    <div class="category-item" data-category="climate">
                        <div class="category-icon">
                            <i class="fas fa-cloud-sun"></i>
                        </div>
                        <div class="category-info">
                            <div class="category-name">Climate</div>
                            <div class="category-count">89</div>
                        </div>
                    </div>
                </div>

                <div class="entity-list">
                    <div class="entity-item">
                        <div class="entity-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <div class="entity-info">
                            <div class="entity-name">pH Sensor WQ-001</div>
                            <div class="entity-type">Water Quality Sensor</div>
                            <div class="entity-properties">
                                <span class="property">Location: Treatment Plant A</span>
                                <span class="property">Status: Active</span>
                                <span class="property">Last Reading: 7.2 pH</span>
                            </div>
                        </div>
                        <div class="entity-connections">
                            <span class="connection-count">12 connections</span>
                        </div>
                    </div>

                    <div class="entity-item">
                        <div class="entity-icon">
                            <i class="fas fa-filter"></i>
                        </div>
                        <div class="entity-info">
                            <div class="entity-name">Coagulation Process</div>
                            <div class="entity-type">Treatment Process</div>
                            <div class="entity-properties">
                                <span class="property">Chemical: Aluminum Sulfate</span>
                                <span class="property">Dosing Rate: 12.3 mg/L</span>
                                <span class="property">Efficiency: 94.7%</span>
                            </div>
                        </div>
                        <div class="entity-connections">
                            <span class="connection-count">8 connections</span>
                        </div>
                    </div>

                    <div class="entity-item">
                        <div class="entity-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="entity-info">
                            <div class="entity-name">Pump Station 3</div>
                            <div class="entity-type">Energy Consumer</div>
                            <div class="entity-properties">
                                <span class="property">Power: 847 kW</span>
                                <span class="property">Efficiency: 89.2%</span>
                                <span class="property">Flow Rate: 2,847 L/min</span>
                            </div>
                        </div>
                        <div class="entity-connections">
                            <span class="connection-count">15 connections</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SPARQL Query Interface -->
        <div class="panel sparql-query-panel">
            <div class="panel-header">
                <h3>SPARQL Query Interface</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="run-query">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-icon" data-action="save-query">
                        <i class="fas fa-save"></i>
                    </button>
                    <button class="btn-icon" data-action="load-query">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            </div>
            <div class="sparql-query-content">
                <div class="query-editor">
                    <div class="query-templates">
                        <h4>Query Templates</h4>
                        <div class="template-buttons">
                            <button class="template-btn" data-template="sensors">Find All Sensors</button>
                            <button class="template-btn" data-template="efficiency">Treatment Efficiency</button>
                            <button class="template-btn" data-template="energy">Energy Consumption</button>
                            <button class="template-btn" data-template="relationships">Entity Relationships</button>
                        </div>
                    </div>
                    <div class="query-input">
                        <textarea class="sparql-textarea" placeholder="Enter your SPARQL query here...">
PREFIX wm: <http://watermanagement.org/ontology#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>

SELECT ?sensor ?location ?reading ?timestamp
WHERE {
  ?sensor rdf:type wm:WaterQualitySensor .
  ?sensor wm:locatedAt ?location .
  ?sensor wm:hasReading ?reading .
  ?reading wm:timestamp ?timestamp .
  ?reading wm:value ?value .
  FILTER(?value > 7.0)
}
ORDER BY DESC(?timestamp)
LIMIT 10</textarea>
                    </div>
                </div>

                <div class="query-results">
                    <div class="results-header">
                        <h4>Query Results</h4>
                        <div class="results-info">
                            <span class="result-count">10 results</span>
                            <span class="execution-time">Executed in 0.23s</span>
                        </div>
                    </div>
                    <div class="results-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Sensor</th>
                                    <th>Location</th>
                                    <th>Reading</th>
                                    <th>Timestamp</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>WQ-001</td>
                                    <td>Treatment Plant A</td>
                                    <td>7.2 pH</td>
                                    <td>2024-01-15 14:23:45</td>
                                </tr>
                                <tr>
                                    <td>WQ-003</td>
                                    <td>Distribution Point 7</td>
                                    <td>7.1 pH</td>
                                    <td>2024-01-15 14:22:30</td>
                                </tr>
                                <tr>
                                    <td>WQ-007</td>
                                    <td>Reservoir B</td>
                                    <td>7.3 pH</td>
                                    <td>2024-01-15 14:21:15</td>
                                </tr>
                                <tr>
                                    <td>WQ-012</td>
                                    <td>Treatment Plant B</td>
                                    <td>7.0 pH</td>
                                    <td>2024-01-15 14:20:00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ontology Management -->
        <div class="panel ontology-management-panel">
            <div class="panel-header">
                <h3>Ontology Management</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="import-ontology">
                        <i class="fas fa-upload"></i>
                    </button>
                    <button class="btn-icon" data-action="export-ontology">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" data-action="validate-ontology">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </div>
            <div class="ontology-management-content">
                <div class="ontology-overview">
                    <div class="ontology-stats">
                        <div class="ontology-stat">
                            <div class="stat-label">Classes</div>
                            <div class="stat-value">47</div>
                        </div>
                        <div class="ontology-stat">
                            <div class="stat-label">Properties</div>
                            <div class="stat-value">123</div>
                        </div>
                        <div class="ontology-stat">
                            <div class="stat-label">Individuals</div>
                            <div class="stat-value">12,847</div>
                        </div>
                        <div class="ontology-stat">
                            <div class="stat-label">Axioms</div>
                            <div class="stat-value">2,456</div>
                        </div>
                    </div>
                </div>

                <div class="ontology-hierarchy">
                    <h4>Class Hierarchy</h4>
                    <div class="hierarchy-tree">
                        <div class="tree-node expanded">
                            <i class="fas fa-minus-square"></i>
                            <span class="node-label">WaterManagementSystem</span>
                            <div class="tree-children">
                                <div class="tree-node expanded">
                                    <i class="fas fa-minus-square"></i>
                                    <span class="node-label">Sensor</span>
                                    <div class="tree-children">
                                        <div class="tree-node">
                                            <i class="fas fa-circle"></i>
                                            <span class="node-label">WaterQualitySensor</span>
                                        </div>
                                        <div class="tree-node">
                                            <i class="fas fa-circle"></i>
                                            <span class="node-label">FlowSensor</span>
                                        </div>
                                        <div class="tree-node">
                                            <i class="fas fa-circle"></i>
                                            <span class="node-label">PressureSensor</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node expanded">
                                    <i class="fas fa-minus-square"></i>
                                    <span class="node-label">TreatmentProcess</span>
                                    <div class="tree-children">
                                        <div class="tree-node">
                                            <i class="fas fa-circle"></i>
                                            <span class="node-label">Coagulation</span>
                                        </div>
                                        <div class="tree-node">
                                            <i class="fas fa-circle"></i>
                                            <span class="node-label">Filtration</span>
                                        </div>
                                        <div class="tree-node">
                                            <i class="fas fa-circle"></i>
                                            <span class="node-label">Disinfection</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node collapsed">
                                    <i class="fas fa-plus-square"></i>
                                    <span class="node-label">EnergySystem</span>
                                </div>
                                <div class="tree-node collapsed">
                                    <i class="fas fa-plus-square"></i>
                                    <span class="node-label">ClimateData</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="property-definitions">
                    <h4>Property Definitions</h4>
                    <div class="property-list">
                        <div class="property-item">
                            <div class="property-name">hasReading</div>
                            <div class="property-type">Object Property</div>
                            <div class="property-domain">Sensor</div>
                            <div class="property-range">Reading</div>
                        </div>
                        <div class="property-item">
                            <div class="property-name">locatedAt</div>
                            <div class="property-type">Object Property</div>
                            <div class="property-domain">Equipment</div>
                            <div class="property-range">Location</div>
                        </div>
                        <div class="property-item">
                            <div class="property-name">efficiency</div>
                            <div class="property-type">Data Property</div>
                            <div class="property-domain">Process</div>
                            <div class="property-range">decimal</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Analytics -->
        <div class="panel graph-analytics-panel">
            <div class="panel-header">
                <h3>Graph Analytics</h3>
                <div class="panel-controls">
                    <button class="btn-icon" data-action="run-analytics">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn-icon" data-action="export-results">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="graph-analytics-content">
                <div class="analytics-metrics">
                    <div class="metric-card centrality">
                        <div class="metric-header">
                            <h4>Centrality Analysis</h4>
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="metric-content">
                            <div class="top-entities">
                                <div class="entity-rank">
                                    <span class="rank">1</span>
                                    <span class="entity">Treatment Plant A</span>
                                    <span class="score">0.847</span>
                                </div>
                                <div class="entity-rank">
                                    <span class="rank">2</span>
                                    <span class="entity">Main Distribution</span>
                                    <span class="score">0.723</span>
                                </div>
                                <div class="entity-rank">
                                    <span class="rank">3</span>
                                    <span class="entity">Energy Grid</span>
                                    <span class="score">0.689</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card clustering">
                        <div class="metric-header">
                            <h4>Clustering Coefficient</h4>
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="metric-content">
                            <div class="clustering-value">0.342</div>
                            <div class="clustering-description">
                                Moderate clustering indicates well-connected subsystems
                            </div>
                        </div>
                    </div>

                    <div class="metric-card path-length">
                        <div class="metric-header">
                            <h4>Average Path Length</h4>
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="metric-content">
                            <div class="path-value">3.7</div>
                            <div class="path-description">
                                Short paths enable efficient information flow
                            </div>
                        </div>
                    </div>
                </div>

                <div class="community-detection">
                    <h4>Community Detection</h4>
                    <div class="communities">
                        <div class="community">
                            <div class="community-header">
                                <div class="community-color" style="background: #4ade80;"></div>
                                <span class="community-name">Water Treatment</span>
                                <span class="community-size">247 nodes</span>
                            </div>
                        </div>
                        <div class="community">
                            <div class="community-header">
                                <div class="community-color" style="background: #3b82f6;"></div>
                                <span class="community-name">Sensor Network</span>
                                <span class="community-size">1,247 nodes</span>
                            </div>
                        </div>
                        <div class="community">
                            <div class="community-header">
                                <div class="community-color" style="background: #f59e0b;"></div>
                                <span class="community-name">Energy Systems</span>
                                <span class="community-size">156 nodes</span>
                            </div>
                        </div>
                        <div class="community">
                            <div class="community-header">
                                <div class="community-color" style="background: #ef4444;"></div>
                                <span class="community-name">Climate Data</span>
                                <span class="community-size">89 nodes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
