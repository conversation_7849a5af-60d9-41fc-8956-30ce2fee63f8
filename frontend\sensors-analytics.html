<!-- Sensors Page -->
<div class="page" id="sensors-page">
    <div class="page-header">
        <h1><i class="fas fa-satellite-dish"></i> Sensor Network Management</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> Add Sensor
            </button>
            <button class="btn-secondary">
                <i class="fas fa-sync"></i> Sync All
            </button>
            <button class="btn-secondary">
                <i class="fas fa-tools"></i> Maintenance
            </button>
        </div>
    </div>

    <!-- Sensors Dashboard -->
    <div class="sensors-dashboard">
        <!-- Sensor Network Overview -->
        <div class="sensor-overview">
            <div class="sensor-stats">
                <div class="stat-item">
                    <div class="stat-icon online">
                        <i class="fas fa-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">1,247</div>
                        <div class="stat-label">Active Sensors</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon offline">
                        <i class="fas fa-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">23</div>
                        <div class="stat-label">Offline Sensors</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon maintenance">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">8</div>
                        <div class="stat-label">Maintenance</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon data">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">2.4 TB</div>
                        <div class="stat-label">Data Collected</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sensor Types Grid -->
        <div class="sensor-types-grid">
            <div class="sensor-type-card water-quality">
                <div class="sensor-type-header">
                    <div class="sensor-type-icon">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div class="sensor-type-info">
                        <h3>Water Quality Sensors</h3>
                        <div class="sensor-count">342 Active</div>
                    </div>
                </div>
                <div class="sensor-type-metrics">
                    <div class="metric">
                        <span class="metric-label">pH Sensors</span>
                        <span class="metric-value">89</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Turbidity</span>
                        <span class="metric-value">76</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Chlorine</span>
                        <span class="metric-value">92</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Multi-parameter</span>
                        <span class="metric-value">85</span>
                    </div>
                </div>
                <div class="sensor-type-status">
                    <div class="status-bar">
                        <div class="status-fill online" style="width: 94%"></div>
                        <div class="status-fill offline" style="width: 4%"></div>
                        <div class="status-fill maintenance" style="width: 2%"></div>
                    </div>
                    <div class="status-legend">
                        <span class="legend-item online">94% Online</span>
                        <span class="legend-item offline">4% Offline</span>
                        <span class="legend-item maintenance">2% Maintenance</span>
                    </div>
                </div>
            </div>

            <div class="sensor-type-card flow-pressure">
                <div class="sensor-type-header">
                    <div class="sensor-type-icon">
                        <i class="fas fa-stream"></i>
                    </div>
                    <div class="sensor-type-info">
                        <h3>Flow & Pressure</h3>
                        <div class="sensor-count">198 Active</div>
                    </div>
                </div>
                <div class="sensor-type-metrics">
                    <div class="metric">
                        <span class="metric-label">Flow Meters</span>
                        <span class="metric-value">124</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Pressure</span>
                        <span class="metric-value">74</span>
                    </div>
                </div>
                <div class="sensor-type-status">
                    <div class="status-bar">
                        <div class="status-fill online" style="width: 97%"></div>
                        <div class="status-fill offline" style="width: 2%"></div>
                        <div class="status-fill maintenance" style="width: 1%"></div>
                    </div>
                    <div class="status-legend">
                        <span class="legend-item online">97% Online</span>
                        <span class="legend-item offline">2% Offline</span>
                        <span class="legend-item maintenance">1% Maintenance</span>
                    </div>
                </div>
            </div>

            <div class="sensor-type-card energy">
                <div class="sensor-type-header">
                    <div class="sensor-type-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="sensor-type-info">
                        <h3>Energy Meters</h3>
                        <div class="sensor-count">156 Active</div>
                    </div>
                </div>
                <div class="sensor-type-metrics">
                    <div class="metric">
                        <span class="metric-label">Smart Meters</span>
                        <span class="metric-value">98</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Power Quality</span>
                        <span class="metric-value">58</span>
                    </div>
                </div>
                <div class="sensor-type-status">
                    <div class="status-bar">
                        <div class="status-fill online" style="width: 92%"></div>
                        <div class="status-fill offline" style="width: 5%"></div>
                        <div class="status-fill maintenance" style="width: 3%"></div>
                    </div>
                    <div class="status-legend">
                        <span class="legend-item online">92% Online</span>
                        <span class="legend-item offline">5% Offline</span>
                        <span class="legend-item maintenance">3% Maintenance</span>
                    </div>
                </div>
            </div>

            <div class="sensor-type-card environmental">
                <div class="sensor-type-header">
                    <div class="sensor-type-icon">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <div class="sensor-type-info">
                        <h3>Environmental</h3>
                        <div class="sensor-count">89 Active</div>
                    </div>
                </div>
                <div class="sensor-type-metrics">
                    <div class="metric">
                        <span class="metric-label">Weather</span>
                        <span class="metric-value">34</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Air Quality</span>
                        <span class="metric-value">28</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Soil</span>
                        <span class="metric-value">27</span>
                    </div>
                </div>
                <div class="sensor-type-status">
                    <div class="status-bar">
                        <div class="status-fill online" style="width: 89%"></div>
                        <div class="status-fill offline" style="width: 7%"></div>
                        <div class="status-fill maintenance" style="width: 4%"></div>
                    </div>
                    <div class="status-legend">
                        <span class="legend-item online">89% Online</span>
                        <span class="legend-item offline">7% Offline</span>
                        <span class="legend-item maintenance">4% Maintenance</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sensor Network Map -->
        <div class="panel sensor-map-panel">
            <div class="panel-header">
                <h3>Sensor Network Map</h3>
                <div class="panel-controls">
                    <div class="map-filters">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="water">Water</button>
                        <button class="filter-btn" data-filter="energy">Energy</button>
                        <button class="filter-btn" data-filter="environmental">Environmental</button>
                    </div>
                    <button class="btn-icon" data-action="fullscreen">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            <div class="sensor-network-map" id="sensorNetworkMap"></div>
        </div>

        <!-- Sensor Data Streams -->
        <div class="panel data-streams-panel">
            <div class="panel-header">
                <h3>Real-time Data Streams</h3>
                <div class="stream-controls">
                    <button class="btn-icon" data-action="pause">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="btn-icon" data-action="refresh">
                        <i class="fas fa-sync"></i>
                    </button>
                </div>
            </div>
            <div class="data-streams">
                <div class="stream-item">
                    <div class="stream-sensor">
                        <div class="sensor-id">WQ-001</div>
                        <div class="sensor-location">Treatment Plant A</div>
                    </div>
                    <div class="stream-data">
                        <div class="data-point">
                            <span class="data-label">pH</span>
                            <span class="data-value">7.2</span>
                        </div>
                        <div class="data-point">
                            <span class="data-label">Turbidity</span>
                            <span class="data-value">1.5 NTU</span>
                        </div>
                        <div class="data-point">
                            <span class="data-label">Chlorine</span>
                            <span class="data-value">1.0 mg/L</span>
                        </div>
                    </div>
                    <div class="stream-status online">
                        <i class="fas fa-circle"></i>
                        <span>Online</span>
                    </div>
                </div>

                <div class="stream-item">
                    <div class="stream-sensor">
                        <div class="sensor-id">EN-045</div>
                        <div class="sensor-location">Pump Station 3</div>
                    </div>
                    <div class="stream-data">
                        <div class="data-point">
                            <span class="data-label">Power</span>
                            <span class="data-value">847 kW</span>
                        </div>
                        <div class="data-point">
                            <span class="data-label">Voltage</span>
                            <span class="data-value">415 V</span>
                        </div>
                        <div class="data-point">
                            <span class="data-label">Frequency</span>
                            <span class="data-value">50.1 Hz</span>
                        </div>
                    </div>
                    <div class="stream-status online">
                        <i class="fas fa-circle"></i>
                        <span>Online</span>
                    </div>
                </div>

                <div class="stream-item">
                    <div class="stream-sensor">
                        <div class="sensor-id">FL-023</div>
                        <div class="sensor-location">Distribution Line 7</div>
                    </div>
                    <div class="stream-data">
                        <div class="data-point">
                            <span class="data-label">Flow Rate</span>
                            <span class="data-value">234 L/s</span>
                        </div>
                        <div class="data-point">
                            <span class="data-label">Pressure</span>
                            <span class="data-value">4.2 bar</span>
                        </div>
                    </div>
                    <div class="stream-status offline">
                        <i class="fas fa-circle"></i>
                        <span>Offline</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Page -->
<div class="page" id="analytics-page">
    <div class="page-header">
        <h1><i class="fas fa-chart-line"></i> Advanced Analytics</h1>
        <div class="page-actions">
            <button class="btn-primary">
                <i class="fas fa-plus"></i> New Report
            </button>
            <button class="btn-secondary">
                <i class="fas fa-download"></i> Export Data
            </button>
            <button class="btn-secondary">
                <i class="fas fa-cog"></i> Configure
            </button>
        </div>
    </div>

    <!-- Analytics Dashboard -->
    <div class="analytics-dashboard">
        <!-- KPI Overview -->
        <div class="kpi-overview">
            <div class="kpi-card efficiency">
                <div class="kpi-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">94.7%</div>
                    <div class="kpi-label">Overall Efficiency</div>
                    <div class="kpi-change positive">+2.3% vs last month</div>
                </div>
                <div class="kpi-sparkline">
                    <canvas id="efficiencySparkline"></canvas>
                </div>
            </div>

            <div class="kpi-card cost-savings">
                <div class="kpi-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">$47,832</div>
                    <div class="kpi-label">Monthly Savings</div>
                    <div class="kpi-change positive">+12.4% vs last month</div>
                </div>
                <div class="kpi-sparkline">
                    <canvas id="savingsSparkline"></canvas>
                </div>
            </div>

            <div class="kpi-card carbon-reduction">
                <div class="kpi-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">23.4 tons</div>
                    <div class="kpi-label">CO₂ Reduced</div>
                    <div class="kpi-change positive">+8.7% vs last month</div>
                </div>
                <div class="kpi-sparkline">
                    <canvas id="carbonSparkline"></canvas>
                </div>
            </div>

            <div class="kpi-card water-saved">
                <div class="kpi-icon">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="kpi-content">
                    <div class="kpi-value">1.2M L</div>
                    <div class="kpi-label">Water Saved</div>
                    <div class="kpi-change positive">+5.2% vs last month</div>
                </div>
                <div class="kpi-sparkline">
                    <canvas id="waterSparkline"></canvas>
                </div>
            </div>
        </div>

        <!-- Advanced Charts -->
        <div class="advanced-charts">
            <div class="panel correlation-panel">
                <div class="panel-header">
                    <h3>Multi-Variable Correlation Analysis</h3>
                    <div class="panel-controls">
                        <select class="variable-select">
                            <option value="energy-water">Energy vs Water Usage</option>
                            <option value="weather-demand">Weather vs Demand</option>
                            <option value="quality-treatment">Quality vs Treatment</option>
                        </select>
                    </div>
                </div>
                <div class="correlation-chart">
                    <canvas id="correlationChart"></canvas>
                </div>
            </div>

            <div class="panel predictive-panel">
                <div class="panel-header">
                    <h3>Predictive Analytics</h3>
                    <div class="panel-controls">
                        <select class="prediction-type">
                            <option value="demand">Demand Forecasting</option>
                            <option value="maintenance">Maintenance Prediction</option>
                            <option value="quality">Quality Trends</option>
                        </select>
                    </div>
                </div>
                <div class="predictive-chart">
                    <canvas id="predictiveChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Machine Learning Insights -->
        <div class="panel ml-insights-panel">
            <div class="panel-header">
                <h3>Machine Learning Insights</h3>
                <div class="ml-model-status">
                    <span class="model-status active">
                        <i class="fas fa-brain"></i>
                        <span>Neural Network: Active</span>
                    </span>
                    <span class="model-status active">
                        <i class="fas fa-dna"></i>
                        <span>Genetic Algorithm: Optimizing</span>
                    </span>
                </div>
            </div>
            <div class="ml-insights">
                <div class="insight-item">
                    <div class="insight-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">Optimization Opportunity Detected</div>
                        <div class="insight-description">
                            Pump Station 3 shows 15% efficiency improvement potential during off-peak hours
                        </div>
                        <div class="insight-impact">
                            <span class="impact-label">Potential Savings:</span>
                            <span class="impact-value">$2,847/month</span>
                        </div>
                    </div>
                    <div class="insight-actions">
                        <button class="btn-insight">Apply</button>
                        <button class="btn-insight">Details</button>
                    </div>
                </div>

                <div class="insight-item">
                    <div class="insight-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">Anomaly Pattern Identified</div>
                        <div class="insight-description">
                            Unusual water quality fluctuations in Sector 7 may indicate sensor drift
                        </div>
                        <div class="insight-impact">
                            <span class="impact-label">Confidence:</span>
                            <span class="impact-value">87%</span>
                        </div>
                    </div>
                    <div class="insight-actions">
                        <button class="btn-insight">Investigate</button>
                        <button class="btn-insight">Schedule</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
